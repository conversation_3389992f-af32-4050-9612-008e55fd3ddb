// Package publishers ...
package publishers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/loan_app_lifecycle_event"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkawriter"
)

// LoanApplicationLifecyclePublisher ...
type LoanApplicationLifecyclePublisher struct {
	KafkaWriter kafkawriter.Client `inject:"writer.loanApplicationLifecycleStream"`
}

// Publish ...
func (l *LoanApplicationLifecyclePublisher) Publish(ctx context.Context, i interface{}) error {
	loanAppLifecycleDTO := i.(*dto.LoanAppLifecycleDTO)
	data := loan_app_lifecycle_event.LoanAppLifecycleEvent{
		OnboardingApplicationID: loanAppLifecycleDTO.OnboardingApplicationID,
		Applicants:              convertApplicants(loanAppLifecycleDTO.Applicants),
		AccountID:               loanAppLifecycleDTO.AccountID, // NOTE: use application object this will be deprecated
		IPAddress:               loanAppLifecycleDTO.IPAddress,
		DeviceID:                loanAppLifecycleDTO.DeviceID,
		Status:                  loanAppLifecycleDTO.Status,                // NOTE: use application object this will be deprecated
		StatusReason:            loanAppLifecycleDTO.StatusReason,          // NOTE: use application object this will be deprecated
		ProductCode:             loanAppLifecycleDTO.ProductCode,           // NOTE: use application object this will be deprecated
		ProductVariantCode:      loanAppLifecycleDTO.ProductVariantCode,    // NOTE: use application object this will be deprecated
		ApplicationID:           loanAppLifecycleDTO.ApplicationID,         // NOTE: use application object this will be deprecated
		Errors:                  convertErrors(loanAppLifecycleDTO.Errors), // NOTE: use application object this will be deprecated
		CreatedAt:               loanAppLifecycleDTO.CreatedAt,             // NOTE: use application object this will be deprecated
		UpdatedAt:               loanAppLifecycleDTO.UpdatedAt,             // NOTE: use application object this will be deprecated
		Application:             convertApplicationData(loanAppLifecycleDTO.ApplicationData),
		Channel:                 loanAppLifecycleDTO.Channel,
	}
	if loanAppLifecycleDTO.DeviceInfo != nil {
		data.DeviceInfo = convertDeviceInfo(loanAppLifecycleDTO)
	}
	if err := l.KafkaWriter.Save(&data); err != nil {
		slog.FromContext(ctx).Warn(constants.LoanApplicationLifecycleKafkaPublisherLogTag, fmt.Sprintf("error in publishing data: %s, err: %s", utils.ToJSON(data), err.Error()), utils.GetTraceID(ctx))
		return err
	}

	slog.FromContext(ctx).Info(constants.LoanApplicationLifecycleKafkaPublisherLogTag, fmt.Sprintf("data published: %s", utils.ToJSON(data)), utils.GetTraceID(ctx))
	return nil
}

func convertDeviceInfo(loanAppLifecycleDTO *dto.LoanAppLifecycleDTO) *loan_app_lifecycle_event.DeviceInfo {
	return &loan_app_lifecycle_event.DeviceInfo{
		DeviceInfo:      loanAppLifecycleDTO.DeviceInfo.DeviceInfo,
		GpsInfo:         convertGPSInfo(loanAppLifecycleDTO.DeviceInfo.GpsInfo),
		OperatingSystem: loanAppLifecycleDTO.DeviceInfo.OperatingSystem,
		OsVersion:       loanAppLifecycleDTO.DeviceInfo.OsVersion,
		DeviceMade:      loanAppLifecycleDTO.DeviceInfo.DeviceMade,
		DeviceModel:     loanAppLifecycleDTO.DeviceInfo.DeviceModel,
		DeviceID:        convertDeviceID(loanAppLifecycleDTO.DeviceInfo.DeviceID),
		IpAddress:       loanAppLifecycleDTO.DeviceInfo.IpAddress,
		AppVersion:      loanAppLifecycleDTO.DeviceInfo.AppVersion,
	}
}

func convertApplicationData(applicationDataDTO []*dto.ApplicationProductData) []*loan_app_lifecycle_event.ApplicationData {
	var applicationData []*loan_app_lifecycle_event.ApplicationData
	for _, value := range applicationDataDTO {
		if value != nil {
			data := &loan_app_lifecycle_event.ApplicationData{
				AccountID:          value.AccountID,
				Status:             value.Status,
				StatusReason:       value.StatusReason,
				ProductCode:        value.ProductCode,
				ProductVariantCode: value.ProductVariantCode,
				ApplicationID:      value.ApplicationID,
				Errors:             convertErrors(value.Errors),
				CreatedAt:          value.CreatedAt,
				UpdatedAt:          value.UpdatedAt,
			}
			if len(value.BizApplicationMetaData) > 0 {
				data.BizApplicationMetaData = value.BizApplicationMetaData
			}
			applicationData = append(applicationData, data)
		}
	}
	return applicationData
}

func convertApplicants(applicantsDTO []*dto.Applicant) []*loan_app_lifecycle_event.Applicant {
	var applicants []*loan_app_lifecycle_event.Applicant
	for _, value := range applicantsDTO {
		if value != nil {
			applicant := &loan_app_lifecycle_event.Applicant{
				ApplicantType:                value.ApplicantType,
				ApplicantID:                  value.ApplicantID,
				ContactNumber:                value.ContactNumber,
				EmailAddress:                 value.EmailAddress,
				EmergencyContactMobileNumber: value.EmergencyContactMobileNumber,
				MotherMaidenName:             value.MotherMaidenName,
				SafeID:                       value.SafeID,
			}
			applicants = append(applicants, applicant)
		}
	}
	return applicants
}

func convertErrors(errors []*dto.LoanAppError) []*loan_app_lifecycle_event.LoanAppError {
	var errs []*loan_app_lifecycle_event.LoanAppError
	for _, value := range errors {
		if value != nil {
			err := &loan_app_lifecycle_event.LoanAppError{
				ErrorCode:        value.ErrorCode,
				ErrorDescription: value.ErrorDescription,
			}
			errs = append(errs, err)
		}
	}
	return errs
}

func convertGPSInfo(gpsInfoDTO *dto.GPSInfo) *loan_app_lifecycle_event.GPSInfo {
	if gpsInfoDTO == nil {
		return nil
	}
	return &loan_app_lifecycle_event.GPSInfo{
		Latitude:  gpsInfoDTO.Latitude,
		Longitude: gpsInfoDTO.Longitude,
		Accuracy:  gpsInfoDTO.Accuracy,
	}
}

func convertDeviceID(deviceIDDTO *dto.DeviceID) *loan_app_lifecycle_event.DeviceID {
	if deviceIDDTO == nil {
		return nil
	}
	return &loan_app_lifecycle_event.DeviceID{
		Mobile:    deviceIDDTO.Mobile,
		AppsFlyer: deviceIDDTO.AppsFlyer,
	}
}
