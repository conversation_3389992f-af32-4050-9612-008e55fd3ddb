// Package consumers ...
package consumers

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dakota/lending/loan-app/handlers"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/schemas/streams"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"
	"gitlab.myteksi.net/gophers/go/commons/util/tags"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

const (
	streamDefaultStopTimeout = 8 * time.Second
	topicTag                 = "topic"
	logTag                   = "stream.consumers"
	dtoNameTag               = "dtoName"
)

const (
	applicationStatusTransitionStream streams.StreamID = "applicationStatusTransitionStream"
	creditBureauEnquiryStream         streams.StreamID = "creditBureauEnquiryStream"
	creditCardAccountCreation         streams.StreamID = "creditCardAccountCreationStream"
	locAccountCreationEventStream     streams.StreamID = "locAccountCreationEventStream"
	incomeDerivationEventStream       streams.StreamID = "incomeDerivationEventStream"
	lendingNameScreeningEventStream   streams.StreamID = "lendingNameScreeningEventStream"
	ecddVerdictEventStream            streams.StreamID = "ecddVerdictEventStream"
	finexusUpdateEventStream          streams.StreamID = "finexusUpdateEventStream"
)

var (
	wg = gconcurrent.NewExecutionGroup()
	// StatsDClient ...
	StatsDClient statsd.Client
)

// Init initializes stream consumers.
func Init(conf *config.AppConfig, service *handlers.LoanAppService, stats statsd.Client) {
	ctx := context.Background()
	StatsDClient = stats
	if conf.FeatureFlags.EnableRetryableStream {
		startConsumeRetryableStream(ctx, conf, stats)
	} else {
		if conf.ApplicationStatusTransitionKafkaConfig.Enable {
			slog.FromContext(ctx).Info(logTag, "starting consumer for application status transition stream")
			startApplicationStatusTransitionStream(ctx, service, conf)
		}
		if conf.CreditBureauEnquiryKafkaConfig.Enable {
			slog.FromContext(ctx).Info(logTag, "starting consumer for credit bureau enquiry stream")
			startCreditBureauEnquiryStream(ctx, conf, stats)
		}
		if conf.CreditCardAccountCreationKafkaConfig.Enable {
			slog.FromContext(ctx).Info(logTag, "starting consumer for account service stream")
			startCreditCardAccountCreationStream(ctx, conf, stats)
		}
		if conf.LOCAccountCreationEventKafkaConfig.Enable {
			slog.FromContext(ctx).Info(logTag, "starting consumer for loc account creation event stream")
			startLOCAccountCreationEventConsumer(ctx, conf, stats)
		}
		if conf.IncomeDerivationEventKafkaConfig.Enable {
			slog.FromContext(ctx).Info(logTag, "starting consumer for income derivation event stream")
			startIncomeDerivationEventStream(ctx, conf, stats)
		}
		if conf.LendingNameScreeningEventKafkaConfig.Enable {
			slog.FromContext(ctx).Info(logTag, "starting consumer for lending name screening event stream")
			startLendingNameScreeningEventStream(ctx, conf, stats)
		}
		if conf.EcddVerdictEventKafkaConfig.Enable {
			slog.FromContext(ctx).Info(logTag, "starting consumer for ecdd verdict event stream")
			startEcddVerdictEventStream(ctx, conf, stats)
		}
		if conf.FinexusUpdateKafkaConfig.Enable {
			slog.FromContext(ctx).Info(logTag, "starting consumer for update finexus stream")
			startFinexusUpdateStream(ctx, service, conf, stats)
		}
		if conf.LendingAccountCreationKafkaConfig.Enable {
			slog.FromContext(ctx).Info(logTag, "starting to consume Asset-offline Back fill Asset Account Stream")
			startLendingAccountCreationEventStream(ctx, conf, stats)
		}
	}
}

// Stop ...
func Stop(conf *config.AppConfig) {
	if conf.FeatureFlags.EnableRetryableStream {
		stopConsumeRetryableStream()
	} else {
		if conf.ApplicationStatusTransitionKafkaConfig.Enable {
			stopConsumer(applicationStatusTransitionConsumer)
		}
		if conf.CreditBureauEnquiryKafkaConfig.Enable {
			stopConsumer(creditBureauEnquiryConsumer)
		}
		if conf.CreditCardAccountCreationKafkaConfig.Enable {
			stopConsumer(creditCardAccountCreationConsumer)
		}
		if conf.LOCAccountCreationEventKafkaConfig.Enable {
			stopConsumer(locAccountCreationEventConsumer)
		}
		if conf.FinexusUpdateKafkaConfig.Enable {
			stopConsumer(finexusUpdateConsumer)
		}

		if conf.IncomeDerivationEventKafkaConfig.Enable {
			stopConsumer(incomeDerivationEventConsumer)
		}
		if conf.LendingNameScreeningEventKafkaConfig.Enable {
			stopConsumer(lendingNameScreeningEventConsumer)
		}
		if conf.EcddVerdictEventKafkaConfig.Enable {
			stopConsumer(ecddVerdictEventConsumer)
		}
		if conf.LendingAccountCreationKafkaConfig.Enable {
			stopConsumer(lendingAccountCreationConsumer)
		}
	}

	timeoutErr := wg.WaitForDone(streamDefaultStopTimeout)
	if timeoutErr != nil {
		slog.FromContext(context.Background()).Fatal(logTag, "timeout on stopping streams, it could lead to a data loss")
	}
}

// nolint
func commonTags(topic, dtoName string) []tags.Tag {
	tagList := []tags.Tag{
		tags.T(topicTag, topic),
		tags.T(dtoNameTag, dtoName),
	}
	return tagList
}

// stopConsumer shutdown the kafkareader Client
func stopConsumer(consumer kafkareader.Client) {
	err := consumer.Shutdown()
	if err != nil {
		slog.FromContext(context.Background()).Error(logTag, fmt.Sprintf("error in stopping kafka consumer client: %v", consumer))
	}
}
