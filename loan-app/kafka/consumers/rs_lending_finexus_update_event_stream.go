// nolint:dupl
package consumers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/updatefinexus"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	finexusUpdateSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/lending_finexus_update"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka"
)

// RsLendingFinexusUpdateEventStream ...
type RsLendingFinexusUpdateEventStream struct {
	AppConfig             *config.AppConfig      `inject:"config"`
	StatsD                statsd.Client          `inject:"statsD"`
	UpdateFinexusWorkflow updatefinexus.Workflow `inject:"workflow.updateFinexus"`
}

// GetEventSchema ...
func (r RsLendingFinexusUpdateEventStream) GetEventSchema() kafka.Entity {
	return &finexusUpdateSchema.LendingFinexusUpdate{}
}

// Handle ...
func (r RsLendingFinexusUpdateEventStream) Handle(ctx context.Context, message kafka.Entity) error {
	data, ok := message.(*finexusUpdateSchema.LendingFinexusUpdate)
	if !ok {
		slog.FromContext(ctx).Fatal(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Wrong entity in reader, event=[%+v]", data),
			commonTags(r.AppConfig.ApplicationStatusTransitionKafkaConfig.KafkaConfig.Stream, r.AppConfig.ApplicationStatusTransitionKafkaConfig.KafkaConfig.DtoName)...)
		return nil
	}

	finexusUpdateStreamImpl := FinexusUpdateStreamImpl{AppConfig: r.AppConfig, StatsD: r.StatsD, UpdateFinexusWorkflow: r.UpdateFinexusWorkflow}
	return finexusUpdateStreamImpl.handleFinexusUpdate(ctx, data)
}
