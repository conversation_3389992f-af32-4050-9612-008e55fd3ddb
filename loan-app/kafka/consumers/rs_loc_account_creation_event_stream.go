package consumers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/loc_account_creation_event"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka"
)

// RsLocAccountCreationEventStream ...
type RsLocAccountCreationEventStream struct {
	AppConfig *config.AppConfig
	StatsD    statsd.Client
}

// GetEventSchema ...
func (r RsLocAccountCreationEventStream) GetEventSchema() kafka.Entity {
	return &loc_account_creation_event.LOCAccountCreationEvent{}
}

// Handle ...
func (r RsLocAccountCreationEventStream) Handle(ctx context.Context, message kafka.Entity) error {
	data, ok := message.(*loc_account_creation_event.LOCAccountCreationEvent)
	if !ok {
		slog.FromContext(ctx).Fatal(constants.LOCAccountCreationEventConsumerLogTag, fmt.Sprintf("Wrong entity in reader, event=[%+v]", data),
			commonTags(r.AppConfig.LOCAccountCreationEventKafkaConfig.Stream, r.AppConfig.LOCAccountCreationEventKafkaConfig.DtoName)...)
		return nil
	}

	locAccountCreationEventStreamImpl := LocAccountCreationEventStreamImpl{KafkaConfig: *r.AppConfig.LOCAccountCreationEventKafkaConfig, StatsD: r.StatsD}
	return locAccountCreationEventStreamImpl.handleLOCAccountCreationEvent(ctx, data)
}
