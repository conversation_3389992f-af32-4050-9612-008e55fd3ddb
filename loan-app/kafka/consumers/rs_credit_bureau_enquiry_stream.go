package consumers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	creditBureauEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/credit_bureau_event"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka"
)

// RsCreditBureauEnquiryStream ...
type RsCreditBureauEnquiryStream struct {
	AppConfig *config.AppConfig
	StatsD    statsd.Client
}

// GetEventSchema ...
func (r RsCreditBureauEnquiryStream) GetEventSchema() kafka.Entity {
	return &creditBureauEvent.CreditBureauEvent{}
}

// Handle ...
func (r RsCreditBureauEnquiryStream) Handle(ctx context.Context, message kafka.Entity) error {
	data, ok := message.(*creditBureauEvent.CreditBureauEvent)
	if !ok {
		slog.FromContext(ctx).Fatal(constants.CreditBureauEnquiryKafkaConsumerLog, fmt.Sprintf("Wrong entity in reader, event=[%+v]", data),
			commonTags(r.AppConfig.CreditBureauEnquiryKafkaConfig.KafkaConfig.Stream, r.AppConfig.CreditBureauEnquiryKafkaConfig.KafkaConfig.DtoName)...)
		return nil
	}
	creditBureauEnquiryStreamImp := CreditBureauEnquiryStreamImpl(r)

	return creditBureauEnquiryStreamImp.handleCreditBureauEnquiryEvent(ctx, data)
}
