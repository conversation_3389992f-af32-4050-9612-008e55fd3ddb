package consumers

import (
	"testing"

	"github.com/stretchr/testify/assert"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"
	creditBureauEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/credit_bureau_event"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/credit_card_account_creation_event"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/loc_account_creation_event"
)

func Test_GetEventSchema_RsCbSgAdapterEvent(t *testing.T) {
	consumer := RsApplicationStatusTransitionStream{}
	schema := consumer.GetEventSchema()

	assert.IsType(t, &applicationStatusTransistionSchema.ApplicationStatusTransition{}, schema)
}

func Test_GetEventSchema_RsCreditBureauEnquiryStream(t *testing.T) {
	consumer := RsCreditBureauEnquiryStream{}
	schema := consumer.GetEventSchema()

	assert.IsType(t, &creditBureauEvent.CreditBureauEvent{}, schema)
}

func Test_GetEventSchema_RsCreditCardAccountCreationEventStream(t *testing.T) {
	consumer := RsCreditCardAccountCreationEventStream{}
	schema := consumer.GetEventSchema()

	assert.IsType(t, &credit_card_account_creation_event.CreditCardAccountCreationEvent{}, schema)
}

func Test_GetEventSchema_RsLocAccountCreationEventStream(t *testing.T) {
	consumer := RsLocAccountCreationEventStream{}
	schema := consumer.GetEventSchema()

	assert.IsType(t, &loc_account_creation_event.LOCAccountCreationEvent{}, schema)
}
