package consumers

import (
	"context"
	"fmt"
	"runtime/debug"
	"time"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/external/mlscoringservice/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/bundledapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexicardapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/mapper"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/schemas/streams"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/credit_card_account_creation_event"

	"gitlab.myteksi.net/dakota/lending/common/stats"
	commonUtils "gitlab.myteksi.net/dakota/lending/common/utils"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	tags "gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

var (
	creditCardAccountCreationConsumer kafkareader.Client
)

// CreditCardAccountCreationStreamImpl account service stream consumer impl
type CreditCardAccountCreationStreamImpl struct {
	AppConfig *config.AppConfig
	StatsD    statsd.Client
}

func startCreditCardAccountCreationStream(ctx context.Context, appConfig *config.AppConfig, statsD statsd.Client) {
	defer func() {
		if err := recover(); err != nil {
			slog.FromContext(ctx).Warn(constants.CreditCardAccountCreationKafkaConsumerLogTag, fmt.Sprintf("Panic occurred. error: %s, stacktrace %s", err, string(debug.Stack())))
		} else {
			slog.FromContext(ctx).Info(constants.CreditCardAccountCreationKafkaConsumerLogTag, "Successfully started Credit Card Account Creation Kafka consumer")
		}
	}()
	reader, err := streams.NewStaticReader(ctx, creditCardAccountCreation, *appConfig.CreditCardAccountCreationKafkaConfig.KafkaConfig, &credit_card_account_creation_event.CreditCardAccountCreationEvent{})
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", appConfig.CreditCardAccountCreationKafkaConfig, err))
	}
	creditCardAccountCreationConsumer = reader
	creditCardAccountCreationStreamImpl := CreditCardAccountCreationStreamImpl{
		AppConfig: appConfig,
		StatsD:    statsD,
	}
	creditCardAccountCreationStreamImpl.registerCreditCardAccountCreationEventStream(ctx, reader.GetDataAckChan())
}

func (a *CreditCardAccountCreationStreamImpl) registerCreditCardAccountCreationEventStream(ctx context.Context, ackChan <-chan *kafkareader.AckEntity) {
	wg.Go(constants.CreditCardAccountCreationKafkaConsumerLogTag, func() {
		a.consumeCreditCardAccountCreationEventStream(ctx, ackChan)
	})
}

// nolint:dupl
func (a *CreditCardAccountCreationStreamImpl) consumeCreditCardAccountCreationEventStream(ctx context.Context, ackChan <-chan *kafkareader.AckEntity) {
	for event := range ackChan {
		data, ok := event.Event.(*credit_card_account_creation_event.CreditCardAccountCreationEvent)
		if !ok {
			slog.FromContext(ctx).Fatal(constants.CreditCardAccountCreationKafkaConsumerLogTag, fmt.Sprintf("Wrong entity in reader, event=[%+v]", event.Event),
				commonTags(a.AppConfig.CreditCardAccountCreationKafkaConfig.KafkaConfig.Stream, a.AppConfig.CreditCardAccountCreationKafkaConfig.KafkaConfig.DtoName)...)
			continue
		}

		ctx = slog.AddTagsToContext(ctx, tags.TraceID(data.ReferenceID))

		err := commonUtils.RetryFunction(ctx, func() error {
			return a.handleCreditCardAccountCreationEvents(ctx, data)
		}, constants.CreditCardAccountCreationKafkaConsumerLogTag, a.AppConfig.CreditCardAccountCreationKafkaConfig.MaxRetryCount, a.AppConfig.CreditCardAccountCreationKafkaConfig.DelayInMilliSeconds)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.CreditCardAccountCreationKafkaConsumerLogTag, fmt.Sprintf("Handling stream event failed, %s", err.Error()),
				commonTags(a.AppConfig.CreditCardAccountCreationKafkaConfig.KafkaConfig.Stream, a.AppConfig.CreditCardAccountCreationKafkaConfig.KafkaConfig.DtoName)...)
		}
		if err = event.Ack(); err != nil {
			slog.FromContext(ctx).Warn(constants.CreditCardAccountCreationKafkaConsumerLogTag, fmt.Sprintf("Failed to ack message: %s", err.Error()),
				commonTags(a.AppConfig.CreditCardAccountCreationKafkaConfig.KafkaConfig.Stream, a.AppConfig.CreditCardAccountCreationKafkaConfig.KafkaConfig.DtoName)...)
		}
	}
}

func (a *CreditCardAccountCreationStreamImpl) handleCreditCardAccountCreationEvents(ctx context.Context, cardAccountCreationEvent *credit_card_account_creation_event.CreditCardAccountCreationEvent) error {
	// publishing custom metrics for consumer
	streamIDTag := fmt.Sprintf("stream:%s", constants.CreditCardAccountCreationKafkaConsumerLogTag)
	a.StatsD.Duration(stats.StreamConsumersContextTag, stats.StreamConsumeLatencyKey, cardAccountCreationEvent.GetStreamInfo().StreamTime, streamIDTag)
	defer a.StatsD.Duration(stats.StreamConsumersContextTag, stats.StreamProcessLatencyKey, time.Now(), streamIDTag)
	// start processing message
	eventDTO := mapper.MapCreditCardAccountCreationStreamToDTO(cardAccountCreationEvent)
	conditions := []data.Condition{
		data.EqualTo("OnboardingApplicationID", eventDTO.ReferenceID),
	}
	applications, err := storage.ApplicationDao.Find(ctx, conditions...)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CreditCardAccountCreationKafkaConsumerLogTag, "Error fetching application from db with application id from cardAccountCreationEvent")
		return err
	}
	if len(applications) == 1 && applications[0].ProductCode == string(api.ProductType_FLEXI_CREDIT_CARD) {
		if workflowErr := flexicardapplication.ConsumeCreditCardAccountCreationStream(ctx, eventDTO); workflowErr != nil {
			return workflowErr
		}
	} else if len(applications) > 1 {
		if workflowErr := bundledapplication.ConsumeCreditCardAccountCreationStream(ctx, eventDTO); workflowErr != nil {
			return workflowErr
		}
	}
	return nil
}
