// nolint:dupl
package consumers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	ecddVerdictEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/ecdd_verdict_event"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka"
)

// RsEcddVerdictEventStream ...
// nolint:dupl
type RsEcddVerdictEventStream struct {
	AppConfig *config.AppConfig `inject:"config"`
	StatsD    statsd.Client     `inject:"statsD"`
}

// GetEventSchema ...
// nolint:dupl
func (r RsEcddVerdictEventStream) GetEventSchema() kafka.Entity {
	return &ecddVerdictEvent.EcddVerdictEvent{}
}

// Handle ...
// nolint:dupl
func (r RsEcddVerdictEventStream) Handle(ctx context.Context, message kafka.Entity) error {
	data, ok := message.(*ecddVerdictEvent.EcddVerdictEvent)
	if !ok {
		slog.FromContext(ctx).Fatal(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Wrong entity in reader, event=[%+v]", data),
			commonTags(r.AppConfig.ApplicationStatusTransitionKafkaConfig.KafkaConfig.Stream, r.AppConfig.ApplicationStatusTransitionKafkaConfig.KafkaConfig.DtoName)...)
		return nil
	}

	ecddVerdictEventStream := EcddVerdictEventStreamImpl{AppConfig: r.AppConfig, StatsD: r.StatsD}
	return ecddVerdictEventStream.handleEcddVerdictEvent(ctx, data)
}
