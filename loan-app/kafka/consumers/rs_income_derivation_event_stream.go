// nolint:dupl
package consumers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	incomeDerivationEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/income_derivation_event"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka"
)

// RsIncomeDerivationEventStream ...
// nolint:dupl
type RsIncomeDerivationEventStream struct {
	AppConfig *config.AppConfig `inject:"config"`
	StatsD    statsd.Client     `inject:"statsD"`
}

// GetEventSchema ...
// nolint:dupl
func (r RsIncomeDerivationEventStream) GetEventSchema() kafka.Entity {
	return &incomeDerivationEvent.IncomeDerivationEvent{}
}

// Handle ...
// nolint:dupl
func (r RsIncomeDerivationEventStream) Handle(ctx context.Context, message kafka.Entity) error {
	data, ok := message.(*incomeDerivationEvent.IncomeDerivationEvent)
	if !ok {
		slog.FromContext(ctx).Fatal(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Wrong entity in reader, event=[%+v]", data),
			commonTags(r.AppConfig.ApplicationStatusTransitionKafkaConfig.KafkaConfig.Stream, r.AppConfig.ApplicationStatusTransitionKafkaConfig.KafkaConfig.DtoName)...)
		return nil
	}

	incomeDerivationEventStream := IncomeDerivationEventStreamImpl{AppConfig: r.AppConfig, StatsD: r.StatsD}
	return incomeDerivationEventStream.handleIncomeDerivationEvent(ctx, data)
}
