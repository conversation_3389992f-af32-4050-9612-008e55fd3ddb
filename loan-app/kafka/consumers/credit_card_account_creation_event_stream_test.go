package consumers

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.com/gx-regional/dakota/lending/loan-app/external/mlscoringservice/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/resources"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/credit_card_account_creation_event"

	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

func Test_registerCreditCardAccountCreationEventStream(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		appCfg := &config.AppConfig{}
		impl := CreditCardAccountCreationStreamImpl{
			AppConfig: appCfg,
			StatsD:    statsd.NewNoop(),
		}
		impl.registerCreditCardAccountCreationEventStream(context.Background(), getInputChannel(
			&kafkareader.AckEntity{
				Event: &credit_card_account_creation_event.CreditCardAccountCreationEvent{
					ReferenceID:             uuid.NewString(),
					ApplicationID:           uuid.NewString(),
					AccountID:               uuid.NewString(),
					ProductID:               uuid.NewString(),
					ProductVariantCode:      string(api.SubProductType_DEFAULT_FLEXI_CREDIT_CARD),
					CifNumber:               "test-cif",
					Status:                  "PENDING_ACTIVE",
					StatusReasonDescription: "PENDING_ACTIVE",
					OpeningTimestamp:        &time.Time{},
					CreatedBy:               "Test User",
				}}))
		err := wg.WaitForDone(time.Second * 4)
		require.Nil(t, err)
	})
	mock.AssertExpectationsForObjects(t)
}

func Test_handleCreditCardAccountCreationEvents(t *testing.T) {
	scenarios := []struct {
		desc               string
		data               *credit_card_account_creation_event.CreditCardAccountCreationEvent
		expectedError      error
		applicationData    []*storage.Application
		applicationDBError error
	}{
		{
			desc:          "event",
			data:          &credit_card_account_creation_event.CreditCardAccountCreationEvent{},
			expectedError: errors.New("workflow not registered"),
			applicationData: []*storage.Application{
				resources.ConstructApplication(uuid.NewString(), uuid.NewString(), "PROCESSING",
					"CARD_APPLICATION_CREATED",
					"FLEXI_CREDIT_CARD",
					"DEFAULT_FLEXI_CREDIT_CARD"),
			},
		},
	}
	for _, tt := range scenarios {
		s := tt
		t.Run(s.desc, func(t *testing.T) {
			impl := CreditCardAccountCreationStreamImpl{
				AppConfig: &config.AppConfig{},
				StatsD:    statsd.NewNoop(),
			}
			mockApplicationDao := &storage.MockIApplicationDAO{}
			mockApplicationDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.applicationData, s.applicationDBError)
			storage.ApplicationDao = mockApplicationDao
			consumerErr := impl.handleCreditCardAccountCreationEvents(context.Background(), s.data)
			if s.expectedError == nil {
				assert.Nil(t, consumerErr)
			} else {
				assert.Equal(t, s.expectedError.Error(), consumerErr.Error())
			}
		})
	}
}
