package consumers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/external/appian"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/consumerlogic/applicationstatustransition"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/snd/streamsdk/kafka"
)

// RsApplicationStatusTransitionStream ...
type RsApplicationStatusTransitionStream struct {
	AppConfig    *config.AppConfig `inject:"config"`
	AppianClient appian.Appian     `inject:"client.appian"`
}

// GetEventSchema ...
func (r RsApplicationStatusTransitionStream) GetEventSchema() kafka.Entity {
	return &applicationStatusTransistionSchema.ApplicationStatusTransition{}
}

// Handle ...
func (r RsApplicationStatusTransitionStream) Handle(ctx context.Context, message kafka.Entity) error {
	data, ok := message.(*applicationStatusTransistionSchema.ApplicationStatusTransition)
	if !ok {
		slog.FromContext(ctx).Fatal(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Wrong entity in reader, event=[%+v]", data),
			commonTags(r.AppConfig.ApplicationStatusTransitionKafkaConfig.KafkaConfig.Stream, r.AppConfig.ApplicationStatusTransitionKafkaConfig.KafkaConfig.DtoName)...)
		return nil
	}

	applicationStatusTransitionImpl := applicationstatustransition.ApplicationStatusTransitionImpl{Config: r.AppConfig, AppianClient: r.AppianClient}
	return applicationStatusTransitionImpl.HandleApplicationStatusTransitionEvent(ctx, data)
}
