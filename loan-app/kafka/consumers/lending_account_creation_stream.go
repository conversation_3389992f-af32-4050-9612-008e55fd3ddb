package consumers

import (
	"context"
	"fmt"
	"runtime/debug"
	"time"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/bundledapplication"
	gxsFlexiTermLoanApplication "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/gxs"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/schemas/streams"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/lending_account_creation_event"

	commonUtils "gitlab.myteksi.net/dakota/lending/common/utils"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	tags "gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

var (
	lendingAccountCreationConsumer   kafkareader.Client
	lendingAccountCreationTopic      string
	lendingAccountCreationStreamImpl LendingAccountCreationStreamImpl
)

// LendingAccountCreationStreamImpl lending account creation enquiry stream consumer impl
type LendingAccountCreationStreamImpl struct {
	AppConfig *config.AppConfig
	StatsD    statsd.Client
}

func startLendingAccountCreationEventStream(ctx context.Context, appConfig *config.AppConfig, statsD statsd.Client) {
	defer func() {
		if err := recover(); err != nil {
			slog.FromContext(ctx).Warn(constants.LendingAccountCreationEventKafkaConsumerLog, fmt.Sprintf("Panic occurred. error: %s, stacktrace %s", err, string(debug.Stack())))
		} else {
			slog.FromContext(ctx).Info(constants.LendingAccountCreationEventKafkaConsumerLog, "successfully started LendingAccountCreationEvent consumer")
		}
	}()

	reader, err := streams.NewStaticReader(ctx, "LendingAccountCreationStream", *appConfig.LendingAccountCreationKafkaConfig.KafkaConfig, &lending_account_creation_event.LendingAccountCreationEvent{})
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", appConfig.LendingAccountCreationKafkaConfig, err))
	}

	lendingAccountCreationConsumer = reader
	lendingAccountCreationTopic = appConfig.LendingAccountCreationKafkaConfig.KafkaConfig.Stream
	lendingAccountCreationStreamImpl = LendingAccountCreationStreamImpl{
		AppConfig: appConfig,
		StatsD:    statsD,
	}
	lendingAccountCreationStreamImpl.registerLendingAccountCreationEvent(ctx, lendingAccountCreationConsumer.GetDataAckChan())
}

func (c *LendingAccountCreationStreamImpl) registerLendingAccountCreationEvent(ctx context.Context, ackChan <-chan *kafkareader.AckEntity) {
	wg.Go(constants.LendingAccountCreationEventKafkaConsumerLog, func() {
		c.consumeLendingAccountCreationEvent(ctx, ackChan)
	})
}

// nolint:dupl
func (c *LendingAccountCreationStreamImpl) consumeLendingAccountCreationEvent(ctx context.Context, ackChan <-chan *kafkareader.AckEntity) {
	for event := range ackChan {
		data, ok := event.Event.(*lending_account_creation_event.LendingAccountCreationEvent)

		if !ok {
			slog.FromContext(ctx).Fatal(constants.LendingAccountCreationEventKafkaConsumerLog, fmt.Sprintf("Wrong entity in reader, event=[%+v]", event.Event),
				commonTags(lendingAccountCreationTopic, c.AppConfig.LendingAccountCreationKafkaConfig.KafkaConfig.DtoName)...)
			continue
		}

		ctx = slog.AddTagsToContext(ctx, tags.TraceID(data.ReferenceID))

		err := commonUtils.RetryFunction(ctx, func() error {
			return c.handleLendingAccountCreationEventLogic(ctx, data)
		}, constants.LendingAccountCreationEventKafkaConsumerLog, c.AppConfig.LendingAccountCreationKafkaConfig.MaxRetryCount, c.AppConfig.LendingAccountCreationKafkaConfig.DelayInMilliSeconds)

		if err != nil {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Handling stream event failed, %s", err.Error()),
				commonTags(lendingAccountCreationTopic, c.AppConfig.LendingAccountCreationKafkaConfig.KafkaConfig.DtoName)...)
		}
		if err = event.Ack(); err != nil {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Failed to ack message: %s", err.Error()),
				commonTags(lendingAccountCreationTopic, c.AppConfig.LendingAccountCreationKafkaConfig.KafkaConfig.DtoName)...)
		}
	}
}

// handleLendingAccountCreationEventLogic handle lending account creation enquiry response event
func (c *LendingAccountCreationStreamImpl) handleLendingAccountCreationEventLogic(ctx context.Context, data *lending_account_creation_event.LendingAccountCreationEvent) error {
	if data == nil {
		slog.FromContext(ctx).Warn(constants.LendingAccountCreationEventKafkaConsumerLog, "empty lending account creation event stream msg")
		return nil
	}
	slog.FromContext(ctx).Info(constants.LendingAccountCreationEventKafkaConsumerLog, fmt.Sprintf("lending account creation event stream msg recieved, referenceID: %s", data.ReferenceID))
	// publishing custom metrics for consumer
	streamIDTag := fmt.Sprintf("stream:%s", constants.LendingAccountCreationEventKafkaConsumerLog)
	c.StatsD.Duration(logTag, "stream_consume_latency", data.GetStreamInfo().StreamTime, streamIDTag)
	defer c.StatsD.Duration(logTag, "stream_process_latency", time.Now(), streamIDTag)
	// start processing message
	eventDTO := mapAccountCreationStreamToDTO(data)
	var workflowErr error
	if c.AppConfig.FeatureFlags.EnableOverarchingLimit {
		workflowErr = gxsFlexiTermLoanApplication.ConsumeLendingAccountCreationData(ctx, eventDTO)
		if workflowErr != nil {
			return workflowErr
		}

		workflowErr = bundledapplication.ConsumeLendingAccountCreationData(ctx, eventDTO)
		if workflowErr != nil {
			return workflowErr
		}
	}
	return nil
}

func mapAccountCreationStreamToDTO(data *lending_account_creation_event.LendingAccountCreationEvent) *dto.LendingAccountCreationDTO {
	return &dto.LendingAccountCreationDTO{
		ReferenceID:             data.ReferenceID,
		ApplicationID:           data.ApplicationID,
		ParentAccountID:         data.ParentAccountID,
		AccountID:               data.AccountID,
		ProductID:               data.ProductID,
		ProductVariantCode:      data.ProductVariantCode,
		ProductVersionID:        data.ProductVersionID,
		CifNumber:               data.CifNumber,
		Status:                  data.Status,
		StatusReasonDescription: data.StatusReasonDescription,
		OpeningTimestamp:        data.OpeningTimestamp,
		InstanceParams:          data.InstanceParams,
		CreatedBy:               data.CreatedBy,
	}
}
