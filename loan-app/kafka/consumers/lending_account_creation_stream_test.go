package consumers

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/lending_account_creation_event"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/streaminfo"

	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

func Test_registerLendingAccountCreationEvent(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		appCfg := &config.AppConfig{}
		impl := LendingAccountCreationStreamImpl{AppConfig: appCfg, StatsD: statsd.NewNoop()}
		impl.registerLendingAccountCreationEvent(context.Background(), getInputChannel(
			&kafkareader.AckEntity{Event: &lending_account_creation_event.LendingAccountCreationEvent{
				ReferenceID:   uuid.NewString(),
				ApplicationID: uuid.NewString(),
				Status:        "ACCEPTED",
			}}))
		err := wg.WaitForDone(time.Second * 4)
		require.Nil(t, err)
	})
	mock.AssertExpectationsForObjects(t)
}

func Test_handleLendingAccountCreationEvent(t *testing.T) {
	scenarios := []struct {
		desc          string
		data          *lending_account_creation_event.LendingAccountCreationEvent
		expectedError error
	}{
		{
			desc:          "event",
			data:          &lending_account_creation_event.LendingAccountCreationEvent{},
			expectedError: errors.New("workflow not registered"),
		},
	}
	for _, tt := range scenarios {
		s := tt
		t.Run(s.desc, func(t *testing.T) {
			impl := LendingAccountCreationStreamImpl{
				AppConfig: &config.AppConfig{
					FeatureFlags: config.FeatureFlags{
						EnableOverarchingLimit: true,
					},
				},
				StatsD: statsd.NewNoop(),
			}
			consumerErr := impl.handleLendingAccountCreationEventLogic(context.Background(), s.data)
			if s.expectedError == nil {
				assert.Nil(t, consumerErr)
			} else {
				assert.Equal(t, s.expectedError.Error(), consumerErr.Error())
			}
		})
	}
}

func Test_mapAccountCreationStreamToDTO(t *testing.T) {
	tests := []struct {
		name     string
		input    *lending_account_creation_event.LendingAccountCreationEvent
		expected *dto.LendingAccountCreationDTO
	}{
		{
			name: "all fields populated",
			input: &lending_account_creation_event.LendingAccountCreationEvent{
				StreamInfo:              streaminfo.StreamInfo{},
				ReferenceID:             "ref123",
				ApplicationID:           "app456",
				ParentAccountID:         "parent789",
				AccountID:               "acc000",
				ProductID:               "prod321",
				ProductVariantCode:      "VAR1",
				ProductVersionID:        "v2",
				CifNumber:               "CIF001",
				Status:                  "ACTIVE",
				StatusReasonDescription: "Opened successfully",
				CreatedBy:               "userX",
			},
			expected: &dto.LendingAccountCreationDTO{
				ReferenceID:             "ref123",
				ApplicationID:           "app456",
				ParentAccountID:         "parent789",
				AccountID:               "acc000",
				ProductID:               "prod321",
				ProductVariantCode:      "VAR1",
				ProductVersionID:        "v2",
				CifNumber:               "CIF001",
				Status:                  "ACTIVE",
				StatusReasonDescription: "Opened successfully",
				CreatedBy:               "userX",
			},
		},
		{
			name:  "all fields empty",
			input: &lending_account_creation_event.LendingAccountCreationEvent{},
			expected: &dto.LendingAccountCreationDTO{
				ReferenceID:             "",
				ApplicationID:           "",
				ParentAccountID:         "",
				AccountID:               "",
				ProductID:               "",
				ProductVariantCode:      "",
				ProductVersionID:        "",
				CifNumber:               "",
				Status:                  "",
				StatusReasonDescription: "",
				CreatedBy:               "",
			},
		},
		{
			name: "some fields missing",
			input: &lending_account_creation_event.LendingAccountCreationEvent{
				ApplicationID: "app999",
				AccountID:     "acc111",
				Status:        "PENDING",
			},
			expected: &dto.LendingAccountCreationDTO{
				ReferenceID:             "",
				ApplicationID:           "app999",
				ParentAccountID:         "",
				AccountID:               "acc111",
				ProductID:               "",
				ProductVariantCode:      "",
				ProductVersionID:        "",
				CifNumber:               "",
				Status:                  "PENDING",
				StatusReasonDescription: "",
				CreatedBy:               "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := mapAccountCreationStreamToDTO(tt.input)
			assert.Equal(t, tt.expected, actual)
		})
	}
}
