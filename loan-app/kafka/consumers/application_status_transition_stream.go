package consumers

import (
	"context"
	"fmt"
	"runtime/debug"
	"time"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/handlers"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/consumerlogic/applicationstatustransition"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	"gitlab.com/gx-regional/dakota/schemas/streams"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	"gitlab.myteksi.net/dakota/lending/common/stats"
	commonUtils "gitlab.myteksi.net/dakota/lending/common/utils"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

const (
	applicationStatusTransitionDTO = "ApplicationStatusTransition"
)

var (
	applicationStatusTransitionConsumer kafkareader.Client
	applicationStatusTransitionTopic    string
)

func startApplicationStatusTransitionStream(ctx context.Context, service *handlers.LoanAppService, appConfig *config.AppConfig) {
	defer func() {
		if err := recover(); err != nil {
			slog.FromContext(ctx).Warn(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Panic occurred. error: %s, stacktrace %s", err, string(debug.Stack())))
		} else {
			slog.FromContext(ctx).Info(constants.ApplicationStatusTransitionKafkaConsumerLogTag, "successfully started ApplicationStatusTransition consumer")
		}
	}()
	ApplicationStatusTransitionKafkaConfig := appConfig.ApplicationStatusTransitionKafkaConfig.KafkaConfig
	reader, err := streams.NewStaticReader(ctx, applicationStatusTransitionStream,
		*ApplicationStatusTransitionKafkaConfig, &applicationStatusTransistionSchema.ApplicationStatusTransition{})
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", ApplicationStatusTransitionKafkaConfig, err))
	}

	applicationStatusTransitionConsumer = reader
	applicationStatusTransitionTopic = ApplicationStatusTransitionKafkaConfig.Stream
	applicationStatusTransition := &applicationstatustransition.ApplicationStatusTransitionImpl{
		Config:       appConfig,
		AppianClient: service.AppianClient,
	}
	registerApplicationStatusTransitionEvent(constants.ApplicationStatusTransitionKafkaConsumerLogTag, applicationStatusTransition, *appConfig.ApplicationStatusTransitionKafkaConfig, applicationStatusTransitionConsumer.GetDataAckChan())
}

var registerApplicationStatusTransitionEvent = func(tag string, applicationStatusTransition *applicationstatustransition.ApplicationStatusTransitionImpl, conf config.KafkaConfig, ch <-chan *kafkareader.AckEntity) {
	wg.Go(tag, func() {
		consumeApplicationStatusTransitionEvent(context.Background(), applicationStatusTransition, conf, ch)
	})
}

// consumeApplicationStatusTransitionEvent receives event from stream and pass it to processing methods
func consumeApplicationStatusTransitionEvent(ctx context.Context, applicationStatusTransition *applicationstatustransition.ApplicationStatusTransitionImpl, conf config.KafkaConfig, ch <-chan *kafkareader.AckEntity) {
	for event := range ch {
		data, ok := event.Event.(*applicationStatusTransistionSchema.ApplicationStatusTransition)
		if !ok {
			slog.FromContext(ctx).Error(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Wrong entity in reader, event=[%+v]", event.Event),
				commonTags(applicationStatusTransitionTopic, applicationStatusTransitionDTO)...)
			continue
		}
		slog.FromContext(ctx).Info(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Application Transition Event received: %s", utils.ToJSON(data)))
		// publishing custom metrics for consumer
		streamIDTag := fmt.Sprintf("stream:%s", constants.ApplicationStatusTransitionKafkaConsumerLogTag)
		StatsDClient.Duration(stats.StreamConsumersContextTag, stats.StreamConsumeLatencyKey, data.GetStreamInfo().StreamTime, streamIDTag)
		err := commonUtils.RetryFunction(ctx, func() error {
			return applicationStatusTransition.HandleApplicationStatusTransitionEvent(ctx, data)
		}, logTag, conf.MaxRetryCount, conf.DelayInMilliSeconds)
		if err != nil {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Handling stream event failed, %s", err.Error()), commonTags(applicationStatusTransitionTopic, applicationStatusTransitionDTO)...)
		}
		if err = event.Ack(); err != nil {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Failed to ack message: %s", err.Error()), commonTags(applicationStatusTransitionTopic, applicationStatusTransitionDTO)...)
		}
		StatsDClient.Duration(stats.StreamConsumersContextTag, stats.StreamProcessLatencyKey, time.Now(), streamIDTag)
	}
}
