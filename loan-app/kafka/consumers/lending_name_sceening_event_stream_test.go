package consumers

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/lending_name_screening"

	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

func Test_registerLendingNameScreening(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		appCfg := &config.AppConfig{}
		impl := LendingNameScreeningEventStreamImpl{AppConfig: appCfg, StatsD: statsd.NewNoop()}
		impl.registerLendingNameScreeningEvent(getInputChannel(
			&kafkareader.AckEntity{Event: &lending_name_screening.LendingNameScreening{
				ReferenceID: uuid.NewString(),
				Metadata: &lending_name_screening.LendingResultMetadata{
					ReferenceID:   uuid.NewString(),
					ApplicationID: "123",
					WorkflowRunID: "123",
					CifNumber:     "123",
				},
			}}))
		err := wg.WaitForDone(time.Second * 4)
		require.Nil(t, err)
	})
	mock.AssertExpectationsForObjects(t)
}

func Test_handleLendingNameScreeningEvent(t *testing.T) {
	scenarios := []struct {
		desc          string
		data          *lending_name_screening.LendingNameScreening
		expectedError error
	}{
		{
			desc: "event",
			data: &lending_name_screening.LendingNameScreening{
				ReferenceID: uuid.NewString(),
				Metadata: &lending_name_screening.LendingResultMetadata{
					ReferenceID:   uuid.NewString(),
					ApplicationID: "123",
					WorkflowRunID: "123",
					CifNumber:     "123",
				},
			},
			expectedError: errors.New("workflow not registered"),
		},
	}
	for _, tt := range scenarios {
		s := tt
		t.Run(s.desc, func(t *testing.T) {
			impl := LendingNameScreeningEventStreamImpl{
				AppConfig: &config.AppConfig{},
				StatsD:    statsd.NewNoop(),
			}
			consumerErr := impl.handleLendingNameScreeningEvent(context.Background(), s.data)
			if s.expectedError == nil {
				assert.Nil(t, consumerErr)
			} else {
				assert.Equal(t, s.expectedError, consumerErr)
			}
		})
	}
}
