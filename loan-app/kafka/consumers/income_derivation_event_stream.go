package consumers

import (
	"context"
	"encoding/json"
	"fmt"
	"runtime/debug"
	"strings"
	"time"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	bizFlexiCreditLoanApplication "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/bizflexicreditapplication"
	gxbFlexiTermLoanApplication "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/gxb"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/schemas/streams"
	incomeDerivationEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/income_derivation_event"

	commonConst "gitlab.myteksi.net/dakota/lending/common/constant"
	"gitlab.myteksi.net/dakota/lending/common/stats"
	commonUtils "gitlab.myteksi.net/dakota/lending/common/utils"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	tags "gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

var (
	incomeDerivationEventConsumer kafkareader.Client
)

const (
	errMissingField = "error: %s is missing" // Constant for error message format
)

// IncomeDerivationEventStreamImpl income derivation enquiry stream consumer impl
type IncomeDerivationEventStreamImpl struct {
	AppConfig *config.AppConfig
	StatsD    statsd.Client
}

func startIncomeDerivationEventStream(ctx context.Context, appConfig *config.AppConfig, statsD statsd.Client) {
	defer func() {
		if err := recover(); err != nil {
			slog.FromContext(ctx).Warn(constants.IncomeDerivationEventKafkaConsumerLog, fmt.Sprintf("Panic occurred. error: %s, stacktrace %s", err, string(debug.Stack())))
		} else {
			slog.FromContext(ctx).Info(constants.IncomeDerivationEventKafkaConsumerLog, "successfully started IncomeDerivationEventKafka consumer")
		}
	}()

	reader, err := streams.NewStaticReader(ctx, incomeDerivationEventStream, *appConfig.IncomeDerivationEventKafkaConfig.KafkaConfig, &incomeDerivationEvent.IncomeDerivationEvent{})
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", appConfig.IncomeDerivationEventKafkaConfig, err))
	}

	incomeDerivationEventConsumer = reader
	incomeDerivationEventStreamImpl := IncomeDerivationEventStreamImpl{
		AppConfig: appConfig,
		StatsD:    statsD,
	}
	incomeDerivationEventStreamImpl.registerIncomeDerivationEvent(incomeDerivationEventConsumer.GetDataAckChan())
}

func (c *IncomeDerivationEventStreamImpl) registerIncomeDerivationEvent(ackChan <-chan *kafkareader.AckEntity) {
	wg.Go(constants.IncomeDerivationEventKafkaConsumerLog, func() {
		c.consumeIncomeDerivationEvent(context.Background(), ackChan)
	})
}

// nolint:dupl
func (c *IncomeDerivationEventStreamImpl) consumeIncomeDerivationEvent(ctx context.Context, ackChan <-chan *kafkareader.AckEntity) {
	for event := range ackChan {
		data, ok := event.Event.(*incomeDerivationEvent.IncomeDerivationEvent)

		if !ok {
			slog.FromContext(ctx).Fatal(constants.IncomeDerivationEventKafkaConsumerLog, fmt.Sprintf("Wrong entity in reader, event=[%+v]", event.Event),
				commonTags(c.AppConfig.IncomeDerivationEventKafkaConfig.KafkaConfig.Stream, c.AppConfig.IncomeDerivationEventKafkaConfig.KafkaConfig.DtoName)...)
			continue
		}

		ctx = slog.AddTagsToContext(ctx, tags.TraceID(data.ReferenceID))

		err := commonUtils.RetryFunction(ctx, func() error {
			return c.handleIncomeDerivationEvent(ctx, data)
		}, constants.IncomeDerivationEventKafkaConsumerLog, c.AppConfig.IncomeDerivationEventKafkaConfig.MaxRetryCount, c.AppConfig.IncomeDerivationEventKafkaConfig.DelayInMilliSeconds)

		if err != nil {
			slog.FromContext(ctx).Warn(constants.IncomeDerivationEventKafkaConsumerLog, fmt.Sprintf("Handling stream event failed, %s", err.Error()),
				commonTags(c.AppConfig.IncomeDerivationEventKafkaConfig.KafkaConfig.Stream, c.AppConfig.IncomeDerivationEventKafkaConfig.KafkaConfig.DtoName)...)
		}
		if err = event.Ack(); err != nil {
			slog.FromContext(ctx).Warn(constants.IncomeDerivationEventKafkaConsumerLog, fmt.Sprintf("Failed to ack message: %s", err.Error()),
				commonTags(c.AppConfig.IncomeDerivationEventKafkaConfig.KafkaConfig.Stream, c.AppConfig.IncomeDerivationEventKafkaConfig.KafkaConfig.DtoName)...)
		}
	}
}

// handleIncomeDerivationEvent handle income derivation enquiry response event
func (c *IncomeDerivationEventStreamImpl) handleIncomeDerivationEvent(ctx context.Context, data *incomeDerivationEvent.IncomeDerivationEvent) error {
	// publishing custom metrics for consumer
	streamIDTag := fmt.Sprintf("stream:%s", constants.IncomeDerivationEventKafkaConsumerLog)
	c.StatsD.Duration(stats.StreamConsumersContextTag, stats.StreamConsumeLatencyKey, data.GetStreamInfo().StreamTime, streamIDTag)
	defer c.StatsD.Duration(stats.StreamConsumersContextTag, stats.StreamProcessLatencyKey, time.Now(), streamIDTag)
	err := validateIncomeDerivationStream(ctx, data)
	if err != nil {
		return err
	}
	// start processing message
	eventDTO, mapErr := mapIncomeDerivationStreamToDTO(ctx, data)
	if mapErr != nil {
		return mapErr
	}
	var workflowErr error
	switch data.ProductVariantCode {
	case commonConst.BizLineOfCreditProductVariantCode:
		workflowErr = bizFlexiCreditLoanApplication.HandleIncomeDerivationEvent(ctx, eventDTO)
	case commonConst.FlexiLineOfCreditProductVariantCode:
		workflowErr = gxbFlexiTermLoanApplication.HandleIncomeDerivationEvent(ctx, eventDTO)
	default:
	}
	if workflowErr != nil {
		return workflowErr
	}
	return nil
}

// mapIncomeDerivationStreamToDTO maps income derivation event stream object to dto
func mapIncomeDerivationStreamToDTO(ctx context.Context, data *incomeDerivationEvent.IncomeDerivationEvent) (*dto.IncomeDerivationStreamMessage, error) {
	incomeDerivationMetaData := dto.IncomeDerivationMetadata{}
	err := json.Unmarshal(data.Metadata, &incomeDerivationMetaData)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Error while unmarshal income derivation perfious data due to %s", err.Error()))
		return nil, err
	}
	streamDataMap := &dto.IncomeDerivationStreamMessage{
		ApplicationID:        data.ApplicationID,
		ReferenceID:          data.ReferenceID,
		DocumentType:         data.DocumentType,
		Status:               data.Status,
		StatusReason:         data.StatusReason,
		Metadata:             &incomeDerivationMetaData,
		IncomeAmount:         data.IncomeAmount,
		IncomeDocumentSource: data.IncomeDocumentSource,
	}
	if data.UserDetails != nil {
		streamDataMap.UserDetails = &dto.UserDetails{
			Name: data.UserDetails.Name,
		}
	}

	if len(data.BankStatementReport) != 0 {
		streamDataMap.BankStatementReport = data.BankStatementReport
	}
	return streamDataMap, nil
}

func validateIncomeDerivationStream(ctx context.Context, eventMsg *incomeDerivationEvent.IncomeDerivationEvent) error {
	requiredFields := []struct {
		name  string
		value string
	}{
		{"ApplicationID", eventMsg.ApplicationID},
		{"DocumentType", string(eventMsg.DocumentType)},
		{"Status", string(eventMsg.Status)},
		{"StatusReason", string(eventMsg.StatusReason)},
		{"IncomeDocumentSource", eventMsg.IncomeDocumentSource},
	}
	if eventMsg.Metadata == nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("%s is missing in the event", "Metadata"))
		return fmt.Errorf(errMissingField, "Metadata")
	}
	if eventMsg.UserDetails == nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("%s is missing in the event", "UserDetails"))
		return fmt.Errorf(errMissingField, "UserDetails")
	}
	for _, field := range requiredFields {
		if strings.TrimSpace(field.value) == "" {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("%s is missing", field.name))
			return fmt.Errorf(errMissingField, field.name)
		}
	}

	return nil
}
