package consumers

import (
	"context"
	"fmt"

	rsStream "gitlab.com/gx-regional/dakota/common/retryable-stream"
	rsHandler "gitlab.com/gx-regional/dakota/common/retryable-stream/handler"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"

	"gitlab.myteksi.net/dakota/servus/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	sndconfig "gitlab.myteksi.net/snd/streamsdk/kafka/config"
)

var (
	retryableStream *rsStream.RetryableStream
)

func startConsumeRetryableStream(ctx context.Context, conf *config.AppConfig, stats statsd.Client) {
	stream, err := rsStream.NewRetryableStream(ctx, conf.SQSConfig, stats)
	if err != nil {
		panic(fmt.Sprintf("failed to create new RetryableStream, sqsConfig=[%+v], err=[%+v]", conf.SQSConfig, err))
	}

	if conf.ApplicationStatusTransitionKafkaConfig != nil && conf.ApplicationStatusTransitionKafkaConfig.Enable {
		slog.FromContext(ctx).Info(logTag, "starting consumer for ApplicationStatusTransition event stream")
		registerConsumerHandler(ctx, stream, *conf.ApplicationStatusTransitionKafkaConfig, &RsApplicationStatusTransitionStream{})
	}
	if conf.CreditBureauEnquiryKafkaConfig != nil && conf.CreditBureauEnquiryKafkaConfig.Enable {
		slog.FromContext(ctx).Info(logTag, "starting consumer for CreditBureauEnquiry event stream")
		registerConsumerHandler(ctx, stream, *conf.CreditBureauEnquiryKafkaConfig, &RsCreditBureauEnquiryStream{})
	}
	if conf.CreditCardAccountCreationKafkaConfig != nil && conf.CreditCardAccountCreationKafkaConfig.Enable {
		slog.FromContext(ctx).Info(logTag, "starting consumer for CreditCardAccountCreation event stream")
		registerConsumerHandler(ctx, stream, *conf.CreditCardAccountCreationKafkaConfig, &RsCreditCardAccountCreationEventStream{})
	}
	if conf.LOCAccountCreationEventKafkaConfig != nil && conf.LOCAccountCreationEventKafkaConfig.Enable {
		slog.FromContext(ctx).Info(logTag, "starting consumer for LOCAccountCreation event stream")
		registerConsumerHandler(ctx, stream, *conf.LOCAccountCreationEventKafkaConfig, &RsLocAccountCreationEventStream{})
	}

	if conf.IncomeDerivationEventKafkaConfig != nil && conf.IncomeDerivationEventKafkaConfig.Enable {
		slog.FromContext(ctx).Info(logTag, "starting consumer for IncomeDerivation event stream")
		registerConsumerHandler(ctx, stream, *conf.IncomeDerivationEventKafkaConfig, &RsIncomeDerivationEventStream{})
	}
	if conf.LendingNameScreeningEventKafkaConfig != nil && conf.LendingNameScreeningEventKafkaConfig.Enable {
		slog.FromContext(ctx).Info(logTag, "starting consumer for LendingNameScreening event stream")
		registerConsumerHandler(ctx, stream, *conf.LendingNameScreeningEventKafkaConfig, &RsLendingNameScreeningStream{})
	}
	if conf.EcddVerdictEventKafkaConfig != nil && conf.EcddVerdictEventKafkaConfig.Enable {
		slog.FromContext(ctx).Info(logTag, "starting consumer for EcddVerdictEvent event stream")
		registerConsumerHandler(ctx, stream, *conf.EcddVerdictEventKafkaConfig, &RsEcddVerdictEventStream{})
	}
	if conf.FinexusUpdateKafkaConfig != nil && conf.FinexusUpdateKafkaConfig.Enable {
		slog.FromContext(ctx).Info(logTag, "starting consumer for FinexusUpdate event stream")
		registerConsumerHandler(ctx, stream, *conf.FinexusUpdateKafkaConfig, &RsLendingFinexusUpdateEventStream{})
	}

	retryableStream = stream
	stream.StartConsuming(ctx)
}

func registerConsumerHandler(ctx context.Context, stream *rsStream.RetryableStream, kafkaConfig config.KafkaConfig, handler rsHandler.Handler) {
	sndKafkaConfig := convertConfig(&kafkaConfig)
	err := stream.RegisterKafkaConsumer(ctx, sndKafkaConfig, handler)
	if err != nil {
		panic(fmt.Sprintf("failed to create new consumer, config=[%+v], err=[%+v]", sndKafkaConfig, err))
	}
}

func stopConsumeRetryableStream() {
	ctx := context.Background()
	if retryableStream == nil {
		return
	}
	err := retryableStream.Stop(ctx)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "error shutting down retryable stream", slog.Error(err))
	}
}

// convertConfig...
func convertConfig(conf *config.KafkaConfig) sndconfig.KafkaConfig {
	return sndconfig.KafkaConfig{
		Brokers:         conf.Brokers,
		ClientID:        conf.ClientID,
		ClusterType:     conf.ClusterType,
		EnableTLS:       conf.EnableTLS,
		Stream:          conf.Stream,
		OffsetType:      conf.OffsetType,
		ConsumerVersion: sndconfig.ConsumerV2,
		PackageName:     "pb",
		DtoName:         conf.DtoName,
		ConsumerGroupID: conf.ConsumerGroupID,
	}
}
