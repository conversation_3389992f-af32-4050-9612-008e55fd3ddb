package consumers

import (
	"context"
	"fmt"
	"runtime/debug"
	"time"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/bizflexicreditapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/bundledapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexicardapplication"
	gxbFlexiTermLoanApplication "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/gxb"
	gxsFlexiTermLoanApplication "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/gxs"
	"gitlab.com/gx-regional/dakota/lending/loan-app/mapper"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/schemas/streams"
	creditBureauEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/credit_bureau_event"

	"gitlab.myteksi.net/dakota/lending/common/countries"
	"gitlab.myteksi.net/dakota/lending/common/stats"
	commonUtils "gitlab.myteksi.net/dakota/lending/common/utils"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	tags "gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

var (
	creditBureauEnquiryConsumer kafkareader.Client
)

// CreditBureauEnquiryStreamImpl credit bureau enquiry stream consumer impl
type CreditBureauEnquiryStreamImpl struct {
	AppConfig *config.AppConfig
	StatsD    statsd.Client
}

func startCreditBureauEnquiryStream(ctx context.Context, appConfig *config.AppConfig, statsD statsd.Client) {
	defer func() {
		if err := recover(); err != nil {
			slog.FromContext(ctx).Warn(constants.CreditBureauEnquiryKafkaConsumerLog, fmt.Sprintf("Panic occurred. error: %s, stacktrace %s", err, string(debug.Stack())))
		} else {
			slog.FromContext(ctx).Info(constants.CreditBureauEnquiryKafkaConsumerLog, "successfully started CreditBureauEnquiryKafka consumer")
		}
	}()

	reader, err := streams.NewStaticReader(ctx, creditBureauEnquiryStream, *appConfig.CreditBureauEnquiryKafkaConfig.KafkaConfig, &creditBureauEvent.CreditBureauEvent{})
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", appConfig.CreditBureauEnquiryKafkaConfig, err))
	}

	creditBureauEnquiryConsumer = reader
	creditBureauEnquiryStreamImpl := CreditBureauEnquiryStreamImpl{
		AppConfig: appConfig,
		StatsD:    statsD,
	}
	creditBureauEnquiryStreamImpl.registerCreditBureauEnquiryEvent(creditBureauEnquiryConsumer.GetDataAckChan())
}

func (c *CreditBureauEnquiryStreamImpl) registerCreditBureauEnquiryEvent(ackChan <-chan *kafkareader.AckEntity) {
	wg.Go(constants.CreditBureauEnquiryKafkaConsumerLog, func() {
		c.consumeCreditBureauEnquiryEvent(context.Background(), ackChan)
	})
}

// nolint:dupl
func (c *CreditBureauEnquiryStreamImpl) consumeCreditBureauEnquiryEvent(ctx context.Context, ackChan <-chan *kafkareader.AckEntity) {
	for event := range ackChan {
		data, ok := event.Event.(*creditBureauEvent.CreditBureauEvent)

		if !ok {
			slog.FromContext(ctx).Fatal(constants.CreditBureauEnquiryKafkaConsumerLog, fmt.Sprintf("Wrong entity in reader, event=[%+v]", event.Event),
				commonTags(c.AppConfig.CreditBureauEnquiryKafkaConfig.KafkaConfig.Stream, c.AppConfig.CreditBureauEnquiryKafkaConfig.KafkaConfig.DtoName)...)
			continue
		}

		ctx = slog.AddTagsToContext(ctx, tags.TraceID(data.ReferenceID))

		err := commonUtils.RetryFunction(ctx, func() error {
			return c.handleCreditBureauEnquiryEvent(ctx, data)
		}, constants.CreditBureauEnquiryKafkaConsumerLog, c.AppConfig.CreditBureauEnquiryKafkaConfig.MaxRetryCount, c.AppConfig.CreditBureauEnquiryKafkaConfig.DelayInMilliSeconds)

		if err != nil {
			slog.FromContext(ctx).Warn(constants.CreditBureauEnquiryKafkaConsumerLog, fmt.Sprintf("Handling stream event failed, %s", err.Error()),
				commonTags(c.AppConfig.CreditBureauEnquiryKafkaConfig.KafkaConfig.Stream, c.AppConfig.CreditBureauEnquiryKafkaConfig.KafkaConfig.DtoName)...)
		}
		if err = event.Ack(); err != nil {
			slog.FromContext(ctx).Warn(constants.CreditBureauEnquiryKafkaConsumerLog, fmt.Sprintf("Failed to ack message: %s", err.Error()),
				commonTags(c.AppConfig.CreditBureauEnquiryKafkaConfig.KafkaConfig.Stream, c.AppConfig.CreditBureauEnquiryKafkaConfig.KafkaConfig.DtoName)...)
		}
	}
}

// handleCreditBureauEnquiryEvent handle credit bureau enquiry response event
func (c *CreditBureauEnquiryStreamImpl) handleCreditBureauEnquiryEvent(ctx context.Context, data *creditBureauEvent.CreditBureauEvent) error {
	// publishing custom metrics for consumer
	streamIDTag := fmt.Sprintf("stream:%s", constants.CreditBureauEnquiryKafkaConsumerLog)
	c.StatsD.Duration(stats.StreamConsumersContextTag, stats.StreamConsumeLatencyKey, data.GetStreamInfo().StreamTime, streamIDTag)
	defer c.StatsD.Duration(stats.StreamConsumersContextTag, stats.StreamProcessLatencyKey, time.Now(), streamIDTag)

	// start processing message
	eventDTO, err := mapper.MapCreditBureauEnquiryStreamToDTO(data)
	if err != nil {
		return err
	}
	var workflowErr error
	if c.AppConfig.FeatureFlags.EnableFlexiCardFlag {
		workflowErr = flexicardapplication.ConsumeCBSData(ctx, eventDTO)
		if workflowErr != nil {
			return workflowErr
		}

		workflowErr = bundledapplication.ConsumeCBSData(ctx, eventDTO)
		if workflowErr != nil {
			return workflowErr
		}
	}
	if c.AppConfig.FeatureFlags.EnableFlexiTermLoanApplicationWorkflow && constants.DefaultCountryCode == countries.SG {
		workflowErr = gxsFlexiTermLoanApplication.ConsumeCBSData(ctx, eventDTO)
		if workflowErr != nil {
			return workflowErr
		}
	}
	if constants.DefaultCountryCode == countries.MY {
		workflowErr = gxbFlexiTermLoanApplication.HandleCBSEvent(ctx, eventDTO)
		if workflowErr != nil {
			return workflowErr
		}
	}
	workflowErr = bizflexicreditapplication.ConsumeCBSData(ctx, eventDTO)
	if workflowErr != nil {
		return workflowErr
	}

	return nil
}
