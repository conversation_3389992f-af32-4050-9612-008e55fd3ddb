package consumers

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	creditBureauEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/credit_bureau_event"

	"gitlab.myteksi.net/dakota/lending/common/constant"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

func Test_registerCreditBureauEnquiryEvent(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		appCfg := &config.AppConfig{}
		impl := CreditBureauEnquiryStreamImpl{AppConfig: appCfg, StatsD: statsd.NewNoop()}
		impl.registerCreditBureauEnquiryEvent(getInputChannel(
			&kafkareader.AckEntity{Event: &creditBureauEvent.CreditBureauEvent{
				ReferenceID:   uuid.NewString(),
				ApplicationID: uuid.NewString(),
				ApplicantID:   uuid.NewString(),
				Service:       string(constant.EnquiryServiceEnqlitsc),
				Status:        string(constant.CbsReportStatusSuccess),
				CountryCode:   "SG",
			}}))
		err := wg.WaitForDone(time.Second * 4)
		require.Nil(t, err)
	})
	mock.AssertExpectationsForObjects(t)
}

func Test_handleCreditBureauEnquiryEvent(t *testing.T) {
	scenarios := []struct {
		desc          string
		data          *creditBureauEvent.CreditBureauEvent
		expectedError error
	}{
		{
			desc:          "event",
			data:          &creditBureauEvent.CreditBureauEvent{},
			expectedError: errors.New("workflow not registered"),
		},
	}
	for _, tt := range scenarios {
		s := tt
		t.Run(s.desc, func(t *testing.T) {
			impl := CreditBureauEnquiryStreamImpl{
				AppConfig: &config.AppConfig{},
				StatsD:    statsd.NewNoop(),
			}
			consumerErr := impl.handleCreditBureauEnquiryEvent(context.Background(), s.data)
			if s.expectedError == nil {
				assert.Nil(t, consumerErr)
			} else {
				assert.Equal(t, s.expectedError.Error(), consumerErr.Error())
			}
		})
	}
}
