// nolint:dupl
package consumers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	nameScreeningEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/lending_name_screening"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka"
)

// RsLendingNameScreeningStream ...
// nolint:dupl
type RsLendingNameScreeningStream struct {
	AppConfig *config.AppConfig `inject:"config"`
	StatsD    statsd.Client     `inject:"statsD"`
}

// GetEventSchema ...
// nolint:dupl
func (r RsLendingNameScreeningStream) GetEventSchema() kafka.Entity {
	return &nameScreeningEvent.LendingNameScreening{}
}

// Handle ...
// nolint:dupl
func (r RsLendingNameScreeningStream) Handle(ctx context.Context, message kafka.Entity) error {
	data, ok := message.(*nameScreeningEvent.LendingNameScreening)
	if !ok {
		slog.FromContext(ctx).Fatal(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Wrong entity in reader, event=[%+v]", data),
			commonTags(r.AppConfig.ApplicationStatusTransitionKafkaConfig.KafkaConfig.Stream, r.AppConfig.ApplicationStatusTransitionKafkaConfig.KafkaConfig.DtoName)...)
		return nil
	}

	lendingNameScreeningEventStreamImpl := LendingNameScreeningEventStreamImpl{AppConfig: r.AppConfig, StatsD: r.StatsD}
	return lendingNameScreeningEventStreamImpl.handleLendingNameScreeningEvent(ctx, data)
}
