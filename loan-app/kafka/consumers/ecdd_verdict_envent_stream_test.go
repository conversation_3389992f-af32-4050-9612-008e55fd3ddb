package consumers

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/resources"
	ecddVerdictEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/ecdd_verdict_event"

	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

func Test_registerEcddVerdict(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		appCfg := &config.AppConfig{}
		impl := EcddVerdictEventStreamImpl{AppConfig: appCfg, StatsD: statsd.NewNoop()}
		impl.registerEcddVerdictEvent(getInputChannel(
			&kafkareader.AckEntity{Event: &ecddVerdictEvent.EcddVerdictEvent{
				CifNumber:       "",
				Approve:         true,
				CaseType:        "",
				ApplicationID:   "",
				CheckerComment:  "",
				MakerComment:    "",
				ApprovalDetails: []*ecddVerdictEvent.ApprovalDetail{},
				EcddInfo: &ecddVerdictEvent.EcddInfo{
					PurposeOfAccount: "",
				},
			}}))
		err := wg.WaitForDone(time.Second * 4)
		require.Nil(t, err)
	})
	mock.AssertExpectationsForObjects(t)
}

func Test_handleEcddVerdictEvent(t *testing.T) {
	scenarios := []struct {
		desc          string
		data          *ecddVerdictEvent.EcddVerdictEvent
		expectedError error
	}{
		{
			desc: "event",
			data: &ecddVerdictEvent.EcddVerdictEvent{
				CifNumber:       "",
				Approve:         true,
				CaseType:        "",
				ApplicationID:   "",
				CheckerComment:  "",
				MakerComment:    "",
				ApprovalDetails: []*ecddVerdictEvent.ApprovalDetail{},
				EcddInfo: &ecddVerdictEvent.EcddInfo{
					PurposeOfAccount: "",
				},
			},
			expectedError: errors.New("workflow not registered"),
		},
	}
	for _, tt := range scenarios {
		s := tt
		t.Run(s.desc, func(t *testing.T) {
			mockApplicationDAO := &storage.MockIApplicationDAO{}
			storage.ApplicationDao = mockApplicationDAO
			mockApplicationDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.SampleApplicationData(), nil).Once()
			impl := EcddVerdictEventStreamImpl{
				AppConfig: &config.AppConfig{},
				StatsD:    statsd.NewNoop(),
			}
			consumerErr := impl.handleEcddVerdictEvent(context.Background(), s.data)
			assert.Equal(t, s.expectedError, consumerErr)
		})
	}
}
