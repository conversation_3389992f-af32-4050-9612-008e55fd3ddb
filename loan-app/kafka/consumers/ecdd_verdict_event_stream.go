package consumers

import (
	"context"
	"fmt"
	"runtime/debug"
	"time"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	gxbFlexiTermLoanApplication "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/gxb"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/schemas/streams"
	ecddVerdictEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/ecdd_verdict_event"

	"gitlab.myteksi.net/dakota/lending/common/stats"
	commonUtils "gitlab.myteksi.net/dakota/lending/common/utils"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	tags "gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

var (
	ecddVerdictEventConsumer kafkareader.Client
)

// EcddVerdictEventStreamImpl ecdd verdict enquiry stream consumer impl
type EcddVerdictEventStreamImpl struct {
	AppConfig *config.AppConfig
	StatsD    statsd.Client
}

func startEcddVerdictEventStream(ctx context.Context, appConfig *config.AppConfig, statsD statsd.Client) {
	defer func() {
		if err := recover(); err != nil {
			slog.FromContext(ctx).Warn(constants.EcddVerdictEventKafkaConsumerLog, fmt.Sprintf("Panic occurred. error: %s, stacktrace %s", err, string(debug.Stack())))
		} else {
			slog.FromContext(ctx).Info(constants.EcddVerdictEventKafkaConsumerLog, "successfully started EcddVerdictEventStream consumer")
		}
	}()

	reader, err := streams.NewStaticReader(ctx, ecddVerdictEventStream, *appConfig.EcddVerdictEventKafkaConfig.KafkaConfig, &ecddVerdictEvent.EcddVerdictEvent{})
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", appConfig.EcddVerdictEventKafkaConfig, err))
	}

	ecddVerdictEventConsumer = reader
	ecddVerdictEventStreamImplementation := EcddVerdictEventStreamImpl{
		AppConfig: appConfig,
		StatsD:    statsD,
	}
	ecddVerdictEventStreamImplementation.registerEcddVerdictEvent(ecddVerdictEventConsumer.GetDataAckChan())
}

func (c *EcddVerdictEventStreamImpl) registerEcddVerdictEvent(ackChan <-chan *kafkareader.AckEntity) {
	wg.Go(constants.EcddVerdictEventKafkaConsumerLog, func() {
		c.consumeEcddVerdictEvent(context.Background(), ackChan)
	})
}

// nolint:dupl
func (c *EcddVerdictEventStreamImpl) consumeEcddVerdictEvent(ctx context.Context, ackChan <-chan *kafkareader.AckEntity) {
	for event := range ackChan {
		data, ok := event.Event.(*ecddVerdictEvent.EcddVerdictEvent)

		if !ok {
			slog.FromContext(ctx).Fatal(constants.EcddVerdictEventKafkaConsumerLog, fmt.Sprintf("Wrong entity in reader, event=[%+v]", event.Event),
				commonTags(c.AppConfig.EcddVerdictEventKafkaConfig.KafkaConfig.Stream, c.AppConfig.EcddVerdictEventKafkaConfig.KafkaConfig.DtoName)...)
			continue
		}

		ctx = slog.AddTagsToContext(ctx, tags.TraceID(data.ApplicationID))

		err := commonUtils.RetryFunction(ctx, func() error {
			return c.handleEcddVerdictEvent(ctx, data)
		}, constants.EcddVerdictEventKafkaConsumerLog, c.AppConfig.EcddVerdictEventKafkaConfig.MaxRetryCount, c.AppConfig.EcddVerdictEventKafkaConfig.DelayInMilliSeconds)

		if err != nil {
			slog.FromContext(ctx).Warn(constants.EcddVerdictEventKafkaConsumerLog, fmt.Sprintf("Handling stream event failed, %s", err.Error()),
				commonTags(c.AppConfig.EcddVerdictEventKafkaConfig.KafkaConfig.Stream, c.AppConfig.EcddVerdictEventKafkaConfig.KafkaConfig.DtoName)...)
		}
		if err = event.Ack(); err != nil {
			slog.FromContext(ctx).Warn(constants.EcddVerdictEventKafkaConsumerLog, fmt.Sprintf("Failed to ack message: %s", err.Error()),
				commonTags(c.AppConfig.EcddVerdictEventKafkaConfig.KafkaConfig.Stream, c.AppConfig.EcddVerdictEventKafkaConfig.KafkaConfig.DtoName)...)
		}
	}
}

// handleEcddVerdictEvent handle ecdd verdict enquiry response event
func (c *EcddVerdictEventStreamImpl) handleEcddVerdictEvent(ctx context.Context, data *ecddVerdictEvent.EcddVerdictEvent) error {
	// publishing custom metrics for consumer
	streamIDTag := fmt.Sprintf("stream:%s", constants.EcddVerdictEventKafkaConsumerLog)
	c.StatsD.Duration(stats.StreamConsumersContextTag, stats.StreamConsumeLatencyKey, data.GetStreamInfo().StreamTime, streamIDTag)
	defer c.StatsD.Duration(stats.StreamConsumersContextTag, stats.StreamProcessLatencyKey, time.Now(), streamIDTag)

	// start processing message
	eventDTO := mapEcddVerdictEventStreamToDTO(data)
	workflowErr := gxbFlexiTermLoanApplication.HandleEcddVerdictEvent(ctx, eventDTO)
	if workflowErr != nil {
		return workflowErr
	}
	return nil
}

// mapEcddVerdictEventStreamToDTO maps ecdd Verdict event stream object to dto
func mapEcddVerdictEventStreamToDTO(data *ecddVerdictEvent.EcddVerdictEvent) *dto.EcddVerdictEvent {
	return &dto.EcddVerdictEvent{
		CifNumber:      data.CifNumber,
		Approve:        data.Approve,
		CaseType:       data.CaseType,
		ApplicationID:  data.ApplicationID,
		CheckerComment: data.CheckerComment,
		MakerComment:   data.MakerComment,
	}
}
