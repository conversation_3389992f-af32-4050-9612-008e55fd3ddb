package consumers

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.com/gx-regional/dakota/lending/loan-app/handlers"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/updatefinexus"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	finexusUpdateSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/lending_finexus_update"

	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/workflowengine"
	weStorage "gitlab.myteksi.net/dakota/workflowengine/storage"
	workflowStorageMock "gitlab.myteksi.net/dakota/workflowengine/storage/mocks"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

func TestFinexusUpdateStreamImpl_registerFinexusUpdateStream(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		impl := FinexusUpdateStreamImpl{
			AppConfig: &config.AppConfig{},
			StatsD:    statsd.NewNoop(),
		}
		impl.registerFinexusUpdateStream(getInputChannel(
			&kafkareader.AckEntity{Event: &finexusUpdateSchema.LendingFinexusUpdate{
				OnboardingApplicationID: "test-application",
				OnboardingStatus:        finexusUpdateSchema.OnboardingStatusUserAcceptPurposeOfLoan,
				PurposeOfLoan:           "test-purpose",
			}}))
		err := wg.WaitForDone(time.Second * 4)
		require.Nil(t, err)
	})
	mock.AssertExpectationsForObjects(t)
}

func Test_startFinexusUpdateStream(t *testing.T) {
	type args struct {
		ctx       context.Context
		service   *handlers.LoanAppService
		appConfig *config.AppConfig
		statsD    statsd.Client
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "happy-path",
			args: args{
				ctx:       context.Background(),
				service:   &handlers.LoanAppService{},
				appConfig: &config.AppConfig{},
				statsD:    statsd.NewNoop(),
			},
		},
		{
			name: "nil-service",
			args: args{
				ctx:       context.Background(),
				service:   nil,
				appConfig: &config.AppConfig{},
				statsD:    statsd.NewNoop(),
			},
		},
		{
			name: "nil-appConfig",
			args: args{
				ctx:       context.Background(),
				service:   &handlers.LoanAppService{},
				appConfig: nil,
				statsD:    statsd.NewNoop(),
			},
		},
		{
			name: "nil-statsD",
			args: args{
				ctx:       context.Background(),
				service:   &handlers.LoanAppService{},
				appConfig: &config.AppConfig{},
				statsD:    nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			startFinexusUpdateStream(tt.args.ctx, tt.args.service, tt.args.appConfig, tt.args.statsD)
		})
	}
}

func TestFinexusUpdateStreamImpl_handleFinexusUpdate(t *testing.T) {
	mockUpdateFinexusWorkflow := &updatefinexus.WorkflowImpl{
		AppConfig: &config.AppConfig{
			LocaleConfig: config.LocaleConfig{
				DefaultCountryCode: "MY",
			},
			WorkflowRetryConfig: config.WorkflowRetryConfig{
				UpdateFinexus: &config.RetryOptions{
					TransactionalRetryOption: &config.RetryPolicy{
						IntervalInSeconds: 30,
						MaxAttempt:        3,
					},
				},
			},
		}}
	mockUpdateFinexusWorkflow.Register()
	type fields struct {
		KafkaConfig           config.FinexusUpdateKafkaConfig
		StatsD                statsd.Client
		UpdateFinexusWorkflow updatefinexus.Workflow
	}
	type args struct {
		data *finexusUpdateSchema.LendingFinexusUpdate
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		executeErr error
		wantErr    bool
	}{
		{
			name: "Update with error",
			fields: fields{
				KafkaConfig: config.FinexusUpdateKafkaConfig{},
				StatsD:      statsd.NewNoop(),
			},
			args: args{
				data: &finexusUpdateSchema.LendingFinexusUpdate{
					OnboardingApplicationID: "test-application",
					OnboardingStatus:        finexusUpdateSchema.OnboardingStatusUserAcceptPurposeOfLoan,
					PurposeOfLoan:           "test-purpose",
				}},
			executeErr: errors.New("dummy error"),
			wantErr:    true,
		},
		{
			name: "Successful create Finexus",
			fields: fields{
				KafkaConfig: config.FinexusUpdateKafkaConfig{},
				StatsD:      statsd.NewNoop(),
			},
			args: args{
				data: &finexusUpdateSchema.LendingFinexusUpdate{
					OnboardingApplicationID: "test-application",
					OnboardingStatus:        finexusUpdateSchema.OnboardingStatusUserAcceptPurposeOfLoan,
					PurposeOfLoan:           "test-purpose",
				},
			},
			wantErr: false,
		},
		{
			name: "Successful update Finexus",
			fields: fields{
				KafkaConfig: config.FinexusUpdateKafkaConfig{},
				StatsD:      statsd.NewNoop(),
			},
			args: args{
				data: &finexusUpdateSchema.LendingFinexusUpdate{
					OnboardingApplicationID: "test-application",
					OnboardingStatus:        finexusUpdateSchema.OnboardingStatusNameScreeningApproved,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockupdateFinexusWorkflow := &updatefinexus.MockWorkflow{}
			mockupdateFinexusWorkflow.On("ExecuteFinexusUpdate", mock.Anything, mock.Anything).Return(tt.executeErr).Once()
			mockWorkflowDAO := workflowStorageMock.IWorkflowExecutionDAO{}
			workflowengine.InitWithMockDAO(&mockWorkflowDAO)
			byteData, _ := json.Marshal(&updatefinexus.ExecutionData{})
			mockWorkflowDAO.On("Find", mock.Anything, mock.AnythingOfType("data.Condition"), mock.AnythingOfType("data.Condition")).
				Return([]*weStorage.WorkflowExecution{
					{
						WorkflowID: "update_finexus_workflow",
						Data:       byteData,
						State:      0,
					}}, nil).Once()
			f := &FinexusUpdateStreamImpl{
				AppConfig:             &config.AppConfig{},
				StatsD:                tt.fields.StatsD,
				UpdateFinexusWorkflow: mockupdateFinexusWorkflow,
			}

			if err := f.handleFinexusUpdate(context.Background(), tt.args.data); (err != nil) != tt.wantErr {
				t.Errorf("FinexusUpdateStreamImpl.handleFinexusUpdate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
