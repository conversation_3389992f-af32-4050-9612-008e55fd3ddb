package consumers

import (
	"context"
	"fmt"
	"runtime/debug"
	"time"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/bizflexicreditapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/bundledapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/gxb"
	gxsFlexiTermLoanApplication "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/gxs"
	"gitlab.com/gx-regional/dakota/lending/loan-app/mapper"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/schemas/streams"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/loc_account_creation_event"

	commonConst "gitlab.myteksi.net/dakota/lending/common/constant"
	"gitlab.myteksi.net/dakota/lending/common/countries"
	"gitlab.myteksi.net/dakota/lending/common/stats"
	commonUtils "gitlab.myteksi.net/dakota/lending/common/utils"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

var (
	locAccountCreationEventConsumer kafkareader.Client
)

// LocAccountCreationEventStreamImpl ...
type LocAccountCreationEventStreamImpl struct {
	KafkaConfig config.KafkaConfig
	StatsD      statsd.Client
	AppConfig   *config.AppConfig
}

func startLOCAccountCreationEventConsumer(ctx context.Context, appConfig *config.AppConfig, statsD statsd.Client) {
	defer func() {
		if err := recover(); err != nil {
			slog.FromContext(ctx).Warn(constants.LOCAccountCreationEventConsumerLogTag, fmt.Sprintf("Panic occurred. error: %s, stacktrace %s", err, string(debug.Stack())))
		} else {
			slog.FromContext(ctx).Info(constants.LOCAccountCreationEventConsumerLogTag, "Successfully started LOC Account Creation Event consumer")
		}
	}()

	reader, err := streams.NewStaticReader(ctx, locAccountCreationEventStream,
		*appConfig.LOCAccountCreationEventKafkaConfig.KafkaConfig, &loc_account_creation_event.LOCAccountCreationEvent{})
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", appConfig.LOCAccountCreationEventKafkaConfig.KafkaConfig, err))
	}

	locAccountCreationEventConsumer = reader
	impl := LocAccountCreationEventStreamImpl{KafkaConfig: *appConfig.LOCAccountCreationEventKafkaConfig, StatsD: statsD, AppConfig: appConfig}
	registerLOCAccountCreationEvent(impl, locAccountCreationEventConsumer.GetDataAckChan())
}

var registerLOCAccountCreationEvent = func(impl LocAccountCreationEventStreamImpl, ch <-chan *kafkareader.AckEntity) {
	wg.Go(constants.LOCAccountCreationEventConsumerLogTag, func() {
		impl.consumeLOCAccountCreationEvent(context.Background(), ch)
	})
}

// consumeLOCAccountCreationEvent ...
// nolint:dupl
func (l *LocAccountCreationEventStreamImpl) consumeLOCAccountCreationEvent(ctx context.Context, ch <-chan *kafkareader.AckEntity) {
	for event := range ch {
		data, ok := event.Event.(*loc_account_creation_event.LOCAccountCreationEvent)
		if !ok {
			slog.FromContext(ctx).Fatal(constants.LOCAccountCreationEventConsumerLogTag, fmt.Sprintf("Wrong entity in reader, event=[%+v]", event.Event),
				commonTags(l.KafkaConfig.Stream, l.KafkaConfig.DtoName)...)
			continue
		}

		err := commonUtils.RetryFunction(ctx, func() error {
			return l.handleLOCAccountCreationEvent(ctx, data)
		}, logTag, l.KafkaConfig.MaxRetryCount, l.KafkaConfig.DelayInMilliSeconds)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.LOCAccountCreationEventConsumerLogTag, fmt.Sprintf("Handling stream event failed, %s", err.Error()), commonTags(l.KafkaConfig.Stream, l.KafkaConfig.DtoName)...)
		}
		if err = event.Ack(); err != nil {
			slog.FromContext(ctx).Warn(constants.LOCAccountCreationEventConsumerLogTag, fmt.Sprintf("Failed to ack message: %s", err.Error()), commonTags(l.KafkaConfig.Stream, l.KafkaConfig.DtoName)...)
		}
	}
}

func (l *LocAccountCreationEventStreamImpl) handleLOCAccountCreationEvent(ctx context.Context, data *loc_account_creation_event.LOCAccountCreationEvent) error {
	// publishing custom metrics for consumer
	streamIDTag := fmt.Sprintf("stream:%s", constants.LOCAccountCreationEventConsumerLogTag)
	StatsDClient.Duration(stats.StreamConsumersContextTag, stats.StreamConsumeLatencyKey, data.GetStreamInfo().StreamTime, streamIDTag)
	defer l.StatsD.Duration(stats.StreamConsumersContextTag, stats.StreamProcessLatencyKey, time.Now(), streamIDTag)

	eventDTO := mapper.MapLOCAccountCreationStreamToDTO(data)

	if eventDTO.ProductVariantCode == commonConst.BizLineOfCreditProductVariantCode {
		if consumerErr := bizflexicreditapplication.ConsumeBizLOCAccountCreationEvent(ctx, eventDTO); consumerErr != nil {
			return consumerErr
		}
	} else {
		if constants.DefaultCountryCode == countries.MY {
			if consumerErr := gxb.ConsumeLOCAccountCreationEvent(ctx, eventDTO); consumerErr != nil {
				return consumerErr
			}
		}
		if constants.DefaultCountryCode == countries.SG && l.AppConfig.FeatureFlags.EnableFlexiTermLoanApplicationWorkflow && eventDTO.CreatedBy == constants.GXSWorkflowID.ToString() {
			if consumeErr := gxsFlexiTermLoanApplication.ConsumeLOCAccountCreationEvent(ctx, eventDTO); consumeErr != nil {
				return consumeErr
			}
			return nil
		}
		if l.AppConfig.FeatureFlags.EnableFlexiCardFlag {
			if consumerErr := bundledapplication.ConsumeLOCAccountCreationEvent(ctx, eventDTO); consumerErr != nil {
				return consumerErr
			}
		}
	}
	return nil
}
