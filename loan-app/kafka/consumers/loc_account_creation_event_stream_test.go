package consumers

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/loc_account_creation_event"

	commonConst "gitlab.myteksi.net/dakota/lending/common/constant"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

func Test_registerLOCAccountCreationEvent(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		impl := LocAccountCreationEventStreamImpl{
			KafkaConfig: config.KafkaConfig{},
			StatsD:      statsd.NewNoop(),
		}
		registerLOCAccountCreationEvent(impl, getInputChannel(
			&kafkareader.AckEntity{Event: &loc_account_creation_event.LOCAccountCreationEvent{
				ReferenceID:   uuid.NewString(),
				ApplicationID: uuid.NewString(),
				AccountID:     uuid.NewString(),
				Status:        "",
			}}))
		err := wg.WaitForDone(time.Second * 4)
		require.Nil(t, err)
	})
	mock.AssertExpectationsForObjects(t)
}

func Test_handleLOCAccountCreationEvent(t *testing.T) {
	scenarios := []struct {
		desc          string
		data          *loc_account_creation_event.LOCAccountCreationEvent
		expectedError error
	}{
		{
			desc:          "event",
			data:          &loc_account_creation_event.LOCAccountCreationEvent{ProductVariantCode: commonConst.FlexiLineOfCreditProductVariantCode},
			expectedError: errors.New("workflow not registered"),
		},
		{
			desc:          "event",
			data:          &loc_account_creation_event.LOCAccountCreationEvent{ProductVariantCode: commonConst.BizLineOfCreditProductVariantCode},
			expectedError: errors.New("workflow not registered"),
		},
	}
	for _, tt := range scenarios {
		s := tt
		t.Run(s.desc, func(t *testing.T) {
			impl := LocAccountCreationEventStreamImpl{
				KafkaConfig: config.KafkaConfig{},
				StatsD:      statsd.NewNoop(),
				AppConfig:   &config.AppConfig{FeatureFlags: config.FeatureFlags{EnableFlexiCardFlag: true}},
			}
			StatsDClient = impl.StatsD
			consumerErr := impl.handleLOCAccountCreationEvent(context.Background(), s.data)
			if s.expectedError == nil {
				assert.Nil(t, consumerErr)
			} else {
				assert.Equal(t, s.expectedError.Error(), consumerErr.Error())
			}
		})
	}
}
