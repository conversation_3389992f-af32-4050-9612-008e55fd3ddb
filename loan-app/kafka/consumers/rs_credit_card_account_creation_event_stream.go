package consumers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/credit_card_account_creation_event"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka"
)

// RsCreditCardAccountCreationEventStream ...
type RsCreditCardAccountCreationEventStream struct {
	AppConfig *config.AppConfig
	StatsD    statsd.Client
}

// GetEventSchema ...
func (r RsCreditCardAccountCreationEventStream) GetEventSchema() kafka.Entity {
	return &credit_card_account_creation_event.CreditCardAccountCreationEvent{}
}

// Handle ...
func (r RsCreditCardAccountCreationEventStream) Handle(ctx context.Context, message kafka.Entity) error {
	data, ok := message.(*credit_card_account_creation_event.CreditCardAccountCreationEvent)
	if !ok {
		slog.FromContext(ctx).Fatal(constants.LOCAccountCreationEventConsumerLogTag, fmt.Sprintf("Wrong entity in reader, event=[%+v]", data),
			commonTags(r.AppConfig.CreditCardAccountCreationKafkaConfig.Stream, r.AppConfig.CreditCardAccountCreationKafkaConfig.DtoName)...)
		return nil
	}

	creditCardAccountCreationStreamImpl := CreditCardAccountCreationStreamImpl(r)
	return creditCardAccountCreationStreamImpl.handleCreditCardAccountCreationEvents(ctx, data)
}
