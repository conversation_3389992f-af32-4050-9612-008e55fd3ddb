package consumers

import (
	"context"
	"fmt"
	"runtime/debug"
	"time"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	gxbFlexiTermLoanApplication "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/gxb"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/schemas/streams"
	nameScreeningEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/lending_name_screening"

	"gitlab.myteksi.net/dakota/lending/common/stats"
	commonUtils "gitlab.myteksi.net/dakota/lending/common/utils"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	tags "gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

var (
	lendingNameScreeningEventConsumer kafkareader.Client
)

// LendingNameScreeningEventStreamImpl name screening enquiry stream consumer impl
type LendingNameScreeningEventStreamImpl struct {
	AppConfig *config.AppConfig
	StatsD    statsd.Client
}

func startLendingNameScreeningEventStream(ctx context.Context, appConfig *config.AppConfig, statsD statsd.Client) {
	defer func() {
		if err := recover(); err != nil {
			slog.FromContext(ctx).Warn(constants.LendingNameScreeningEventKafkaConsumerLog, fmt.Sprintf("Panic occurred. error: %s, stacktrace %s", err, string(debug.Stack())))
		} else {
			slog.FromContext(ctx).Info(constants.LendingNameScreeningEventKafkaConsumerLog, "successfully started LendingNameScreeningEventStream consumer")
		}
	}()

	reader, err := streams.NewStaticReader(ctx, lendingNameScreeningEventStream, *appConfig.LendingNameScreeningEventKafkaConfig.KafkaConfig, &nameScreeningEvent.LendingNameScreening{})
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", appConfig.LendingNameScreeningEventKafkaConfig, err))
	}

	lendingNameScreeningEventConsumer = reader
	lendingNameScreeningEventStreamImpl := LendingNameScreeningEventStreamImpl{
		AppConfig: appConfig,
		StatsD:    statsD,
	}
	lendingNameScreeningEventStreamImpl.registerLendingNameScreeningEvent(lendingNameScreeningEventConsumer.GetDataAckChan())
}

func (c *LendingNameScreeningEventStreamImpl) registerLendingNameScreeningEvent(ackChan <-chan *kafkareader.AckEntity) {
	wg.Go(constants.LendingNameScreeningEventKafkaConsumerLog, func() {
		c.consumeLendingNameScreeningEvent(context.Background(), ackChan)
	})
}

// nolint:dupl
func (c *LendingNameScreeningEventStreamImpl) consumeLendingNameScreeningEvent(ctx context.Context, ackChan <-chan *kafkareader.AckEntity) {
	for event := range ackChan {
		data, ok := event.Event.(*nameScreeningEvent.LendingNameScreening)

		if !ok {
			slog.FromContext(ctx).Fatal(constants.LendingNameScreeningEventKafkaConsumerLog, fmt.Sprintf("Wrong entity in reader, event=[%+v]", event.Event),
				commonTags(c.AppConfig.LendingNameScreeningEventKafkaConfig.KafkaConfig.Stream, c.AppConfig.LendingNameScreeningEventKafkaConfig.KafkaConfig.DtoName)...)
			continue
		}

		ctx = slog.AddTagsToContext(ctx, tags.TraceID(data.ReferenceID))

		err := commonUtils.RetryFunction(ctx, func() error {
			return c.handleLendingNameScreeningEvent(ctx, data)
		}, constants.LendingNameScreeningEventKafkaConsumerLog, c.AppConfig.LendingNameScreeningEventKafkaConfig.MaxRetryCount, c.AppConfig.LendingNameScreeningEventKafkaConfig.DelayInMilliSeconds)

		if err != nil {
			slog.FromContext(ctx).Warn(constants.LendingNameScreeningEventKafkaConsumerLog, fmt.Sprintf("Handling stream event failed, %s", err.Error()),
				commonTags(c.AppConfig.LendingNameScreeningEventKafkaConfig.KafkaConfig.Stream, c.AppConfig.LendingNameScreeningEventKafkaConfig.KafkaConfig.DtoName)...)
		}
		if err = event.Ack(); err != nil {
			slog.FromContext(ctx).Warn(constants.LendingNameScreeningEventKafkaConsumerLog, fmt.Sprintf("Failed to ack message: %s", err.Error()),
				commonTags(c.AppConfig.LendingNameScreeningEventKafkaConfig.KafkaConfig.Stream, c.AppConfig.LendingNameScreeningEventKafkaConfig.KafkaConfig.DtoName)...)
		}
	}
}

// handleLendingNameScreeningEvent handle name screening enquiry response event
func (c *LendingNameScreeningEventStreamImpl) handleLendingNameScreeningEvent(ctx context.Context, data *nameScreeningEvent.LendingNameScreening) error {
	// publishing custom metrics for consumer
	streamIDTag := fmt.Sprintf("stream:%s", constants.LendingNameScreeningEventKafkaConsumerLog)
	c.StatsD.Duration(stats.StreamConsumersContextTag, stats.StreamConsumeLatencyKey, data.GetStreamInfo().StreamTime, streamIDTag)
	defer c.StatsD.Duration(stats.StreamConsumersContextTag, stats.StreamProcessLatencyKey, time.Now(), streamIDTag)

	// start processing message
	eventDTO := mapLendingNameScreeningEventStreamToDTO(data)
	workflowErr := gxbFlexiTermLoanApplication.HandleLendingNameScreeningEvent(ctx, eventDTO)
	if workflowErr != nil {
		return workflowErr
	}
	return nil
}

// mapLendingNameScreeningEventStreamToDTO maps name screening event stream object to dto
func mapLendingNameScreeningEventStreamToDTO(data *nameScreeningEvent.LendingNameScreening) *dto.LendingNameScreening {
	return &dto.LendingNameScreening{
		ReferenceID: data.ReferenceID,
		Status:      dto.NameScreeningStatus(data.Status),
		Metadata: &dto.NameScreeningResultMetadata{
			ReferenceID:   data.Metadata.ReferenceID,
			ApplicationID: data.Metadata.ApplicationID,
			WorkflowRunID: data.Metadata.WorkflowRunID,
			CifNumber:     data.Metadata.CifNumber,
		},
	}
}
