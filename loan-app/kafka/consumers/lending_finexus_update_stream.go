package consumers

import (
	"context"
	"fmt"
	"runtime/debug"
	"time"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/handlers"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/updatefinexus"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	"gitlab.com/gx-regional/dakota/schemas/streams"
	finexusUpdateSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/lending_finexus_update"

	"gitlab.myteksi.net/dakota/lending/common/stats"
	commonUtils "gitlab.myteksi.net/dakota/lending/common/utils"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

var (
	finexusUpdateConsumer kafkareader.Client
)

// FinexusUpdateStreamImpl credit bureau enquiry stream consumer impl
type FinexusUpdateStreamImpl struct {
	AppConfig             *config.AppConfig
	StatsD                statsd.Client
	UpdateFinexusWorkflow updatefinexus.Workflow
}

func startFinexusUpdateStream(ctx context.Context, service *handlers.LoanAppService, appConfig *config.AppConfig, statsD statsd.Client) {
	defer func() {
		if err := recover(); err != nil {
			slog.FromContext(ctx).Warn(constants.FinexusUpdateKafkaConsumerLogTag, fmt.Sprintf("Panic occurred. error: %s, stacktrace %s", err, string(debug.Stack())))
		} else {
			slog.FromContext(ctx).Info(constants.FinexusUpdateKafkaConsumerLogTag, "Successfully started FinexusUpdateKafka consumer")
		}
	}()

	reader, err := streams.NewStaticReader(ctx, finexusUpdateEventStream, *appConfig.FinexusUpdateKafkaConfig.KafkaConfig, &finexusUpdateSchema.LendingFinexusUpdate{})
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", appConfig.FinexusUpdateKafkaConfig, err))
	}

	finexusUpdateConsumer = reader
	finexusUpdateStreamImpl := FinexusUpdateStreamImpl{
		AppConfig:             appConfig,
		StatsD:                statsD,
		UpdateFinexusWorkflow: service.UpdateFinexusWorkflow,
	}
	finexusUpdateStreamImpl.registerFinexusUpdateStream(finexusUpdateConsumer.GetDataAckChan())
}

func (f *FinexusUpdateStreamImpl) registerFinexusUpdateStream(ackChan <-chan *kafkareader.AckEntity) {
	wg.Go(constants.FinexusUpdateKafkaConsumerLogTag, func() {
		f.consumeFinexusUpdateStream(context.Background(), ackChan)
	})
}

func (f *FinexusUpdateStreamImpl) consumeFinexusUpdateStream(ctx context.Context, ch <-chan *kafkareader.AckEntity) {
	for event := range ch {
		data, ok := event.Event.(*finexusUpdateSchema.LendingFinexusUpdate)
		if !ok {
			slog.FromContext(ctx).Fatal(constants.FinexusUpdateKafkaConsumerLogTag, fmt.Sprintf("Wrong entity in reader, event=[%+v]", event.Event),
				commonTags(f.AppConfig.FinexusUpdateKafkaConfig.Stream, f.AppConfig.FinexusUpdateKafkaConfig.DtoName)...)
			continue
		}
		slog.FromContext(ctx).Info(constants.FinexusUpdateKafkaConsumerLogTag, fmt.Sprintf("Finexus Update received: %s", utils.ToJSON(data)))

		err := commonUtils.RetryFunction(ctx, func() error {
			return f.handleFinexusUpdate(ctx, data)
		}, constants.FinexusUpdateKafkaConsumerLogTag, f.AppConfig.FinexusUpdateKafkaConfig.MaxRetryCount, f.AppConfig.FinexusUpdateKafkaConfig.DelayInMilliSeconds)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.FinexusUpdateKafkaConsumerLogTag, fmt.Sprintf("Handling stream event failed, %s", err.Error()), commonTags(f.AppConfig.FinexusUpdateKafkaConfig.Stream, f.AppConfig.FinexusUpdateKafkaConfig.DtoName)...)
		}
		if err = event.Ack(); err != nil {
			slog.FromContext(ctx).Warn(constants.FinexusUpdateKafkaConsumerLogTag, fmt.Sprintf("Failed to ack message: %s", err.Error()), commonTags(f.AppConfig.FinexusUpdateKafkaConfig.Stream, f.AppConfig.FinexusUpdateKafkaConfig.DtoName)...)
		}
	}
}

func (f *FinexusUpdateStreamImpl) handleFinexusUpdate(ctx context.Context, data *finexusUpdateSchema.LendingFinexusUpdate) error {
	// publishing custom metrics for consumer
	streamIDTag := fmt.Sprintf("stream:%s", constants.FinexusUpdateKafkaConsumerLogTag)
	f.StatsD.Duration(stats.StreamConsumersContextTag, stats.StreamConsumeLatencyKey, data.GetStreamInfo().StreamTime, streamIDTag)
	defer f.StatsD.Duration(stats.StreamConsumersContextTag, stats.StreamProcessLatencyKey, time.Now(), streamIDTag)
	eventData := mapFinexusStreamToDTO(data)
	if data.OnboardingStatus != "" && data.OnboardingStatus != finexusUpdateSchema.OnboardingStatusUserAcceptPurposeOfLoan {
		if consumerErr := updatefinexus.ConsumeFinexusEvent(ctx, eventData); consumerErr != nil {
			return consumerErr
		}
	} else if data.OnboardingStatus == finexusUpdateSchema.OnboardingStatusUserAcceptPurposeOfLoan {
		if consumerErr := f.UpdateFinexusWorkflow.ExecuteFinexusUpdate(ctx, eventData); consumerErr != nil {
			return consumerErr
		}
	}

	return nil
}

// mapFinexusStreamToDTO maps finexus stream to dto
func mapFinexusStreamToDTO(data *finexusUpdateSchema.LendingFinexusUpdate) *dto.FinexusStreamMsgDTO {
	return &dto.FinexusStreamMsgDTO{
		OnboardingApplicationID: data.OnboardingApplicationID,
		PurposeOfLoan:           string(data.PurposeOfLoan),
		OnboardingStatus:        constants.OnboardingStatusType(data.OnboardingStatus),
	}
}
