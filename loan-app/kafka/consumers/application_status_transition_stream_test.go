package consumers

import (
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.com/gx-regional/dakota/lending/loan-app/external/appian/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/external/appian/mocks"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/consumerlogic/applicationstatustransition"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/resources"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	sndconfig "gitlab.myteksi.net/snd/streamsdk/kafka/config"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

func Test_registerApplicationStatusTransitionEvent(t *testing.T) {
	StatsDClient = statsd.NewNoop()
	mockApplicationDao := &storage.MockIApplicationDAO{}
	mockApplicationDao.On("Find", mock.Anything, mock.Anything, mock.Anything).
		Return(resources.SampleApplicationData(), nil)
	mockApplicationDao.On("Update", mock.Anything, mock.Anything).Return(nil)
	storage.ApplicationDao = mockApplicationDao

	mockOfferDao := &storage.MockIOfferDAO{}
	mockOfferDao.On("Find", mock.Anything, mock.Anything).
		Return(resources.SampleOfferData("PENDING_ACCEPTANCE", time.Now()), nil)
	mockOfferDao.On("Update", mock.Anything, mock.Anything).Return(nil)
	storage.OfferDao = mockOfferDao

	mockAppianClient := &mocks.Appian{}
	mockAppianClient.On("GetAccessToken", mock.Anything, mock.Anything).
		Return(&dto.GetAccessTokenResponse{AccessToken: "Token"}, nil)
	mockAppianClient.On("UpdateOfferDetails", mock.Anything, mock.Anything).
		Return(&dto.UpdateOfferDetailsResponse{}, nil)

	appConfig := &config.AppConfig{
		FeatureFlags: config.FeatureFlags{AppianFlag: true},
		ApplicationStatusTransitionKafkaConfig: &config.KafkaConfig{
			KafkaConfig: &sndconfig.KafkaConfig{},
			Enable:      true,
		},
	}

	t.Run("happy-path", func(t *testing.T) {
		registerApplicationStatusTransitionEvent("", &applicationstatustransition.ApplicationStatusTransitionImpl{AppianClient: mockAppianClient,
			Config: appConfig}, config.KafkaConfig{}, getInputChannel(
			&kafkareader.AckEntity{Event: &applicationStatusTransistionSchema.ApplicationStatusTransition{
				ToStatus:        "AS_REJECTED",
				ApplicationType: "AT_LENDING",
				ApplicationID:   "test-id",
				RejectionReason: "",
				Metadata:        nil,
				EventTime:       time.Now(),
			}}))
		err := wg.WaitForDone(time.Second * 4)
		require.Nil(t, err)
	})
	mock.AssertExpectationsForObjects(t)
}

func getInputChannel(event ...*kafkareader.AckEntity) <-chan *kafkareader.AckEntity {
	ch := make(chan *kafkareader.AckEntity, len(event))
	for _, data := range event {
		ch <- data
	}
	close(ch)
	return ch
}
