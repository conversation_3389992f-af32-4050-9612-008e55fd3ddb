package consumers

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/resources"
	incomeDerivationEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/income_derivation_event"

	"gitlab.myteksi.net/dakota/lending/common/constant"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

func Test_registerIncomeDerivationEvent(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		appCfg := &config.AppConfig{}
		impl := IncomeDerivationEventStreamImpl{AppConfig: appCfg, StatsD: statsd.NewNoop()}
		impl.registerIncomeDerivationEvent(getInputChannel(
			&kafkareader.AckEntity{Event: &incomeDerivationEvent.IncomeDerivationEvent{
				ReferenceID:        uuid.NewString(),
				ProductCode:        "productCode",
				ProductVariantCode: "productVariantCode",
				DocumentType:       incomeDerivationEvent.BankStatement,
				Status:             incomeDerivationEvent.Success,
				StatusReason:       incomeDerivationEvent.ReportVerified,
				UserDetails: &incomeDerivationEvent.UserDetails{
					Name: "userID",
				},
				Metadata: []byte("metadata"),
			}}))
		err := wg.WaitForDone(time.Second * 4)
		require.Nil(t, err)
	})
	mock.AssertExpectationsForObjects(t)
}

func Test_handleIncomeDerivationEvent(t *testing.T) {
	scenarios := []struct {
		desc          string
		data          *incomeDerivationEvent.IncomeDerivationEvent
		expectedError error
	}{
		{
			desc: "case1: workflow not register event",
			data: &incomeDerivationEvent.IncomeDerivationEvent{
				ReferenceID:   "ref-123",
				ApplicationID: "app-456",
				DocumentType:  incomeDerivationEvent.DocumentType("Salary Slip"),
				Status:        incomeDerivationEvent.Status("Approved"),
				StatusReason:  incomeDerivationEvent.StatusReason("Verified"),
				UserDetails: &incomeDerivationEvent.UserDetails{
					Name: "John Doe",
				},
				Metadata: jsonMarshalOrNil(dto.IncomeDerivationMetadata{
					IncomeAlert:            true,
					SixMonthsEPF:           true,
					PerfiosStatementStatus: "Success",
				}),
				IncomeAmount:         12345.67,
				IncomeDocumentSource: "Bank Statement",
				ProductVariantCode:   constant.FlexiLineOfCreditProductVariantCode,
			},
			expectedError: errors.New("workflow not registered"),
		},
		{
			desc: "case2: meta data is missing",
			data: &incomeDerivationEvent.IncomeDerivationEvent{
				ReferenceID:   "ref-123",
				ApplicationID: "app-456",
				DocumentType:  incomeDerivationEvent.DocumentType("Salary Slip"),
				Status:        incomeDerivationEvent.Status("Approved"),
				StatusReason:  incomeDerivationEvent.StatusReason("Verified"),
				UserDetails: &incomeDerivationEvent.UserDetails{
					Name: "John Doe",
				},
				IncomeAmount:         12345.67,
				IncomeDocumentSource: "Bank Statement",
			},
			expectedError: errors.New("error: Metadata is missing"),
		},
		{
			desc: "case3: userDetails is missing",
			data: &incomeDerivationEvent.IncomeDerivationEvent{
				ReferenceID:   "ref-123",
				ApplicationID: "app-456",
				DocumentType:  incomeDerivationEvent.DocumentType("Salary Slip"),
				Status:        incomeDerivationEvent.Status("Approved"),
				StatusReason:  incomeDerivationEvent.StatusReason("Verified"),
				Metadata: jsonMarshalOrNil(dto.IncomeDerivationMetadata{
					IncomeAlert:            true,
					SixMonthsEPF:           true,
					PerfiosStatementStatus: "Success",
				}),
				IncomeAmount:         12345.67,
				IncomeDocumentSource: "Bank Statement",
			},
			expectedError: errors.New("error: UserDetails is missing"),
		},
		{
			desc: "case4: ApplicationID is empty",
			data: &incomeDerivationEvent.IncomeDerivationEvent{
				ReferenceID:   "ref-123",
				ApplicationID: "",
				DocumentType:  incomeDerivationEvent.DocumentType("Salary Slip"),
				Status:        incomeDerivationEvent.Status("Approved"),
				StatusReason:  incomeDerivationEvent.StatusReason("Verified"),
				UserDetails: &incomeDerivationEvent.UserDetails{
					Name: "John Doe",
				},
				Metadata: jsonMarshalOrNil(dto.IncomeDerivationMetadata{
					IncomeAlert:            true,
					SixMonthsEPF:           true,
					PerfiosStatementStatus: "Success",
				}),
				IncomeAmount:         12345.67,
				IncomeDocumentSource: "Bank Statement",
			},
			expectedError: errors.New("error: ApplicationID is missing"),
		},
	}
	for _, tt := range scenarios {
		s := tt
		t.Run(s.desc, func(t *testing.T) {
			impl := IncomeDerivationEventStreamImpl{
				AppConfig: &config.AppConfig{},
				StatsD:    statsd.NewNoop(),
			}
			mockApplicationDAO := &storage.MockIApplicationDAO{}
			storage.ApplicationDao = mockApplicationDAO
			mockApplicationDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.SampleApplicationData(), nil).Once()
			consumerErr := impl.handleIncomeDerivationEvent(context.Background(), s.data)
			if s.expectedError == nil {
				assert.Nil(t, consumerErr)
			} else {
				assert.Equal(t, s.expectedError.Error(), consumerErr.Error())
			}
		})
	}
}

func TestMapIncomeDerivationStreamToDTO(t *testing.T) {
	ctx := context.Background()

	testCases := []struct {
		name         string
		input        *incomeDerivationEvent.IncomeDerivationEvent
		expected     *dto.IncomeDerivationStreamMessage
		shouldError  bool
		errorMessage error
	}{
		{
			name: "successfully maps income derivation event with complete data",
			input: &incomeDerivationEvent.IncomeDerivationEvent{
				ReferenceID:   "ref-123",
				ApplicationID: "app-456",
				DocumentType:  incomeDerivationEvent.DocumentType("Salary Slip"),
				Status:        incomeDerivationEvent.Status("Approved"),
				StatusReason:  incomeDerivationEvent.StatusReason("Verified"),
				UserDetails: &incomeDerivationEvent.UserDetails{
					Name: "John Doe",
				},
				Metadata: jsonMarshalOrNil(dto.IncomeDerivationMetadata{
					IncomeAlert:            true,
					SixMonthsEPF:           true,
					PerfiosStatementStatus: "Success",
				}),
				IncomeAmount:         12345.67,
				IncomeDocumentSource: "Bank Statement",
			},
			expected: &dto.IncomeDerivationStreamMessage{
				ReferenceID:   "ref-123",
				ApplicationID: "app-456",
				DocumentType:  incomeDerivationEvent.DocumentType("Salary Slip"),
				Status:        incomeDerivationEvent.Status("Approved"),
				StatusReason:  incomeDerivationEvent.StatusReason("Verified"),
				UserDetails: &dto.UserDetails{
					Name: "John Doe",
				},
				Metadata: &dto.IncomeDerivationMetadata{
					IncomeAlert:            true,
					SixMonthsEPF:           true,
					PerfiosStatementStatus: "Success",
				},
				IncomeAmount:         12345.67,
				IncomeDocumentSource: "Bank Statement",
			},
			shouldError: false,
		},
		{
			name: "handles empty metadata gracefully",
			input: &incomeDerivationEvent.IncomeDerivationEvent{
				ReferenceID:   "ref-124",
				ApplicationID: "app-457",
				DocumentType:  incomeDerivationEvent.DocumentType("Bank Statement"),
				Status:        incomeDerivationEvent.Status("Pending"),
				StatusReason:  incomeDerivationEvent.StatusReason("Awaiting Verification"),
				UserDetails: &incomeDerivationEvent.UserDetails{
					Name: "Jane Smith",
				},
				Metadata:             []byte{},
				IncomeAmount:         0,
				IncomeDocumentSource: "",
			},
			expected:     nil,
			shouldError:  true,
			errorMessage: jsonSyntaxError([]byte{}),
		},
		{
			name: "handles nil user details",
			input: &incomeDerivationEvent.IncomeDerivationEvent{
				ReferenceID:   "ref-125",
				ApplicationID: "app-458",
				DocumentType:  incomeDerivationEvent.DocumentType("Form 16"),
				Status:        incomeDerivationEvent.Status("Rejected"),
				StatusReason:  incomeDerivationEvent.StatusReason("Incomplete Document"),
				UserDetails:   nil,
				Metadata: jsonMarshalOrNil(dto.IncomeDerivationMetadata{
					IncomeAlert:            false,
					SixMonthsEPF:           false,
					PerfiosStatementStatus: "Failed",
				}),
				IncomeAmount:         999.99,
				IncomeDocumentSource: "Self Declaration",
			},
			expected: &dto.IncomeDerivationStreamMessage{
				ReferenceID:   "ref-125",
				ApplicationID: "app-458",
				DocumentType:  incomeDerivationEvent.DocumentType("Form 16"),
				Status:        incomeDerivationEvent.Status("Rejected"),
				StatusReason:  incomeDerivationEvent.StatusReason("Incomplete Document"),
				UserDetails:   nil,
				Metadata: &dto.IncomeDerivationMetadata{
					IncomeAlert:            false,
					SixMonthsEPF:           false,
					PerfiosStatementStatus: "Failed",
				},
				IncomeAmount:         999.99,
				IncomeDocumentSource: "Self Declaration",
			},
			shouldError: false,
		},
		{
			name: "fails on invalid metadata",
			input: &incomeDerivationEvent.IncomeDerivationEvent{
				ReferenceID:   "ref-126",
				ApplicationID: "app-459",
				Metadata:      []byte("invalid-metadata"),
			},
			expected:     nil,
			shouldError:  true,
			errorMessage: jsonSyntaxError([]byte("invalid-metadata")),
		},
		{
			name: "successfully maps income derivation event with bank statement report for biz",
			input: &incomeDerivationEvent.IncomeDerivationEvent{
				ReferenceID:   "ref-123",
				ApplicationID: "app-456",
				DocumentType:  incomeDerivationEvent.DocumentType("Salary Slip"),
				Status:        incomeDerivationEvent.Status("Approved"),
				StatusReason:  incomeDerivationEvent.StatusReason("Verified"),
				UserDetails: &incomeDerivationEvent.UserDetails{
					Name: "John Doe",
				},
				Metadata: jsonMarshalOrNil(dto.IncomeDerivationMetadata{
					IncomeAlert:            true,
					SixMonthsEPF:           true,
					PerfiosStatementStatus: "Success",
				}),
				IncomeAmount:         12345.67,
				IncomeDocumentSource: "Bank Statement",
				BankStatementReport:  []byte("statement-details"),
			},
			expected: &dto.IncomeDerivationStreamMessage{
				ReferenceID:   "ref-123",
				ApplicationID: "app-456",
				DocumentType:  incomeDerivationEvent.DocumentType("Salary Slip"),
				Status:        incomeDerivationEvent.Status("Approved"),
				StatusReason:  incomeDerivationEvent.StatusReason("Verified"),
				UserDetails: &dto.UserDetails{
					Name: "John Doe",
				},
				Metadata: &dto.IncomeDerivationMetadata{
					IncomeAlert:            true,
					SixMonthsEPF:           true,
					PerfiosStatementStatus: "Success",
				},
				IncomeAmount:         12345.67,
				IncomeDocumentSource: "Bank Statement",
				BankStatementReport:  []byte("statement-details"),
			},
			shouldError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := mapIncomeDerivationStreamToDTO(ctx, tc.input)
			assert.Equal(t, tc.errorMessage, err, tc.name)
			assert.Equal(t, tc.expected, result, tc.name)
		})
	}
}

// jsonSyntaxError ...
func jsonSyntaxError(Metadata []byte) error {
	incomeDerivationMetaData := dto.IncomeDerivationMetadata{}
	err := json.Unmarshal(Metadata, &incomeDerivationMetaData)
	return err
}

// Helper function to marshal struct to JSON or return nil if an error occurs
func jsonMarshalOrNil(v interface{}) []byte {
	data, err := json.Marshal(v)
	if err != nil {
		return nil
	}
	return data
}
