include:
  - project: gx-regional/dakota/backend/ci
    ref: master
    file: pipelines/go/service.yml
  - project: gx-regional/dakota/backend/ci
    ref: master
    file: pipelines/go/bersama-service.yml
  - project: gx-regional/dakota/backend/ci
    ref: master
    file: pipelines/database/bersama-mysql.yml
  - project: gx-regional/dakota/backend/ci
    ref: master
    file: pipelines/go/dbmy-service.yml
  - project: gx-regional/dakota/backend/ci
    ref: master
    file: pipelines/database/dbmy-mysql.yml
  - project: gx-regional/dakota/backend/ci
    ref: master
    file: pipelines/go/code-review.yml

variables:
  CMD_NAME: loan-app
  ECR_URI: 851255665500.dkr.ecr.ap-southeast-1.amazonaws.com/loan-app
  MODULE_PATH: loan-app
  BERSAMA_ECR_URI: 303083450960.dkr.ecr.ap-southeast-3.amazonaws.com/backend/loan-app
  DBMY_ECR_URI: 712221657655.dkr.ecr.ap-southeast-1.amazonaws.com/loan-app
  ENABLE_RACE_BUILD: "true"
  ENABLE_CODE_REVIEW: "true"
  GOLANG_VERSION: 1.24.3.20250701-bullseye
  GO_LINT_TAG: 1.24.2.9336-1.24.2
  GOFLAGS: -buildvcs=false
  KUBERNETES_MEMORY_REQUEST: '3Gi'
  KUBERNETES_MEMORY_LIMIT: '3Gi'

unit-test:
  variables:
    UNITTEST_GATE_COVERAGE: '73'