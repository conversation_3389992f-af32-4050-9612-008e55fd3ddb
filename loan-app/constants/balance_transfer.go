package constants

var (
	// DefaultBalanceTransferProcessingFeePercentage ...
	DefaultBalanceTransferProcessingFeePercentage = 1.35
	// DefaultBalanceTransferInterestRate ...
	DefaultBalanceTransferInterestRate = 0.0
	// DefaultBalanceTransferParametersTenor ...
	DefaultBalanceTransferParametersTenor = 4
)

const (
	// LoanAccountCreationBalanceTransferTemplate ...
	LoanAccountCreationBalanceTransferTemplate = "loanAccountCreationBalanceTransferEmailTemplate"
)

// TemplateParameter ...
type TemplateParameter string

var (
	// TemplateParameterBalanceTransferEIR ...
	TemplateParameterBalanceTransferEIR TemplateParameter = "balance_transfer_eir"
	// TemplateParameterBalanceTransferProcessingFee ...
	TemplateParameterBalanceTransferProcessingFee TemplateParameter = "balance_transfer_processing_fee"
	// TemplateParameterBalanceTransferInterestRate ...
	TemplateParameterBalanceTransferInterestRate TemplateParameter = "balance_transfer_interest_rate"
	// TemplateParameterTotalTenor ...
	TemplateParameterTotalTenor TemplateParameter = "total_tenor"
	// TemplateParameterMinTenor ...
	TemplateParameterMinTenor TemplateParameter = "min_tenor"
	// TemplateParameterMaxPrincipal ...
	TemplateParameterMaxPrincipal TemplateParameter = "max_principal"
)
