package constants

const (
	// LoanAccountBizCreationPushNotificationTemplate ...
	LoanAccountBizCreationPushNotificationTemplate = "bizLOCAccountSuccessPushNotificationTemplate"
	// LoanAccountBizCreationEmailTemplate ...
	LoanAccountBizCreationEmailTemplate = "bizLOCAccountSuccessEmailTemplate"
	// LoanAccountCreationTemplate ...
	LoanAccountCreationTemplate = "loanAccountCreationSuccessfulEmailTemplate"
	// LoanAccountSoftRejectionEmailTemplate ...
	LoanAccountSoftRejectionEmailTemplate = "loanAccountSoftRejectionEmailTemplate"
	// FlexiLoanCreationSuccessfulTemplate ...
	FlexiLoanCreationSuccessfulTemplate = "flexi_loan_creation_successful"
	// FlexiLoanCreationSoftRejectTemplate ...
	FlexiLoanCreationSoftRejectTemplate = "flexi_loan_creation_soft_reject"
	// FlexiLoanCreationHardRejectTemplate ...
	FlexiLoanCreationHardRejectTemplate = "flexi_loan_creation_hard_reject"
	// LoanAccountCreationAppTemplate ...
	LoanAccountCreationAppTemplate = "loanAccountCreationSuccessfulPushNotificationTemplate"
	// LoanAccountHardRejectAppTemplate ...
	LoanAccountHardRejectAppTemplate = "loanAccountHardRejectPushNotificationTemplate"
	// LoanAccountIncomeDocumentRequiredTemplate ...
	LoanAccountIncomeDocumentRequiredTemplate = "loanAccountIncomeDocumentRequiredTemplate"
	// LoanAccountHardRejectSMSTemplate ...
	LoanAccountHardRejectSMSTemplate = "loanAccountHardRejectSMSNotificationTemplate"
	// LoanAccountSoftRejectAppTemplate ...
	LoanAccountSoftRejectAppTemplate = "loanAccountSoftRejectPushNotificationTemplate"
	// LoanAccountFailedAppTemplate ...
	LoanAccountFailedAppTemplate = "loanAccountFailedPushNotificationTemplate"
	// LoanApplicationPendingAcceptanceTemplate ...
	LoanApplicationPendingAcceptanceTemplate = "loanApplicationPendingAcceptanceTemplate"
	// LoanApplicationExpiredTemplate ...
	LoanApplicationExpiredTemplate = "loanApplicationExpiredTemplate"
)

// Variable in Pigeon template
const (
	//TemplateID ...
	TemplateID = LoanAccountCreationTemplate
	// TemplateCreditLimit ...
	TemplateCreditLimit = "credit_limit"
	// TemplateUserName ...
	TemplateUserName = "user_name"
	// TemplateEffectiveInterestRate ...
	TemplateEffectiveInterestRate = "effective_interest_rate"
	// TemplateInterestRate ...
	TemplateInterestRate = "interest_rate"
	// TemplateAccountID ...
	TemplateAccountID = "account_id"
	//TemplateTenor ...
	TemplateTenor = "tenor"
)
