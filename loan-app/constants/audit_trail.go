package constants

import (
	"gitlab.myteksi.net/dakota/common/servicename"
)

// Constants used for audit trail events
//
//nolint:gosec
const (
	GrabNRICEvent                                          = "GrabNRICEvent"
	GrabPhoneNumberEvent                                   = "GrabPhoneNumberEvent"
	ApplicationCreatedEvent                                = "ApplicationCreatedEvent"
	CBSEvent                                               = "CBSEvent"
	LOCCreateEvent                                         = "LOCCreateEvent"
	OAAccountCheckEvent                                    = "OAAccountCheckEvent"
	UpdateOfferEvent                                       = "UpdateOfferEvent"
	CBSCEvent                                              = "CBSCEvent"
	ECSREvent                                              = "ECSREvent"
	EISEvent                                               = "EISEvent"
	IBSEvent                                               = "IBSEvent"
	SMENSEvent                                             = "SMENSEvent"
	MLScoringEvent                                         = "MLScoringEvent"
	PostIncomeDerivationMLScoringEvent                     = "PostIncomeDerivationMLScoringEvent"
	PreBureauCDEEvent                                      = "PreBureauCDEEvent"
	PostBureauCDEEvent                                     = "PostBureauCDEEvent"
	PostIncomeDerivationCDEEvent                           = "PostIncomeDerivationCDEEvent"
	GrabUENEvent                                           = "GrabUENEvent"
	GrabNewBRNEvent                                        = "GrabNewBRNEvent"
	GrabOldBRNEvent                                        = "GrabOldBRNEvent"
	EkybOldBrnEvent                                        = "EkybOldBrnEvent"
	SingtelUENEvent                                        = "SingtelUENEvent"
	CreateFinexusWhenUserAcceptedPurposeOfLoanEvent        = "CreateFinexusWhenUserAcceptedPurposeOfLoanEvent"
	UpdateFinexusWhenNameScreeningSuccessfulEvent          = "UpdateFinexusWhenNameScreeningSuccessfulEvent"
	UpdateFinexusWhenFundInSuccessfulAndLoanDisbursedEvent = "UpdateFinexusWhenFundInSuccessfulAndLoanDisbursedEvent"
	UpdateFinexusWhenNameScreeningFailedEvent              = "UpdateFinexusWhenNameScreeningFailedEvent"
	UpdateFinexusWhenApplicationExpiredEvent               = "UpdateFinexusWhenApplicationExpiredEvent"
	FinexusFailedEvent                                     = "FinexusFailedEvent"
	FinexusInitiatedEvent                                  = "FinexusInitiatedEvent"
	CTOSEvent                                              = "CTOSEvent"
	OnboardingRejectionEvent                               = "OnboardingRejectionEvent"
	UploadDocumentInitiatedEvent                           = "UploadDocumentInitiatedEvent"
	UploadDocumentEvent                                    = "UploadDocumentEvent"
	GenerateReportEvent                                    = "GenerateReportEvent"
	RetrievedPerfiosReportEvent                            = "RetrievedPerfiosReportEvent"
	PGLetterEvent                                          = "PGLetterEvent"

	VerdictFailed     = "FAILED"
	VerdictCompleted  = "COMPLETED"
	VerdictProcessing = "PROCESSING"

	CreateApplicationEventSource    = string(servicename.LoanApp)
	GrabEventSource                 = "Grab"
	EkybSource                      = "EkybService"
	WhiteListSource                 = "WhiteListService"
	CreditBureauSource              = "CreditBureau"
	AccountServiceSource            = "AccountService"
	ExperianSource                  = "ExperianAdapter"
	MlOpsSource                     = "MlOps"
	PostIncomeDerivationMlOpsSource = "PostIncomeDerivationMlOps"
	PreBureauCdeSource              = "PreBureauCde"
	PostBureauCdeSource             = "PostBureauCde"
	PostIncomeDerivationCdeSource   = "PostIncomeDerivationCde"
	CTOSSource                      = "CTOS"
	FinexusSource                   = "Finexus"
	OnbardingSource                 = "Onboarding"
	PerfiosSource                   = "Perfios"
)

// constants for workflowID based on country code for perfios audit trail ( event : UploadDocumentInitiatedEvent, UploadDocumentEvent, GenerateReportEvent)
const (
	PerfiosAuditMyWorkflowID = "biz_flexi_credit_app_my_workflow"
	PerfiosAuditSgWorkflowID = "biz_flexi_credit_app_sg_workflow"
)
