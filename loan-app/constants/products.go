package constants

import crDecisionEngAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"

var (
	//FlexiCardOnlyApplicationProductTypes product types in flexicard only application
	FlexiCardOnlyApplicationProductTypes = []crDecisionEngAPI.ProductType{crDecisionEngAPI.ProductType_FLEXI_CREDIT_CARD}

	// BundledApplicationProductTypes product types  in bundled application
	BundledApplicationProductTypes = []crDecisionEngAPI.ProductType{crDecisionEngAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT, crDecisionEngAPI.ProductType_FLEXI_CREDIT_CARD}

	// FlexiLoanApplicationProductType product type in FlexiLoan application
	FlexiLoanApplicationProductType = []crDecisionEngAPI.ProductType{crDecisionEngAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT}

	// BizFlexiCreditApplicationProductTypes product types in flexi credit application
	BizFlexiCreditApplicationProductTypes = []crDecisionEngAPI.ProductType{crDecisionEngAPI.ProductType_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT}
)
