package constants

import (
	applicationAPI "gitlab.myteksi.net/dakota/lending/external/applicationservice"
)

// IDTypeMap maps application ID types to their corresponding string representations.
var (
	IDTypeMap = map[string]string{
		applicationAPI.IDType_CITIZEN: "NRIC",
		applicationAPI.IDType_PR:      "NRIC",
		applicationAPI.IDType_QPASS:   "EMPL",
		applicationAPI.IDType_MWP:     "EMPL",
		applicationAPI.IDType_ENTRE:   "EMPL",
		applicationAPI.IDType_TEP:     "EMPL",
		applicationAPI.IDType_WHP:     "EMPL",
		applicationAPI.IDType_PEP:     "EMPL",
		applicationAPI.IDType_P1PASS:  "EMPL",
		applicationAPI.IDType_P2PASS:  "EMPL",
		applicationAPI.IDType_SPASS:   "EMPL",
		applicationAPI.IDType_RPASS:   "WORK",
	}
	ResidentialStatusMap = map[string]string{
		"CITIZEN": "C",
		"ALIEN":   "A",
		"PR":      "P",
		"UNKNOWN": "U",
	}
	EducationCodes = map[string]int64{
		"no_formal_education":        0,
		"primary":                    1,
		"lower_secondary":            2,
		"secondary":                  3,
		"post_secondary":             4,
		"diploma":                    5,
		"professional_qualification": 6,
		"bachelors":                  7,
		"post_graduate":              8,
		"masters_doctorate":          9,
		"modular_certification":      10,
	}
	HousingTypeCodes = map[string]int64{
		"DETACHED HOUSE":        121,
		"SEMI-DETACHED HOUSE":   122,
		"TERRACE HOUSE":         123,
		"CONDOMINIUM":           131,
		"EXECUTIVE CONDOMINIUM": 132,
		"APARTMENT":             139,
	}
	HdbTypeCodes = map[string]int64{
		"1-ROOM FLAT (HDB)":      111,
		"2-ROOM FLAT (HDB)":      112,
		"3-ROOM FLAT (HDB)":      113,
		"4-ROOM FLAT (HDB)":      114,
		"5-ROOM FLAT (HDB)":      115,
		"EXECUTIVE FLAT (HDB)":   116,
		"STUDIO APARTMENT (HDB)": 118,
	}
	SourceOfIncomeMap = map[string]string{
		"salaried":     "SA",
		"selfEmployed": "SE",
		"gig":          "GIG",
	}
)

// IDTypeMapping maps string representations of ID types to their corresponding application API constants.
var IDTypeMapping = map[string]string{
	"CITIZEN": applicationAPI.IDType_CITIZEN,
	"PR":      applicationAPI.IDType_PR,
	"RPass":   applicationAPI.IDType_RPASS,
	"SPass":   applicationAPI.IDType_SPASS,
	"P1Pass":  applicationAPI.IDType_P1PASS,
	"P2Pass":  applicationAPI.IDType_P2PASS,
	"QPass":   applicationAPI.IDType_QPASS,
	"PEP":     applicationAPI.IDType_PEP,
	"WHP":     applicationAPI.IDType_WHP,
	"TEP":     applicationAPI.IDType_TEP,
	"Entre":   applicationAPI.IDType_ENTRE,
	"DP":      applicationAPI.IDType_DP,
	"LTVP":    applicationAPI.IDType_LTVP,
	"LOC":     applicationAPI.IDType_LOC,
	"MWP":     applicationAPI.IDType_MWP,
	"OVE":     applicationAPI.IDType_OVE,
	"STP":     applicationAPI.IDType_STP,
	"LTVP+":   applicationAPI.IDType_LTVP_PLUS,
	"IEO":     applicationAPI.IDType_IEO,
}

// Field constants used in the application process.
const (
	FieldName                            = "name"
	FieldFamilyName                      = "familyName"
	FieldAlias                           = "aliasName"
	FieldHanyuPinyinName                 = "hanyuPinyinName"
	FieldGender                          = "gender"
	FieldMaritalStatus                   = "maritalStatus"
	FieldDateOfBirth                     = "dob"
	FieldNationality                     = "nationality"
	FieldCountryOfBirth                  = "countryOfBirth"
	FieldIDType                          = "idType"
	FieldIDNumber                        = "idNumber"
	FieldEmail                           = "email"
	FieldMobileNumber                    = "mobileNumber"
	FieldAddressCountry                  = "registeredAddress.country"
	FieldAddressStreet                   = "registeredAddress.streetName"
	FieldAddressBlock                    = "registeredAddress.blockNumber"
	FieldAddressUnit                     = "registeredAddress.unitNo"
	FieldAddressPostalCode               = "registeredAddress.postalCode"
	FieldIndustry                        = "industry"
	FieldEmployer                        = "employerName"
	FieldOccupation                      = "occupation"
	FieldTnC                             = "termsAndConditions"
	FieldMarketingConsent                = "marketingConsent"
	FieldThirdPartySharingConsent        = "thirdPartySharingConsent"
	FieldETBAcknowledgment               = "etbAcknowledgement"
	FieldAnnualIncome                    = "annualIncome"
	FieldTax                             = "taxField"
	FieldDeclareNotUSTaxResident         = "declareNotUSTaxResident"
	FieldPassportNumber                  = "passportNumber"
	FieldPassportExpiry                  = "passportExpiryDate"
	FieldIDExpiry                        = "finExpiryDate"
	FieldSourceOfFunds                   = "sourceOfFunds"
	FieldInterestRate                    = "interestRate"
	FieldGrabLendingMetadata             = "grabLendingMetadata"
	FieldSingtelLendingMetadata          = "SingtelLendingMetadata"
	FieldIsDefaultInterestRate           = "isDefaultRate"
	FieldEIR                             = "EIR"
	FieldBalanceTransfer                 = "balanceTransfer"
	FieldBalanceTransferIR               = "balanceTransferIR"
	FieldBalanceTransferEIR              = "balanceTransferEIR"
	FieldProcessingFee                   = "processingFee"
	FieldResidentialStatus               = "residentialStatus"
	FieldPassType                        = "passType"
	FieldRace                            = "race"
	FieldEducation                       = "education"
	FieldSourceOfIncome                  = "sourceOfIncome"
	FieldSideIncome                      = "declareSideIncome"
	FieldTypeOfHousing                   = "registeredAddress.typeOfHousing"
	FieldAssessOtherProducts             = "assessOtherProducts"
	FieldEmploymentLength                = "employmentLength"
	FieldYearsOfExperience               = "yearsOfExperience"
	FieldUen                             = "uen"
	FieldIncomeAndProperty               = "incomeAndProperty"
	FieldCpfDetails                      = "cpfDetails"
	FieldBusinessMobile                  = "businessMobile"
	FieldBusinessEmail                   = "businessEmail"
	FieldSanctionsActivity               = "sanctionsActivity"
	FieldBusinessPurpose                 = "businessPurpose"
	FieldBusinessAnnualIncome            = "businessAnnualIncome"
	FieldAnticipatedAccountActivity      = "anticipatedAccountActivity"
	FieldAnticipatedAmountPerTransaction = "anticipatedAmountPerTransaction"
	FieldBusinessSof                     = "businessSof"
	FieldDeclareSideIncome               = "declareSideIncome"
	InvalidPostalCode                    = "000000"
	ServiceIDDigibank                    = "DIGIBANK"
	PercentagePA                         = "% p.a."
	Handphone                            = "HANDPHONE"
	Residential                          = "RESIDENTIAL"
)

// MY specific fields - individual
const (
	FieldMykad                        = "mykad"
	FeildMailingAddress               = "mailingAddress"
	FieldAddress                      = "address"
	FieldReligion                     = "religion"
	FieldDocumentIssuingCountry       = "documentIssuingCountry"
	FieldEmploymentType               = "employmentType"
	FieldIsResident                   = "isResident"
	FieldIsRegisteredSSM              = "isRegisteredSSM"
	FieldDepositInsuranceScheme       = "depositInsuranceScheme"
	FieldAllowMarketingConsent        = "allowMarketingConsent"
	FieldAllowDataSharing             = "allowDataSharing"
	FieldAllowCreditBureauDataSharing = "allowCreditBureauDataSharing"
)

// MY specific fields - business
const (
	FieldBRN                         = "brn"
	FieldBizName                     = "bizName"
	FieldBizRegistrationNo           = "bizRegistrationNo"
	FieldBizNature                   = "bizNature"
	FieldBizOwnerName                = "bizOwnerName"
	FieldBizOwnerNric                = "bizOwnerNric"
	FieldBizAddressLine1             = "bizAddressLine1"
	FieldBizAddressLine2             = "bizAddressLine2"
	FieldBizAddressPostcode          = "bizAddressPostcode"
	FieldBizAddressState             = "bizAddressState"
	FieldBizBrandName                = "bizBrandName"
	FieldBizAccountCreatePurpose     = "bizAccountCreatePurpose"
	FieldBizAnnualSales              = "bizAnnualSales"
	FieldBizNoOfEmployee             = "bizNoOfEmployee"
	FieldBizEmail                    = "email"
	FieldBizCountryOfIncome          = "bizCountryOfIncome"
	FieldBizEstimatedMonthlyTrans    = "bizEstimatedMonthlyTrans"
	FieldBizEstimatedMonthlyTransAmt = "bizEstimatedMonthlyTransAmt"
)

// Field constants used in the application process.
const (
	LendingFormPaidForMonthFormat = "Jan 2006"
	CdePaidForMonthFormat         = "2006-01-02"
)

// Field constants used in the application process.
const (
	IncomeAndPropertyFieldKey         = "embeddedForm.incomeAndProperty.details"
	CpfFieldKey                       = "embeddedForm.cpfDetails.details"
	TitleCPFBalance                   = "CPF ACCOUNT BALANCE"
	TitleCPFHousingWithdrawalTitle    = "CPF HOUSING WITHDRAWAL"
	TitleCPFContributionHistory       = "CPF CONTRIBUTION HISTORY (UP TO 15 MONTHS)"
	NameOA                            = "Ordinary Account (OA)"
	NameSA                            = "Special Account (SA)"
	NameMA                            = "Medisave Account (MA)"
	NameRA                            = "Retirement Account (RA)"
	TitleNoticeOfAssessment           = "NOTICE OF ASSESSMENT (DETAILED, LAST 2 YEARS)"
	TitleProperty                     = "PROPERTY"
	NamePrincipalWithdrawal           = "Principal Withdrawal"
	NameAccuredInterest               = "Accured Interest Amount"
	NameMonthlyInstallmentAmt         = "Monthly installment Amount"
	NameCPFAllowedForProperty         = "Total Amount of CPF Allowed for Property"
	SubtitleAddress                   = "ADDRESS"
	NameStreet                        = "Street name"
	NameBlockNumber                   = "Block number"
	NameUnitNumber                    = "Unit number"
	NamePostalCode                    = "Postal code"
	NameCountry                       = "Country"
	NameForMonth                      = "For month"
	NamePaidOn                        = "Paid on"
	NameContributionTotal             = "Contribution total"
	NameEmployerName                  = "Employer Name"
	NameAssessibleIncome              = "Assessible income"
	NameEmployment                    = "Employment"
	NameTrade                         = "Trade"
	NameRent                          = "Rent"
	NameInterest                      = "Interest"
	SubtitleAssessibleIncomeBreakdown = "ASSESSIBLE INCOME BREAKDOWN"
	NameYOA                           = "Year of assessment"
	NameTOA                           = "Type of assessment"
	NamePrivatePropertyOwnership      = "Ownership of Private Residential Property"
	NameHDBMonthlyLoanInstallment     = "HDB Ownership - Monthly Loan Instalment"
	NameHDBOutstandingLoanInstallment = "HDB Ownership - Outstanding Loan Instalment"
)

// MYEmploymentKeyType ...
type MYEmploymentKeyType string

// MYEmploymentKeyType constants
const (
	PublicEmployeeKey  MYEmploymentKeyType = "embeddedForm.financial.publicEmployee"
	PrivateEmployeeKey MYEmploymentKeyType = "embeddedForm.financial.privateEmployee"
	SelfEmployedKey    MYEmploymentKeyType = "embeddedForm.financial.selfEmployed"
	UnemployedKey      MYEmploymentKeyType = "embeddedForm.financial.unemployed"
	RetiredKey         MYEmploymentKeyType = "embeddedForm.financial.retired"
	StudentKey         MYEmploymentKeyType = "embeddedForm.financial.student"
)

// MY form titles
const (
	PrivateEmployeeKeyTypeOccupation       = "occupation.privateEmployee"
	PrivateEmployeeKeyTypeEmployer         = "employer.privateEmployee"
	PrivateEmployeeKeyTypeNatureOfBusiness = "natureOfBusiness.privateEmployee"
	PrivateEmployeeKeyTypeMonthlyIncome    = "monthlyIncome.privateEmployee"

	PublicEmployeeKeyTypeOccupation       = "occupation.publicEmployee"
	PublicEmployeeKeyTypeEmployer         = "employer.publicEmployee"
	PublicEmployeeKeyTypeNatureOfBusiness = "natureOfBusiness.publicEmployee"
	PublicEmployeeKeyTypeMonthlyIncome    = "monthlyIncome.publicEmployee"

	SelfEmployedKeyTypeOccupation       = "occupation.selfEmployed"
	SelfEmployedKeyTypeEmployer         = "employer.selfEmployed"
	SelfEmployedKeyTypeNatureOfBusiness = "natureOfBusiness.selfEmployed"
	SelfEmployedKeyTypeMonthlyIncome    = "monthlyIncome.selfEmployed"
)
