package constants

const (
	// EligibilitySuccessStatusCode represents the status code for successful eligibility.
	EligibilitySuccessStatusCode = "EC000"

	// WhitelistStatusCode represents the status code for applicants in the whitelist.
	WhitelistStatusCode = "EC001"

	// CoolingPeriodStatusCode represents the status code for applicants in the cooling period.
	CoolingPeriodStatusCode = "EC002"

	// ApplicantApplicationIDInProgress represents the applicant have application ID in progress.
	ApplicantApplicationIDInProgress = "EC003"

	// ApplicantApplicationIDApproved represents the applicant have application ID in Approved.
	ApplicantApplicationIDApproved = "EC004"

	// ApplicantDefaultNotEligible defines a status when applicant is not eligible
	ApplicantDefaultNotEligible = "EC005"

	// ApplicantApplicationIDInPendingAcceptance defines a status when applicant is in pending acceptance
	ApplicantApplicationIDInPendingAcceptance = "EC006"

	// HardRejectType represents the reject type as a hard reject.
	HardRejectType = "HARD_REJECT"

	// SoftRejectType represents the reject type as a soft reject.
	SoftRejectType = "SOFT_REJECT"

	// MaxCoolingPeriodDays represents the maximum number of cooling period days.
	MaxCoolingPeriodDays = 55000
	// NotEligible ...
	NotEligible = "NOT_ELIGIBLE"
	// Eligible ...
	Eligible = "ELIGIBLE"

	// PROCESSING ...
	PROCESSING = "PROCESSING"

	// APPROVED ...
	APPROVED = "APPROVED"

	// FraudCheckV2StatusRejected ...
	FraudCheckV2StatusRejected = 2
)

const (
	// ApplicantPortfolioDetailsTable ...
	ApplicantPortfolioDetailsTable = "applicant_portfolio_details"

	// ApplicationRejectionDetailsTable ...
	ApplicationRejectionDetailsTable = "application_rejection_details"

	// ApplicantDecisionTable ...
	ApplicantDecisionTable = "applicant_decision"
)

const (
	// TimeunitDisplayDay ...
	TimeunitDisplayDay = "hari"
	// TimeunitDisplayMonth ...
	TimeunitDisplayMonth = "bulan"
	// TimeunitDisplayYear ...
	TimeunitDisplayYear = "tahun"
)
