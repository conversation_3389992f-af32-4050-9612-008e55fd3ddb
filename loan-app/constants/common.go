package constants

const (

	// ISOLayout ...
	ISOLayout = "2006-01-02"

	// ProductNotFoundErrorCode ...
	ProductNotFoundErrorCode = "3245"

	// DefaultTenor ...
	DefaultTenor int64 = 24

	// Authorization ...
	Authorization = "Authorization"

	// UserID ...
	UserID = "userID"

	// PreBureau ...
	PreBureau = "PRE_BUREAU"

	// PostBureau ...
	PostBureau = "POST_BUREAU"

	// FAILURE ...
	FAILURE = "FAILURE"

	// HygieneCheck ...
	HygieneCheck = "HYGIENE_CHECK"

	// Default ...
	Default = "Default"

	// DefaultCoolingPeriodInDays ...
	DefaultCoolingPeriodInDays = 30

	// MessageTimestampLayout ...
	MessageTimestampLayout = "02 Jan 2006, 03:04 PM"

	// DefaultPrincipalInCents ...
	DefaultPrincipalInCents = 1000000
	// RefApplicationID ...
	RefApplicationID = "RefApplicationID"

	// MONTH ...
	MONTH string = "MONTH"
	//ApplicationID ...
	ApplicationID = "ApplicationID"

	// BIF ...
	BIF = "BIF"
)

const (
	// YearMonthFormat ...
	YearMonthFormat = "2006-01"

	// Month ...
	Month = "month"

	// Year ...
	Year = "year"

	// MonthsInAYear ...
	MonthsInAYear = 12
)

var (
	// DefaultTimeZoneOffset ...
	DefaultTimeZoneOffset int64

	// DefaultTimeZone ...
	DefaultTimeZone string

	// DefaultCountryCode ...
	DefaultCountryCode string

	// DefaultCurrencyCode ...
	DefaultCurrencyCode string

	// CoolingPeriodMap ...
	CoolingPeriodMap map[string]int

	// StatusReasonToPercentageMap ...
	StatusReasonToPercentageMap map[string]int

	// EnableBalanceTransferFeatureFlag ...
	EnableBalanceTransferFeatureFlag bool

	// DefaultNotificationLanguage ...
	DefaultNotificationLanguage string

	// UploadDocumentAPITimeoutInSecs ...
	UploadDocumentAPITimeoutInSecs int64

	// EnableBizFlexiCredit ...
	EnableBizFlexiCredit bool

	// EnableBizBankStatementsUpload ...
	EnableBizBankStatementsUpload bool

	// EnableBIFForNotification ...
	EnableBIFForNotification bool

	// EnableCDEEligibleProgram ...
	EnableCDEEligibleProgram bool

	// EnableUtilisationTracker ...
	EnableUtilisationTracker bool

	// EnableGetECSRReportByID ...
	EnableGetECSRReportByID bool

	// EnablePrivateLimited ...
	EnablePrivateLimited bool

	// EnableActiveProfile ...
	EnableActiveProfile bool

	// ListLoanDocumentTypesTTLInMinutes ...
	ListLoanDocumentTypesTTLInMinutes int64

	// EnablePersonalBankStatementsUpload ...
	EnablePersonalBankStatementsUpload bool

	// EnableIDNumberEncryption ...
	EnableIDNumberEncryption bool

	// EnableSwitchToHashedIDNumber ...
	EnableSwitchToHashedIDNumber bool

	// EnableIDNumberHashShadow ...
	EnableIDNumberHashShadow bool
)

const (
	// GrabWhitelistRedisPrefix ...
	GrabWhitelistRedisPrefix = "GrabWhitelist"
)
