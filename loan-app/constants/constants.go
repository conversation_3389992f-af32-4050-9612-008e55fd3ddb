// Package constants ...
package constants

// Constants ...
const (
	// ApplicationFraudCheckDetailsTable ...
	ApplicationFraudCheckDetailsTable = "application_fraud_check_details"
)

const (

	// NoApplicationAvailableStatus ...
	NoApplicationAvailableStatus = "NO APPLICATION"

	// MSISDN ...
	MSISDN = "MSISDN"
)

// StatusSuccess ...
const (
	StatusSuccess = "SUCCESS"
)

// SourceOfIncome Service File Type ...
const (
	SourceOfIncome = "sourceOfIncome"
)

// Notification Params
const (
	// DateOfEligibility ...
	DateOfEligibility = "dateOfEligibility"

	// Username ...
	Username = "username"
)

const (
	// TimeunitDay ...
	TimeunitDay = "DAY"
	// TimeunitMonth ...
	TimeunitMonth = "MONTH"
	// TimeunitYear ...
	TimeunitYear = "YEAR"
)

// Http Headers
const (
	HeaderContentType = "Content-Type"
	BodyTypeMultipart = "multipart/form-data"
)

// CurrencyCodes
const (
	// CurrencyCodeSGD ...
	CurrencyCodeSGD = "SGD"
	// CurrencyCodeMYR ...
	CurrencyCodeMYR = "MYR"
)

// ReportType ...
type ReportType string

const (
	// ReportTypeSMENS ...
	ReportTypeSMENS ReportType = "SMENS"
	// ReportTypeCBSC ...
	ReportTypeCBSC ReportType = "CBSC"
	// ReportTypeEIS ...
	ReportTypeEIS ReportType = "EIS"
	// ReportTypeECSR ...
	ReportTypeECSR ReportType = "ECSR"
	// ReportTypeIBS ...
	ReportTypeIBS ReportType = "IBS"
)

// ProdEnv ...
const ProdEnv = "prod"

// EntityType ...
type EntityType string

const (
	// EntityTypeUEN ...
	EntityTypeUEN EntityType = "UEN"
	// EntityTypeNRIC ...
	EntityTypeNRIC EntityType = "NRIC"
)

// CallType ...
type CallType string

const (
	// CallTypePS2 ...
	CallTypePS2 CallType = "PS2"
	// CallTypePS11 ...
	CallTypePS11 CallType = "PS11"
	// CallTypePS ...
	CallTypePS CallType = "PS"
)
const (
	// FinexusApplication ...
	FinexusApplication = "Finexus_Application"
)

// NotifyType ...
type NotifyType string

const (
	// NotifyTypeCreate ...
	NotifyTypeCreate NotifyType = "CREATE"
	// NotifyTypeUpdate ...
	NotifyTypeUpdate NotifyType = "UPDATE"
)

// OnboardingStatusType ...
type OnboardingStatusType string

const (
	// OnboardingStatusApplicationExpired ...
	OnboardingStatusApplicationExpired OnboardingStatusType = "APPLICATION_EXPIRED"
	// OnboardingStatusUserAcceptPurposeOfLoan ...
	OnboardingStatusUserAcceptPurposeOfLoan OnboardingStatusType = "USER_ACCEPT_PURPOSE_OF_LOAN"
	// OnboardingStatusNameScreeningApproved ...
	OnboardingStatusNameScreeningApproved OnboardingStatusType = "NAME_SCREENING_APPROVED"
	// OnboardingStatusNameScreeningRejected ...
	OnboardingStatusNameScreeningRejected OnboardingStatusType = "NAME_SCREENING_REJECTED"
	// OnboardingStatusUserFundSuccessful ...
	OnboardingStatusUserFundSuccessful OnboardingStatusType = "USER_FUND_SUCCESSFUL"
	// OnboardingStatusUserFundExpired ...
	OnboardingStatusUserFundExpired OnboardingStatusType = "USER_FUND_EXPIRED"
)

const (
	// ProgramIEM ....
	ProgramIEM = "IEM"
)

const (
	ProductVariantCode      = "productVariantCode"
	BizApplicationID        = "applicationId"
	OnboardingApplicationID = "onboardingApplicationId"
	IdentificationNumber    = "identificationNumber" //cif or bif
)
