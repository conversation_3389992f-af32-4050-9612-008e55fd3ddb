package constants

// worker job scheduler ids
const (
	// ExpireLoanApplicationSchedulerID ...
	ExpireLoanApplicationSchedulerID            = "ExpireLoanApplicationScheduler"
	ExpirePendingDocLoanAppSchedulerSchedulerID = "ExpirePendingDocLoanAppScheduler"
	ByPassIncomeDerivationSchedulerID           = "BypassIncomeDerivationScheduler"
)

// worker job name
const (
	// ExpireLoanApplicationScheduler ...
	ExpireLoanApplicationScheduler = "expireLoanApplicationScheduler"
	// ExpirePendingDocumentLoanApplicationScheduler ...
	ExpirePendingDocumentLoanApplicationScheduler = "expirePendingDocumentLoanApplicationScheduler"
	// BypassIncomeDerivationScheduler ...
	BypassIncomeDerivationScheduler = "bypassIncomeDerivationScheduler"
)

// worker job status
const (
	// ProcessingJobStatus indicates job is currently running
	ProcessingJobStatus = "PROCESSING"
	// FailedJobStatus indicates that job ran failed
	FailedJobStatus = "FAILED"
	// CompletedJobStatus indicates job ran successfully
	CompletedJobStatus = "COMPLETED"
)

// job trigger types
const (
	// TriggeredByCron indicates if job is triggered by cron
	TriggeredByCron = "CRON"
)
