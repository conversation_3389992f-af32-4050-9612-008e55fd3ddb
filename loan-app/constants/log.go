// Package constants ...
package constants

// handler log tag
const (
	// CreateApplicationLogTag ...
	CreateApplicationLogTag = "handler.createApplicationLog"

	// CreateApplicationLogTagV2 ...
	CreateApplicationLogTagV2 = "handler.createApplicationLogV2"

	// GetApplicationLogTag ...
	GetApplicationLogTag = "handler.getApplicationLog"

	// GetApplicationByIdentifierLogTag ...
	GetApplicationByIdentifierLogTag = "handler.getApplicationByIdentifierLog"

	// UpdateApplicationLogTag ...
	UpdateApplicationLogTag = "handler.updateApplicationLog"

	// UpdateApplicationStatusLogTag ...
	UpdateApplicationStatusLogTag = "handler.updateApplicationStatusLog"

	// GetLoanOfferDetailsHandlerLogTag ...
	GetLoanOfferDetailsHandlerLogTag = "handler.getLoanOfferDetailsHandlerLog"

	// GetFlexiTermLoanApplicationProgressPercentageLogTag ...
	GetFlexiTermLoanApplicationProgressPercentageLogTag = "handler.GetFlexiTermLoanApplicationPrgPrc"

	// UpdateLoanOfferDetailsHandlerLogTag ...
	UpdateLoanOfferDetailsHandlerLogTag = "handler.updateLoanOfferDetailsHandlerLog"

	// UpdateFlexiCreditApplicationLogTag ...
	UpdateFlexiCreditApplicationLogTag = "handler.updateFlexiCreditApplicationLog"

	// GetApplicantEligibilityLogTag ...
	GetApplicantEligibilityLogTag = "handler.getApplicantEligibilityLog"

	// GetApplicantCoolingPeriodDetailsLogTag ...
	GetApplicantCoolingPeriodDetailsLogTag = "handler.getApplicantCoolingPeriodDetailsLogTag"

	// GetApplicantLoanPortfolioLogTag ...
	GetApplicantLoanPortfolioLogTag = "handler.getApplicantLoanPortfolioLog"

	// GetWhitelistApplicantListLogTag ...
	GetWhitelistApplicantListLogTag = "handler.GetWhitelistApplicantListLog"

	// CreateOrUpdateWhitelistApplicant ...
	CreateOrUpdateWhitelistApplicant = "handler.createOrUpdateWhitelistApplicant"

	// CreateFlexiCardApplicationLog
	CreateFlexiCardApplicationLog = "handler.createFlexiCardApplicationLog"

	// CreateBundledApplicationLog
	CreateBundledApplicationLog = "handler.createBundledApplicationLog"

	// GetApplicationByApplicationIDLogTag ...
	GetApplicationByApplicationIDLogTag = "handler.getApplicationByApplicationIDLog"

	// CreateOrUpdatePreSelectOfferDetailsLogTag ...
	CreateOrUpdatePreSelectOfferDetailsLogTag = "handler.createOrUpdatePreSelectOfferDetailsLog"

	// UpdateLoanApplicationDetailsLogTag ...
	UpdateLoanApplicationDetailsLogTag = "handler.updateLoanApplicationDetailsLog"

	// GetWhitelistHistoryApplicantLogTag ...
	GetWhitelistHistoryApplicantLogTag = "handler.getWhitelistHistoryApplicantLog"

	// GetFlexiTermLoanApplicationProgressPercentageLogTagV2 ...
	GetFlexiTermLoanApplicationProgressPercentageLogTagV2 = "handler.GetFlexiTermLoanApplicationPrgPrcV2"

	// InitiateDocumentUploadLogTag ...
	InitiateDocumentUploadLogTag = "handler.initiateDocumentUploadLog"

	// InitiateEpfLoginLogTag ...
	InitiateEpfLoginLogTag = "handler.initiateEpfLoginLog"

	// DocumentUploadLogTag ...
	DocumentUploadLogTag = "handler.documentUploadLog"

	// ConfirmDocumentUploadLogTag ...
	ConfirmDocumentUploadLogTag = "handler.confirmDocumentUploadLog"

	// ConfirmEpfLoginLogTag ...
	ConfirmEpfLoginLogTag = "handler.confirmEpfLoginLog"

	// GetApplicationsByApplicantIDLogTag ...
	GetApplicationsByApplicantIDLogTag = "handler.getApplicationsByApplicantIDLog"

	// CreateBizFlexiCreditApplicationLogTag
	CreateBizFlexiCreditApplicationLogTag = "handler.createBizFlexiCreditApplicationLog"

	// CreateLoanApplicationLogTag
	CreateLoanApplicationLogTag = "handler.createLoanApplicationLog"

	// GetAuditTrailLogTag
	GetAuditTrailLogTag = "handler.getAuditTrailLog"

	// PublishLoanApplicationLifeCycleEventLogTag ...
	PublishLoanApplicationLifeCycleEventLogTag = "handler.publishLoanApplicationLifeCycleEventLog"

	// GetApplicantEcoSystemDetailsLogTag
	GetApplicantEcoSystemDetailsLogTag = "handler.getApplicantEcoSystemDetailsLog"

	// GetDocumentTypesLogTag log tag for get document types api
	GetDocumentTypesLogTag = "handler.getDocumentTypesLog"

	// GetApplicationV2LogTag ...
	GetApplicationV2LogTag = "handler.getApplicationV2Log"
)

// external call log tags
const (
	// CustomerMasterLogTag ...
	CustomerMasterLogTag = "customerMasterLog"

	// ProductMasterLogTag ...
	ProductMasterLogTag = "productMasterLog"

	// AppianLogTag ...
	AppianLogTag = "AppianLogTag"

	// GrabLogTag ...
	GrabLogTag = "GrabLog"

	// AuditTrailLogTag ...
	AuditTrailLogTag = "AuditTrailLog"

	// AccountServiceLog ...
	AccountServiceLog = "AccountServiceLog"

	// OverarchingLimitLog ...
	OverarchingLimitLog = "OverarchingLimitLog"

	// GetActiveProfileIDLogTag ...
	GetActiveProfileIDLogTag = "getActiveProfileIDLog"
)

const (
	// LoanApplicationLifecycleKafkaPublisherLogTag ...
	LoanApplicationLifecycleKafkaPublisherLogTag = "publishers.LoanApplicationLifeCycleKafkaPublisher"

	// LoanApplicationNotificationLogTag ...
	LoanApplicationNotificationLogTag = "notifier.loanApplicationNotifierLog"

	// AccountCreationEmailNotificationLogTag ...
	AccountCreationEmailNotificationLogTag = "AccountCreationEmailNotificationLog"

	// EmailNotificationLogTag ...
	EmailNotificationLogTag = "EmailNotificationLog"
	// SMSNotificationLogTag ...
	SMSNotificationLogTag = "SMSNotificationLog"
	// AppNotificationLogTag ...
	AppNotificationLogTag = "AppNotificationLog"
)

// custom tag fields
const (
	// ReferenceIDTag ...
	ReferenceIDTag = "referenceID"

	// InitTxnReferenceIDTag ...
	InitTxnReferenceIDTag = "initTxnReferenceID"

	// OnboardingApplicationIDTag ...
	OnboardingApplicationIDTag = "onboardingApplicationID"

	// ApplicantIDTag ...
	ApplicantIDTag = "applicantID"

	// IdentifierTypeTag ...
	IdentifierTypeTag = "identifierType"

	// ApplicationIDTag ...
	ApplicationIDTag = "applicationID"

	// RejectedReasonTag ...
	RejectedReasonTag = "rejectedReason"
)

// cron job log tag
const (
	// JobSchedulerInitializationLogTag ...
	JobSchedulerInitializationLogTag = "workers.jobSchedulerInitializationLog"

	// ExpireLoanApplicationJobLogTag ...
	ExpireLoanApplicationJobLogTag = "expireLoanApplicationJobLog"

	// ExpirePendingDocLoanAppJobLogTag ...
	ExpirePendingDocLoanAppJobLogTag = "expirePendingDocLoanAppJobLog"

	// BypassIncomeDerivationJobLogTag ...
	BypassIncomeDerivationJobLogTag = "bypassIncomeDerivationJobLog"
)

// store log tag
const (
	// GetRequestDBStoreLogTag ...
	GetRequestDBStoreLogTag = "store.GetRequestLog"

	// CreateRequestDBStoreLogTag ...
	CreateRequestDBStoreLogTag = "store.CreateRequestLog"

	// UpdateRequestDBStoreLogTag ...
	UpdateRequestDBStoreLogTag = "store.UpdateRequestLog"

	// TransactionConnectionLogTag ...
	TransactionConnectionLogTag = "db.transactionConnectionLog"

	// GetDatabaseHandleLogTag ...
	GetDatabaseHandleLogTag = "db.getDatabaseHandleLog"
)

// kafka consumers log tags
const (
	// ApplicationStatusTransitionKafkaConsumerLogTag ...
	ApplicationStatusTransitionKafkaConsumerLogTag = "consumer.ApplicationStatusTransitionKafkaConsumerLog"
	// CreditBureauEnquiryKafkaConsumerLog ...
	CreditBureauEnquiryKafkaConsumerLog = "consumer.CreditBureauEnquiryKafkaConsumerLog"
	// CreditCardAccountCreationKafkaConsumerLogTag ...
	CreditCardAccountCreationKafkaConsumerLogTag = "consumer.CreditCardAccountCreationKafkaConsumerLog"
	// LOCAccountCreationEventConsumerLogTag ...
	LOCAccountCreationEventConsumerLogTag = "consumer.LOCAccountCreationEventConsumerLog"
	// IncomeDerivationEventKafkaConsumerLog ...
	IncomeDerivationEventKafkaConsumerLog = "consumer.IncomeDerivationEventKafkaConsumerLog"
	// LendingNameScreeningEventKafkaConsumerLog ...
	LendingNameScreeningEventKafkaConsumerLog = "consumer.LendingNameScreeningEventKafkaConsumerLog"
	// EcddVerdictEventKafkaConsumerLog ...
	EcddVerdictEventKafkaConsumerLog = "consumer.EcddVerdictEventKafkaConsumerLog"
	// FinexusUpdateKafkaConsumerLogTag ...
	FinexusUpdateKafkaConsumerLogTag = "consumer.FinexusUpdateKafkaConsumerLog"
	// LendingAccountCreationEventKafkaConsumerLog ...
	LendingAccountCreationEventKafkaConsumerLog = "consumer.LendingAccountCreationEventKafkaConsumerLog"
)

const (
	// KMSClientLogTag use only for kms package code
	KMSClientLogTag = "kms.KMSClientLog"
)
