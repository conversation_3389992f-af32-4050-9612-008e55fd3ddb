package constants

import (
	"net/http"
)

// CDERecommendedDecision cde recommended decision denoting success or failure
type CDERecommendedDecision string

const (
	// CDERecommendedDecisionApprove success status
	CDERecommendedDecisionApprove CDERecommendedDecision = "APPROVE"
	// CDERecommendedDecisionApproved success status
	CDERecommendedDecisionApproved CDERecommendedDecision = "APPROVED"
	// CDERecommendedDecisionRejected failure status
	CDERecommendedDecisionRejected CDERecommendedDecision = "REJECTED"
	// CDERecommendedDecisionRefer refer status
	CDERecommendedDecisionRefer CDERecommendedDecision = "REFER"
	// CDERecommendedDecisionKIV refer to KIV status
	CDERecommendedDecisionKIV CDERecommendedDecision = "KIV"
	// CDERecomendedDecisionDocUpload refer to DOCUPLOAD status
	CDERecomendedDecisionDocUpload CDERecommendedDecision = "DOCUPLOAD"
)

// CDEResponseStatus : credit-decision-engine response status
type CDEResponseStatus string

const (
	// CDEResponseStatusSuccess ...
	CDEResponseStatusSuccess CDEResponseStatus = "SUCCESS"
)

const (
	// CreditDecisionEngineFailedError ...
	CreditDecisionEngineFailedError = "credit decision engine failed"
)

// CdeRetryErrorCodes ...
var CdeRetryErrorCodes = map[int]bool{
	http.StatusRequestTimeout:      true,
	http.StatusInternalServerError: true,
	http.StatusBadGateway:          true,
	http.StatusServiceUnavailable:  true,
	http.StatusGatewayTimeout:      true,
}

// CDERecommendedDecisionMetadataKeyName ...
const (
	CDERecommendedDecisionMetadataKeyName = "cdeRecommendedDecision"
)
