package common

import (
	"context"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/responses"
	"gitlab.myteksi.net/dakota/klient/errorhandling"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	api "gitlab.myteksi.net/dakota/lending/external/corebanking/productmaster"
	mocks "gitlab.myteksi.net/dakota/lending/external/corebanking/productmaster/mocks"
)

func TestCheckForMandatoryParameters(t *testing.T) {
	resp := CheckForMandatoryParameters(map[string]string{}, MandatoryProductVariantParameters())
	expected := commonErr.BuildErrorResponse(http.StatusBadRequest, commonErr.ErrMissingMandatoryProductParameter.Code, fmt.Sprintf("%s: %s", commonErr.ErrMissingMandatoryProductParameter.Message, constants.MinCreditLimitAmount))
	assert.Equal(t, expected, resp)
}

func TestFetchProductVariantParameters(t *testing.T) {
	tests := []struct {
		name         string
		apiErr       error
		apiResp      *api.ListEffectiveProductVariantParametersResponse
		expectedErr  bool
		expectedResp map[string]string
	}{
		{
			name: "should return error if API returns product not found",
			apiErr: &errorhandling.Error{
				Code: constants.ProductNotFoundErrorCode,
			},
			expectedErr: true,
		},
		{
			name: "should return error if API returns Authorization error",
			apiErr: &errorhandling.Error{
				Code: constants.Authorization,
			},
			expectedErr: true,
		},
		{
			name:   "should return proper value if API returns proper response",
			apiErr: nil,
			apiResp: &api.ListEffectiveProductVariantParametersResponse{
				ProductVariantParameters: []api.ProductVariantParameter{{
					ParameterKey:   constants.MinCreditLimitAmount,
					ParameterValue: "1000",
				}},
			},
			expectedResp: map[string]string{"min_credit_limit_amount": "1000"},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			productMasterMock := mocks.ProductMaster{}
			productMasterMock.On("ListEffectiveProductVariantParameters", mock.Anything, mock.Anything).Return(tt.apiResp, tt.apiErr).Once()
			got, err := FetchProductVariantParameters(context.Background(), &productMasterMock, "productVariantCode")
			if tt.expectedErr {
				assert.Error(t, err)
			}
			assert.Equal(t, tt.expectedResp, got)
		})
	}
}

func TestGetInterestParametersByProductVariant(t *testing.T) {
	tests := []struct {
		name         string
		apiErr       error
		apiResp      *api.GetInterestParametersByProductVariantResponse
		expectedErr  bool
		expectedResp *api.LoanAccountInterestParameters
	}{
		{
			name:         "should return error if API returns product not found",
			expectedResp: nil,
			apiErr:       responses.SampleGetInterestParametersAPIFailureResponse(),
			expectedErr:  true,
		},
		{
			name:    "should return proper value if API returns proper response",
			apiErr:  nil,
			apiResp: responses.SampleGetInterestParametersByProductVariantAPIBalanceTransferSuccessResponse(),
			expectedResp: &api.LoanAccountInterestParameters{
				NormalInterest: []api.LoanAccountFlatInterest{
					{
						Rate:       1,
						Multiplier: 1,
						SlabType:   "MONTH",
						FromUnit:   "1",
						ToUnit:     "3",
					},
				},
				PenalInterest: []api.LoanAccountFlatInterest{
					{
						Rate:       1,
						Multiplier: 1,
						SlabType:   "MONTH",
						FromUnit:   "1",
						ToUnit:     "3",
					},
				},
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			productMasterMock := mocks.ProductMaster{}
			productMasterMock.On("GetInterestParametersByProductVariant", mock.Anything, mock.Anything).Return(tt.apiResp, tt.apiErr).Once()
			got, err := GetInterestParametersByProductVariant(context.Background(), &productMasterMock, "productVariantCode")
			if tt.expectedErr {
				assert.Error(t, err)
			}
			assert.Equal(t, tt.expectedResp, got)
		})
	}
}

func TestListProcessingFeeParametersByProductVariant(t *testing.T) {
	tests := []struct {
		name         string
		apiErr       error
		apiResp      *api.ListFeeParametersByProductVariantResponse
		expectedErr  bool
		expectedResp map[string]api.FeeParameters
	}{
		{
			name:         "should return error if API returns product not found",
			expectedResp: nil,
			apiErr:       responses.SampleListFeeParametersAPIFailureResponse(),
			expectedErr:  true,
		},
		{
			name:    "should return proper value if API returns proper response",
			apiErr:  nil,
			apiResp: responses.SampleListFeeParametersAPISuccessResponse(),
			expectedResp: map[string]api.FeeParameters{
				"processingFee": {
					Type: "PERCENTAGE",
					SlabRates: []api.FeeSlabRate{
						{
							SlabType: "MONTH",
							FromUnit: "3",
							ToUnit:   "4",
							Value:    "1",
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			productMasterMock := mocks.ProductMaster{}
			productMasterMock.On("ListFeeParametersByProductVariant", mock.Anything, mock.Anything).Return(tt.apiResp, tt.apiErr).Once()
			got, err := ListFeeParametersByProductVariant(context.Background(), &productMasterMock, "productVariantCode")
			if tt.expectedErr {
				assert.Error(t, err)
			}
			assert.Equal(t, tt.expectedResp, got)
		})
	}
}
