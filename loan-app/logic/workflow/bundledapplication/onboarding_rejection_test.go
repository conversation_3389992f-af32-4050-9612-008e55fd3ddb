package bundledapplication

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	loanAppAPI "gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/requests"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	"gitlab.myteksi.net/dakota/workflowengine"
)

func Test_persistOnboardingEvent(t *testing.T) {
	errDummySimulateError := errors.New("simulate error")
	scenarios := []struct {
		desc          string
		workflowState workflowengine.State
		streamMessage interface{}
		errParams     error
		expectedErr   error
	}{
		{
			desc:          "error stream message",
			errParams:     errDummySimulateError,
			expectedErr:   common.ErrInvalidApplicationTransitionDtoPassed,
			streamMessage: "Stream Message",
		},
		{
			desc:          "successfully persisted",
			workflowState: stOnboardingEventPersisted,
			streamMessage: &applicationStatusTransistionSchema.ApplicationStatusTransition{},
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			w := WorkflowImpl{}
			resp, err := w.persistOnboardingEvent(context.Background(), "", &ExecutionData{
				CreateBundledApplicationRequest: requests.SampleCreateBundledApplicationRequest(requests.SampleBundledApplicationMessage()),
				ApplicationData: map[string]*storage.Application{
					string(loanAppAPI.ProductType_FLEXI_CREDIT_CARD):         {ApplicationID: "application-id-card"},
					string(loanAppAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT): {ApplicationID: "application-id-loan"},
				},
			}, scenario.streamMessage)
			if scenario.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, scenario.expectedErr, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, scenario.workflowState, resp.GetState())
			}
		})
	}
}

func Test_handleOnboardingRejection(t *testing.T) {
	scenarios := []struct {
		desc           string
		offeredProduct loanAppAPI.ProductType
		expectedErr    error
		updateErr      error
		expectedState  workflowengine.State
	}{
		{
			desc:           "error while updating the application",
			offeredProduct: loanAppAPI.ProductType_FLEXI_CREDIT_CARD,
			updateErr:      errDummy,
			expectedErr:    errDummy,
		},
		{
			desc:           "error while updating the application",
			offeredProduct: loanAppAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
			updateErr:      errDummy,
			expectedErr:    errDummy,
		},
		{
			desc:           "happy path",
			offeredProduct: loanAppAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
			expectedState:  stOnboardingChecksRejected,
		},
	}
	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			mockDatabaseStore := &storage.MockDatabaseStore{}
			mockDatabaseStore.On("ExecuteTransaction", mock.Anything, mock.Anything, mock.Anything).Return(scenario.updateErr)

			w := WorkflowImpl{
				Store:                            mockDatabaseStore,
				AppConfig:                        &config.AppConfig{},
				BundledApplicationQueryGenerator: &SGBundledQueryGeneratorImpl{},
			}
			var applicationID string
			if scenario.offeredProduct == loanAppAPI.ProductType_FLEXI_CREDIT_CARD {
				applicationID = "application-id-card"
			} else if scenario.offeredProduct == loanAppAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT {
				applicationID = "application-id-loan"
			}
			resp, err := w.handleOnboardingRejection(context.Background(), "", &ExecutionData{
				OfferedProduct:                  scenario.offeredProduct,
				CreateBundledApplicationRequest: requests.SampleCreateBundledApplicationRequest(requests.SampleBundledApplicationMessage()),
				ApplicationData: map[string]*storage.Application{
					string(loanAppAPI.ProductType_FLEXI_CREDIT_CARD):         {OnboardingApplicationID: "application-id-card"},
					string(loanAppAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT): {OnboardingApplicationID: "application-id-loan"},
				},
				ApplicationStatusTransition: &applicationStatusTransistionSchema.ApplicationStatusTransition{
					ApplicationID: applicationID,
				},
			}, nil)
			if scenario.expectedErr != nil {
				assert.Nil(t, resp)
				assert.Equal(t, scenario.expectedErr.Error(), err.Error())
			} else {
				assert.Nil(t, err)
				assert.Equal(t, scenario.expectedState, resp.GetState())
			}
		})
	}
}
