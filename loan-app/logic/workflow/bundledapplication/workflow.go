package bundledapplication

import (
	"context"
	"fmt"

	creditDecisionEngineAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	mlScoringServiceAPI "gitlab.com/gx-regional/dakota/lending/loan-app/external/mlscoringservice/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/kafka/publishers"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	logicAnalyserFactory "gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans/factory"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	digicardCoreAPI "gitlab.myteksi.net/dakota/digicard/digicard-core/api"
	creditBureauServiceAPI "gitlab.myteksi.net/dakota/lending/credit-bureau/api"
	accountServiceAPI "gitlab.myteksi.net/dakota/lending/external/corebanking/accountservice"
	grabAPI "gitlab.myteksi.net/dakota/lending/external/grab/api"
	"gitlab.myteksi.net/dakota/lending/external/notification"
	customerMasterAPI "gitlab.myteksi.net/dakota/lending/external/onboarding/customermaster"
	LoanCoreClient "gitlab.myteksi.net/dakota/lending/loan-core/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	we "gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"
)

const (
	workflowID                             = "bundled_application_workflow"
	createBundledApplicationWorkflowLogTag = "createBundledApplicationWorkflowLog"
	// BundledApplicationWorkflowID should always be same as workflowID but public
	BundledApplicationWorkflowID = "bundled_application_workflow"
)

const (
	stInit = we.StateInit

	stCreateApplicationSuccess = we.State(200)

	stAggregateEcosystemDataSuccess = we.State(205)

	stPreBureauCDEProcessing        = we.State(210)
	stPreBureauCDEApproved          = we.State(211)
	stPreBureauCDERejected          = we.State(510)
	stPreBureauCDERejectedPublished = we.State(213)

	stFetchCreditBureauDataProcessing = we.State(215)
	stFetchCreditBureauDataCompleted  = we.State(216)
	stFetchCreditBureauDataFailure    = we.State(515)

	stFetchScoringModelDataSuccess = we.State(220)

	stPostBureauCDEProcessing = we.State(225)
	stPostBureauCDEApproved   = we.State(226)
	stPostBureauCDERejected   = we.State(525)

	stPostBureauCDEApprovedPublished = we.State(235)
	stPostBureauCDERejectedPublished = we.State(236)
	stOnboardingEventPersisted       = we.State(237)
	stOnboardingChecksRejected       = we.State(538)

	stPersistCustomerResponse            = we.State(240)
	stCustomerAcceptedLoanOffer          = we.State(241)
	stCustomerAcceptedCardOffer          = we.State(242)
	stCustomerDeclinedLoanOffer          = we.State(541)
	stCustomerDeclinedCardOffer          = we.State(542)
	stCustomerDeclinedLoanOfferPublished = we.State(543)

	stFlexiLimitAccountCreationProcessing       = we.State(245)
	stFlexiLimitAccountCreationPersisted        = we.State(246)
	stFlexiLimitAccountCreationSuccess          = we.State(247)
	stFlexiLimitAccountCreationFailed           = we.State(547)
	stFlexiLimitAccountCreationSuccessPublished = we.State(248)
	stApplicantsNotified                        = we.State(249)

	stFlexiCardAccountCreationProcessing = we.State(250)
	stFlexiCardAccountResponsePersisted  = we.State(251)
	stFlexiCardAccountCreationSuccess    = we.State(252)
	stFlexiCardAccountCreationFailed     = we.State(550)

	stFlexiCardCreationSuccess                        = we.State(260)
	stFlexiCardCreationFailed                         = we.State(261)
	stUpdateApplicationStatusFlexiCardCreationSuccess = we.State(262)
	stUpdateApplicationStatusFlexiCardCreationFailed  = we.State(263)

	stUpdateFlexiCardAccountStatus          = we.State(265)
	stUpdateFlexiCardAccountStatusPublished = we.State(266)

	stOverarchingAccCreationInitiated = we.State(270)
	stOverarchingCallbackReceived     = we.State(274)
	stOverarchingAccCheckPassed       = we.State(276)

	stInterventionNeeded               = we.State(500)
	stOverarchingAccountCreationFailed = we.State(526)

	stUpdateCDEWithCustomerAcceptance = we.State(900)
)

const (
	evNoNeed                             = we.EventNoNeed
	evCreateBundledApplication           = we.Event(110)
	evStartAsyncWorkflow                 = we.Event(120)
	evPersistCreditBureauData            = we.Event(130)
	evOnboardingRejected                 = we.Event(140)
	evCustomerOfferResponse              = we.Event(150)
	evUpdateOfferResumeAsyncWorkflow     = we.Event(151)
	evFlexiLimitAccountCreationSuccess   = we.Event(160)
	evPersistFlexiCardAccountResponse    = we.Event(170)
	evOverarchingAccountCreationCallback = we.Event(180)
	evResumeOverarchingCreation          = we.Event(190)
)

var (
	wfInit            = we.InitExecution
	wfGet             = we.GetExecution
	wfExecute         = we.Execute
	errInvalidContext = common.ErrInvalidContext
)

// WorkflowImpl bundled flow workflow impl
type WorkflowImpl struct {
	AppConfig                        *config.AppConfig                     `inject:"config"`
	BundledApplicationQueryGenerator IBundledApplicationQueryGenerator     `inject:"bundledApplicationQueryGenerator"`
	Store                            storage.DatabaseStore                 `inject:"DBStore"`
	GrabClient                       grabAPI.Grab                          `inject:"client.grabAPI"`
	CreditDecisionEngine             creditDecisionEngineAPI.CrDecisionEng `inject:"client.creditDecisionEng"`
	CreditBureauServiceClient        creditBureauServiceAPI.CreditBureau   `inject:"client.creditBureauService"`
	MlScoringServiceClient           mlScoringServiceAPI.MlScoringService  `inject:"client.mlScoringService"`
	LoanAppLifecycleEventPublisher   publishers.Publisher                  `inject:"publishers.loanAppLifecycleEvent"`
	AccountServiceClient             accountServiceAPI.AccountService      `inject:"client.accountService"`
	DigicardCoreClient               digicardCoreAPI.DigicardCore          `inject:"client.digicardCore"`
	CustomerMasterClient             customerMasterAPI.CustomerMaster      `inject:"client.customerMaster"`
	NotificationService              notification.NotificationService      `inject:"client.notificationService"`
	LogicAnalyser                    logicAnalyserFactory.LogicAnalyser    `inject:"logic.AnalyserFactory"`
	LoanCoreClient                   LoanCoreClient.LoanCore               `inject:"client.loanCoreClient"`
}

// Register : register bundled application workflow
// nolint: funlen
func (w *WorkflowImpl) Register() {
	bundledApplication := we.NewWorkflow(workflowID, func() we.ExecutionData {
		return &ExecutionData{}
	})
	var transactionalRetryOption *we.TransitionOptions
	if retryOption := w.AppConfig.WorkflowRetryConfig.BundledApplication.TransactionalRetryOption; retryOption != nil {
		// used for time-sensitive transactional states
		transactionalRetryOption = &we.TransitionOptions{RetryPolicy: &we.RetryPolicy{
			Interval:    retryOption.IntervalInSeconds,
			MaxAttempts: retryOption.MaxAttempt}}
	}

	// Create application for loan and card
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stInit, evCreateBundledApplication, w.persistDB, nil, stCreateApplicationSuccess)

	// Fetch ecosystem data
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stCreateApplicationSuccess, evStartAsyncWorkflow, w.fetchEcosystemData, transactionalRetryOption, stAggregateEcosystemDataSuccess)

	// Pre bureau checks
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stAggregateEcosystemDataSuccess, evNoNeed, w.preBureauCDECheck, transactionalRetryOption, stPreBureauCDEProcessing)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stPreBureauCDEProcessing, evNoNeed, w.completePreBureauCDECheck, nil, stPreBureauCDEApproved, stPreBureauCDERejected, stInterventionNeeded)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stPreBureauCDERejected, evNoNeed, w.publishStatusToOnboarding, transactionalRetryOption, stPreBureauCDERejectedPublished)

	// Fetch credit bureau data
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stPreBureauCDEApproved, evNoNeed, w.fetchCreditBureauData, transactionalRetryOption, stFetchCreditBureauDataProcessing)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stFetchCreditBureauDataProcessing, evPersistCreditBureauData, w.persistCreditBureauData, nil, stFetchCreditBureauDataCompleted, stFetchCreditBureauDataFailure)

	// Application scoring
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stFetchCreditBureauDataCompleted, evNoNeed, w.fetchScoringModelData, transactionalRetryOption, stFetchScoringModelDataSuccess)

	// Post credit bureau
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stFetchScoringModelDataSuccess, evNoNeed, w.postBureauCDECheck, transactionalRetryOption, stPostBureauCDEProcessing)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stPostBureauCDEProcessing, evNoNeed, w.completePostBureauCDECheck, nil, stPostBureauCDEApproved, stPostBureauCDERejected, stInterventionNeeded)

	// publish post bureau events (onboarding checks)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stPostBureauCDERejected, evNoNeed, w.publishStatusToOnboarding, transactionalRetryOption, stPostBureauCDERejectedPublished)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stPostBureauCDEApproved, evNoNeed, w.publishStatusToOnboarding, transactionalRetryOption, stPostBureauCDEApprovedPublished)

	// handle onboarding rejection
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stPostBureauCDEApprovedPublished, evOnboardingRejected, w.persistOnboardingEvent, nil, stOnboardingEventPersisted)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stOnboardingEventPersisted, evNoNeed, w.handleOnboardingRejection, nil, stOnboardingChecksRejected)

	// customer response
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stPostBureauCDEApprovedPublished, evCustomerOfferResponse, w.persistCustomerResponse, nil, stPersistCustomerResponse)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stPersistCustomerResponse, evUpdateOfferResumeAsyncWorkflow, w.handleCustomerResponse, nil, stCustomerAcceptedCardOffer, stCustomerAcceptedLoanOffer, stCustomerDeclinedLoanOffer, stCustomerDeclinedCardOffer)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stCustomerDeclinedLoanOffer, evNoNeed, w.publishStatusToOnboarding, transactionalRetryOption, stCustomerDeclinedLoanOfferPublished)

	// Create Overarching account
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stCustomerAcceptedLoanOffer, evNoNeed, w.createOverarchingAccount, transactionalRetryOption, stOverarchingAccCreationInitiated, stOverarchingAccCheckPassed)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stOverarchingAccCreationInitiated, evOverarchingAccountCreationCallback, w.persistOverarchingAccountCreationResponse, nil, stOverarchingCallbackReceived)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stCustomerAcceptedLoanOffer, evOverarchingAccountCreationCallback, w.persistOverarchingAccountCreationResponse, nil, stOverarchingCallbackReceived)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stOverarchingCallbackReceived, evResumeOverarchingCreation, w.completeOverarchingAccountCreation, nil, stOverarchingAccCheckPassed, stOverarchingAccountCreationFailed)

	// create LOC account
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stOverarchingAccCheckPassed, evNoNeed, w.createFlexiLimitAccount, transactionalRetryOption, stFlexiLimitAccountCreationProcessing)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stFlexiLimitAccountCreationProcessing, evFlexiLimitAccountCreationSuccess, w.persistFlexiLimitAccountCreationResponse, nil, stFlexiLimitAccountCreationPersisted)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stFlexiLimitAccountCreationPersisted, evNoNeed, w.completeFlexiLimitAccountCreation, nil, stFlexiLimitAccountCreationSuccess, stFlexiLimitAccountCreationFailed)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stFlexiLimitAccountCreationSuccess, evNoNeed, w.publishStatusToOnboarding, transactionalRetryOption, stFlexiLimitAccountCreationSuccessPublished)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stFlexiLimitAccountCreationSuccessPublished, evNoNeed, w.sendLOCCreatedNotificationToApplicants, nil, stApplicantsNotified)

	// create flexicard account
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stCustomerAcceptedCardOffer, evNoNeed, w.createFlexicardAccount, transactionalRetryOption, stFlexiCardAccountCreationProcessing)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stFlexiCardAccountCreationProcessing, evPersistFlexiCardAccountResponse, w.persistFlexiCardAccountCreationResponse, nil, stFlexiCardAccountResponsePersisted)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stFlexiCardAccountResponsePersisted, evNoNeed, w.completeFlexiCardAccountCreation, nil, stFlexiCardAccountCreationSuccess, stFlexiCardAccountCreationFailed)

	// create virtual + physical card
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stFlexiCardAccountCreationSuccess, evNoNeed, w.createFlexiCard, transactionalRetryOption, stFlexiCardCreationSuccess, stFlexiCardCreationFailed, stInterventionNeeded)

	// update application status after virtual card creation
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stFlexiCardCreationSuccess, evNoNeed, w.updateApplicationStatus, transactionalRetryOption, stUpdateApplicationStatusFlexiCardCreationSuccess)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stFlexiCardCreationFailed, evNoNeed, w.updateApplicationStatus, transactionalRetryOption, stUpdateApplicationStatusFlexiCardCreationFailed)

	// update flexi card account status (pending activation to active)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stUpdateApplicationStatusFlexiCardCreationSuccess, evNoNeed, w.updateFlexiCardAccountStatus, transactionalRetryOption, stUpdateFlexiCardAccountStatus)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stUpdateFlexiCardAccountStatus, evNoNeed, w.publishStatusToOnboarding, transactionalRetryOption, stUpdateFlexiCardAccountStatusPublished)

	//Update cde with customer acceptance
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stApplicantsNotified, evNoNeed, w.updateCDEWithCustomerAcceptance, transactionalRetryOption, stUpdateCDEWithCustomerAcceptance)
	bundledApplication.AddTransition(createBundledApplicationWorkflowLogTag, stUpdateFlexiCardAccountStatusPublished, evNoNeed, w.updateCDEWithCustomerAcceptance, transactionalRetryOption, stUpdateCDEWithCustomerAcceptance)

	we.RegisterWorkflow(bundledApplication)
}

// ConsumeCBSData consumes event from credit-bureau event stream and resumes workflow for bundled application
func ConsumeCBSData(ctx context.Context, cbsDataDTO *dto.CreditBureauEnquiryStreamMessage) error {
	_, err := wfExecute(ctx, we.Execution{
		WorkflowID:     workflowID,
		TransitionID:   cbsDataDTO.ReferenceID,
		ExecutionEvent: evPersistCreditBureauData,
	}, cbsDataDTO)
	if err != nil {
		slog.FromContext(ctx).Warn(createBundledApplicationWorkflowLogTag, fmt.Sprintf("Workflow execute error in ConsumeCBSData: %s", err.Error()))
		return utils.CheckIfResourceNotFound(ctx, err, createBundledApplicationWorkflowLogTag, cbsDataDTO.ReferenceID)
	}
	return nil
}

// ConsumeLendingAccountCreationData consumes event from account-service event stream and resumes workflow for bundled application
func ConsumeLendingAccountCreationData(ctx context.Context, lendingAccCreationDTO *dto.LendingAccountCreationDTO) error {
	nextExecData, consumeErr := wfExecute(ctx, we.Execution{
		WorkflowID:     workflowID,
		TransitionID:   lendingAccCreationDTO.ReferenceID,
		ExecutionEvent: evOverarchingAccountCreationCallback,
	}, lendingAccCreationDTO)
	if consumeErr != nil {
		slog.FromContext(ctx).Warn(createBundledApplicationWorkflowLogTag, fmt.Sprintf("Workflow execute error in ConsumeOverarchingAccountCreationEvent: %s", consumeErr.Error()))
		return utils.CheckIfResourceNotFound(ctx, consumeErr, createBundledApplicationWorkflowLogTag, lendingAccCreationDTO.ReferenceID)
	}

	// execute async processing
	currCtx, ok := nextExecData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(createBundledApplicationWorkflowLogTag, "Invalid context passed in ConsumeOverarchingAccountCreationEvent ")
		return nil
	}
	slog.FromContext(ctx).Info(createBundledApplicationWorkflowLogTag, fmt.Sprintf("starting async workflow execution in ConsumeOverarchingAccountCreationEvent for referenceID: %s", lendingAccCreationDTO.ReferenceID), utils.GetTraceID(ctx))
	newCtx := utils.NewCtxWithLoggerAndSpan(ctx)
	gconcurrent.Go(newCtx, createBundledApplicationWorkflowLogTag, utils.ExecuteEventAsync(currCtx.CreateBundledApplicationRequest.Message.OnboardingApplicationID, evResumeOverarchingCreation, createBundledApplicationWorkflowLogTag, workflowID))
	return nil
}

// ConsumeCreditCardAccountCreationStream consumes event from account service flexicard creation stream and resumes workflow for bundle application
// nolint: dupl
func ConsumeCreditCardAccountCreationStream(ctx context.Context, data *dto.CreditCardAccountCreationStreamMessage) error {
	execData, err := wfGet(ctx, we.Execution{
		WorkflowID: workflowID,
		RequestID:  data.ReferenceID,
	})

	if err != nil {
		slog.FromContext(ctx).Warn(createBundledApplicationWorkflowLogTag, fmt.Sprintf("Error when getting existing workflow: %s", err.Error()))
		return err
	}

	if execData.GetState() == stFlexiCardAccountCreationProcessing {
		_, err := wfExecute(ctx, we.Execution{
			WorkflowID:     workflowID,
			RequestID:      data.ReferenceID,
			ExecutionEvent: evPersistFlexiCardAccountResponse,
		}, data)
		if err != nil {
			slog.FromContext(ctx).Warn(createBundledApplicationWorkflowLogTag, fmt.Sprintf("Workflow execute error in ConsumeCreditCardAccountCreationStream: %s", err.Error()))
			return utils.CheckIfResourceNotFound(ctx, err, createBundledApplicationWorkflowLogTag, data.ReferenceID)
		}
	}
	return nil
}

// ConsumeLOCAccountCreationEvent consumes event from account service loc creation stream and resumes workflow for bundler application
// nolint: dupl
func ConsumeLOCAccountCreationEvent(ctx context.Context, data *dto.LOCAccountCreationStreamMessage) error {
	execData, err := wfGet(ctx, we.Execution{
		WorkflowID: workflowID,
		RequestID:  data.ReferenceID,
	})

	if err != nil {
		slog.FromContext(ctx).Warn(createBundledApplicationWorkflowLogTag, fmt.Sprintf("Error when getting existing workflow: %s", err.Error()))
		return err
	}

	if execData.GetState() == stFlexiLimitAccountCreationProcessing {
		_, consumeErr := wfExecute(ctx, we.Execution{
			WorkflowID:     workflowID,
			RequestID:      data.ReferenceID,
			ExecutionEvent: evFlexiLimitAccountCreationSuccess,
		}, data)
		if consumeErr != nil {
			slog.FromContext(ctx).Warn(createBundledApplicationWorkflowLogTag, fmt.Sprintf("Workflow execute error in ConsumeLOCAccountCreationEvent: %s", consumeErr.Error()))
			return utils.CheckIfResourceNotFound(ctx, consumeErr, createBundledApplicationWorkflowLogTag, data.ReferenceID)
		}
	}
	return nil
}

// ConsumeOnboardingRejectionEvent consumes onboarding rejected event for bundledApplication application and resume workflow
func ConsumeOnboardingRejectionEvent(ctx context.Context, data *applicationStatusTransistionSchema.ApplicationStatusTransition) error {
	execData, err := wfGet(ctx, we.Execution{
		WorkflowID: workflowID,
		RequestID:  data.ApplicationID,
	})
	if err != nil {
		slog.FromContext(ctx).Warn(createBundledApplicationWorkflowLogTag, fmt.Sprintf("Error when getting existing workflow: %s", err.Error()))
		return utils.CheckIfResourceNotFound(ctx, err, createBundledApplicationWorkflowLogTag, data.ApplicationID)
	}

	if execData.GetState() == stPostBureauCDEApprovedPublished {
		_, err2 := wfExecute(ctx, we.Execution{
			WorkflowID:     workflowID,
			RequestID:      data.ApplicationID,
			ExecutionEvent: evOnboardingRejected,
		}, data)
		if err2 != nil {
			slog.FromContext(ctx).Warn(createBundledApplicationWorkflowLogTag, fmt.Sprintf("Workflow execute error in ConsumeCreditCardAccountCreationStream: %s", err2.Error()))
			return utils.CheckIfResourceNotFound(ctx, err2, createBundledApplicationWorkflowLogTag, data.ApplicationID)
		}
	}
	return nil
}

// ConsumeUpdateOfferDetails consumes customer response and resumes workflow for flexicard application
func ConsumeUpdateOfferDetails(ctx context.Context, updateLoanOfferDetailsRequest *api.UpdateLoanOfferDetailsRequest, onboardingApplicationID string) error {
	execData, err := wfExecute(ctx, we.Execution{
		WorkflowID:     workflowID,
		RequestID:      onboardingApplicationID,
		ExecutionEvent: evCustomerOfferResponse,
	}, updateLoanOfferDetailsRequest)
	if err != nil {
		return utils.CheckIfResourceNotFound(ctx, err, createBundledApplicationWorkflowLogTag, onboardingApplicationID)
	}
	if execData.GetState() == stPersistCustomerResponse {
		newCtx := utils.NewCtxWithSpan(ctx)
		gconcurrent.Go(newCtx, createBundledApplicationWorkflowLogTag, utils.ExecuteEventAsync(onboardingApplicationID, evUpdateOfferResumeAsyncWorkflow, createBundledApplicationWorkflowLogTag, workflowID))
	}
	return nil
}
