// Package bundledapplication ...
// nolint:dupl
package bundledapplication

import (
	"context"
	"errors"
	"fmt"

	crDecisionEng "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	loanAppAPI "gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	we "gitlab.myteksi.net/dakota/workflowengine"
	tags "gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

func (w *WorkflowImpl) persistOnboardingEvent(ctx context.Context, transitionID string, executionData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := executionData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(createBundledApplicationWorkflowLogTag, "Invalid context passed in persistOnboardingEvent state")
		return nil, errInvalidContext
	}
	nextCtx := currCtx.Clone()
	ctx = slog.AddTagsToContext(ctx, tags.TraceID(nextCtx.CreateBundledApplicationRequest.Message.OnboardingApplicationID))

	applicationStatusTransition, ok := params.(*applicationStatusTransistionSchema.ApplicationStatusTransition)
	if !ok {
		slog.FromContext(ctx).Warn(createBundledApplicationWorkflowLogTag, "Invalid params passed in persistOnboardingEvent state")
		return nil, common.ErrInvalidApplicationTransitionDtoPassed
	}
	nextCtx.ApplicationStatusTransition = applicationStatusTransition
	nextCtx.SetState(stOnboardingEventPersisted)
	return nextCtx, nil
}

func (w *WorkflowImpl) handleOnboardingRejection(ctx context.Context, transitionID string, executionData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := executionData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(createBundledApplicationWorkflowLogTag, "Invalid context passed in handleOnboardingRejection state")
		return nil, errInvalidContext
	}
	nextCtx := currCtx.Clone()
	ctx = slog.AddTagsToContext(ctx, tags.TraceID(nextCtx.CreateBundledApplicationRequest.Message.OnboardingApplicationID))

	var stmts []*storage.TransactionStmt
	if nextCtx.OfferedProduct == loanAppAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT && nextCtx.ApplicationData[string(loanAppAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT)].OnboardingApplicationID == nextCtx.ApplicationStatusTransition.ApplicationID {
		nextCtx = updateStatusAndStatusReason(nextCtx, string(loanAppAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT), string(loanAppAPI.FlexiCard_ApplicationStatus_REJECTED), nextCtx.ApplicationStatusTransition.RejectionReason)
		stmts = append(stmts, w.BundledApplicationQueryGenerator.CreateUpdateApplicationStatement(nextCtx.ApplicationData, []crDecisionEng.ProductType{crDecisionEng.ProductType_FLEXI_LOAN_LINE_OF_CREDIT})...)
		stmts = append(stmts, w.BundledApplicationQueryGenerator.CreateUpdateOfferStatement(nextCtx.ApplicationData[string(loanAppAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT)]))
	} else if nextCtx.OfferedProduct == loanAppAPI.ProductType_FLEXI_CREDIT_CARD && nextCtx.ApplicationData[string(loanAppAPI.ProductType_FLEXI_CREDIT_CARD)].OnboardingApplicationID == nextCtx.ApplicationStatusTransition.ApplicationID {
		nextCtx = updateStatusAndStatusReason(nextCtx, string(loanAppAPI.ProductType_FLEXI_CREDIT_CARD), string(loanAppAPI.FlexiCard_ApplicationStatus_REJECTED), nextCtx.ApplicationStatusTransition.RejectionReason)
		stmts = append(stmts, w.BundledApplicationQueryGenerator.CreateUpdateApplicationStatement(nextCtx.ApplicationData, []crDecisionEng.ProductType{crDecisionEng.ProductType_FLEXI_CREDIT_CARD})...)
		stmts = append(stmts, w.BundledApplicationQueryGenerator.CreateUpdateOfferStatement(nextCtx.ApplicationData[string(loanAppAPI.ProductType_FLEXI_CREDIT_CARD)]))
	} else {
		slog.FromContext(ctx).Warn(createBundledApplicationWorkflowLogTag, "Application ID in application transition event doesn't match to bundled application IDs")
		return nil, errors.New("application ID in application transition event doesn't match to bundled application IDs")
	}

	err := w.Store.ExecuteTransaction(ctx, w.AppConfig, stmts)
	if err != nil {
		slog.FromContext(ctx).Warn(createBundledApplicationWorkflowLogTag, fmt.Sprintf("Error in update application status and offer status: %s", err.Error()))
		return nil, err
	}

	nextCtx.SetState(stOnboardingChecksRejected)
	return nextCtx, nil
}
