package flexicardapplication

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	loanAppAPI "gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	"gitlab.myteksi.net/dakota/workflowengine"
)

func Test_ConsumeCBSData(t *testing.T) {
	scenarios := []struct {
		desc        string
		errExecute  error
		expectedErr error
	}{
		{desc: "happy case", errExecute: nil, expectedErr: nil},
		{desc: "error case", errExecute: errDummy, expectedErr: errDummy},
		{desc: "workflow found but workflowID is different", errExecute: errors.New("loadWorkflowByTransID: workflowID does not match"), expectedErr: nil},
		{desc: "workflow not found", errExecute: workflowengine.ErrResourceNotFound, expectedErr: nil},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
				return nil, scenario.errExecute
			}
			cbsDataDTO := &dto.CreditBureauEnquiryStreamMessage{}
			resp := ConsumeCBSData(context.Background(), cbsDataDTO)
			assert.Equal(t, scenario.expectedErr, resp)
		})
	}
}

func Test_ConsumeUpdateOfferDetails(t *testing.T) {
	scenarios := []struct {
		desc        string
		errExecute  error
		expectedErr error
	}{
		{desc: "happy case", errExecute: nil, expectedErr: nil},
		{desc: "error case", errExecute: errDummy, expectedErr: errDummy},
		{desc: "workflow found but workflowID is different", errExecute: errors.New("loadWorkflowByTransID: workflowID does not match"), expectedErr: nil},
		{desc: "workflow not found", errExecute: workflowengine.ErrResourceNotFound, expectedErr: nil},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
				return &ExecutionData{State: stPersistCustomerResponse}, scenario.errExecute
			}
			updateLoanOfferDetailsRequest := &loanAppAPI.UpdateLoanOfferDetailsRequest{}
			resp := ConsumeUpdateOfferDetails(context.Background(), updateLoanOfferDetailsRequest, "onboarding-application-id")
			assert.Equal(t, scenario.expectedErr, resp)
		})
	}
}

func Test_ConsumeCreditCardAccountCreationStream(t *testing.T) {
	scenarios := []struct {
		desc        string
		errExecute  error
		errGet      error
		expectedErr error
	}{
		{desc: "happy case", errExecute: nil, expectedErr: nil},
		{desc: "error case", errGet: errDummy, expectedErr: errDummy},
		{desc: "error case", errExecute: errDummy, expectedErr: errDummy},
		{desc: "workflow found but workflowID is different", errExecute: errors.New("loadWorkflowByTransID: workflowID does not match"), expectedErr: nil},
		{desc: "workflow not found", errExecute: workflowengine.ErrResourceNotFound, expectedErr: nil},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			wfGet = func(ctx context.Context, e workflowengine.Execution) (workflowengine.ExecutionData, error) {
				return &ExecutionData{
					State: stFlexiCardAccountCreationProcessing,
				}, scenario.errGet
			}
			wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
				return &ExecutionData{}, scenario.errExecute
			}
			data := &dto.CreditCardAccountCreationStreamMessage{}
			resp := ConsumeCreditCardAccountCreationStream(context.Background(), data)
			assert.Equal(t, scenario.expectedErr, resp)
		})
	}
}

func Test_ConsumeOnboardingRejectionEvent(t *testing.T) {
	scenarios := []struct {
		desc        string
		errExecute  error
		errGet      error
		expectedErr error
	}{
		{desc: "happy case", errExecute: nil, expectedErr: nil},
		{desc: "error case", errGet: errDummy, expectedErr: errDummy},
		{desc: "error case", errExecute: errDummy, expectedErr: errDummy},
		{desc: "workflow found but workflowID is different", errExecute: errors.New("loadWorkflowByTransID: workflowID does not match"), expectedErr: nil},
		{desc: "workflow not found", errExecute: workflowengine.ErrResourceNotFound, expectedErr: nil},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			wfGet = func(ctx context.Context, e workflowengine.Execution) (workflowengine.ExecutionData, error) {
				return &ExecutionData{
					State: stPostBureauCDEApprovedPublished,
				}, scenario.errGet
			}
			wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
				return &ExecutionData{}, scenario.errExecute
			}
			data := &applicationStatusTransistionSchema.ApplicationStatusTransition{}
			resp := ConsumeOnboardingRejectionEvent(context.Background(), data)
			assert.Equal(t, scenario.expectedErr, resp)
		})
	}
}
