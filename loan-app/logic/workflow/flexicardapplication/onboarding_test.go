package flexicardapplication

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	"gitlab.myteksi.net/dakota/workflowengine"
)

func Test_persistOnboardingEvent(t *testing.T) {
	errDummySimulateError := errors.New("simulate error")
	scenarios := []struct {
		desc          string
		workflowState workflowengine.State
		streamMessage interface{}
		errParams     error
		expectedErr   error
	}{
		{
			desc:          "error stream message",
			errParams:     errDummySimulateError,
			expectedErr:   common.ErrInvalidApplicationTransitionDtoPassed,
			streamMessage: "Stream Message",
		},
		{
			desc:          "successfully persisted",
			workflowState: stOnboardingEventPersisted,
			streamMessage: &applicationStatusTransistionSchema.ApplicationStatusTransition{},
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			w := WorkflowImpl{}
			resp, err := w.persistOnboardingEvent(context.Background(), "", &ExecutionData{
				CreateFlexiCardApplicationRequest: &api.CreateFlexiCardApplicationRequest{
					Message: &api.FlexiCardMessage{OnboardingApplicationID: uuid.NewString()},
				},
			}, scenario.streamMessage)
			if scenario.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, scenario.expectedErr, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, scenario.workflowState, resp.GetState())
			}
		})
	}
}

func Test_handleOnboardingRejection(t *testing.T) {
	scenarios := []struct {
		desc          string
		expectedErr   error
		updateErr     error
		expectedState workflowengine.State
	}{
		{
			desc:        "error while updating the application",
			updateErr:   errDummy,
			expectedErr: errDummy,
		},
		{
			desc:          "happy path",
			expectedState: stOnboardingChecksRejected,
		},
	}
	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			mockDatabaseStore := &storage.MockDatabaseStore{}
			mockDatabaseStore.On("ExecuteTransaction", mock.Anything, mock.Anything, mock.Anything).Return(scenario.updateErr)

			mockQueryGenerator := &MockIFlexiCardQueryGenerator{}
			mockQueryGenerator.On("CreateUpdateApplicationStatement", mock.Anything).Return(&storage.TransactionStmt{})
			mockQueryGenerator.On("CreateUpdateOfferStatement", mock.Anything).Return(&storage.TransactionStmt{})

			w := WorkflowImpl{
				Store:                   mockDatabaseStore,
				AppConfig:               &config.AppConfig{},
				FlexiCardQueryGenerator: mockQueryGenerator,
			}

			resp, err := w.handleOnboardingRejection(context.Background(), "", &ExecutionData{
				CreateFlexiCardApplicationRequest: &api.CreateFlexiCardApplicationRequest{
					Message: &api.FlexiCardMessage{OnboardingApplicationID: uuid.NewString()},
				},
				Application:                 &storage.Application{},
				ApplicationStatusTransition: &applicationStatusTransistionSchema.ApplicationStatusTransition{},
			}, nil)
			if scenario.expectedErr != nil {
				assert.Nil(t, resp)
				assert.Equal(t, scenario.expectedErr.Error(), err.Error())
			} else {
				assert.Nil(t, err)
				assert.Equal(t, scenario.expectedState, resp.GetState())
			}
		})
	}
}
