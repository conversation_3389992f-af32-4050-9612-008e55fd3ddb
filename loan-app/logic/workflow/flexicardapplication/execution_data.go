package flexicardapplication

import (
	"encoding/json"

	creditDecisionEngineAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	mlScoringServiceAPI "gitlab.com/gx-regional/dakota/lending/loan-app/external/mlscoringservice/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	digicardCoreAPI "gitlab.myteksi.net/dakota/digicard/digicard-core/api"
	creditBureauServiceAPI "gitlab.myteksi.net/dakota/lending/credit-bureau/api"
	accountServiceAPI "gitlab.myteksi.net/dakota/lending/external/corebanking/accountservice"
	"gitlab.myteksi.net/dakota/workflowengine"
)

// ExecutionData is execution data for flexi-card creation workflow
type ExecutionData struct {
	State                                  workflowengine.State
	UserID                                 string
	ApplicationID                          string
	Application                            *storage.Application
	CreateFlexiCardApplicationRequest      *api.CreateFlexiCardApplicationRequest
	IsApplicantFlexiLOCCustomer            bool
	ExistingFlexiLOCCustomerAccounts       []accountServiceAPI.AccountDetailForCustomer
	CreditBureauEnquiryRequest             *creditBureauServiceAPI.ApplicantEnquiryRequest
	CreditBureauEnquiryResponse            *creditBureauServiceAPI.ApplicantEnquiryResponse
	CreditBureauEnquiryStreamMessage       *dto.CreditBureauEnquiryStreamMessage
	ApplicationScoreRequest                *mlScoringServiceAPI.GetApplicationScoreRequest
	ApplicationScoreResponse               *mlScoringServiceAPI.GetApplicationScoreResponse
	ApplicantsEcosystemDetails             []*storage.ApplicantEcoSystemDetails
	PreBureauCreditDecisionRequest         *creditDecisionEngineAPI.FICOCreditDecisionRequest
	PreBureauCreditDecisionResponse        *creditDecisionEngineAPI.FICOCreditDecisionResponse
	PostBureauCreditDecisionRequest        *creditDecisionEngineAPI.FICOCreditDecisionRequest
	PostBureauCreditDecisionResponse       *creditDecisionEngineAPI.FICOCreditDecisionResponse
	CreateCardInternalRequest              *digicardCoreAPI.CreateCardInternalRequest
	CreateCardInternalResponse             *digicardCoreAPI.CreateCardInternalResponse
	UpdateOfferDetailsRequest              *api.UpdateLoanOfferDetailsRequest
	CreateCreditCardAccountRequest         *accountServiceAPI.CreateCreditCardAccountRequest
	CreateCreditCardAccountResponse        *accountServiceAPI.CreateCreditCardAccountResponse
	CreditCardAccountCreationStreamMessage *dto.CreditCardAccountCreationStreamMessage
	FinalDecisionCreditDecisionRequest     *creditDecisionEngineAPI.FICOCreditDecisionRequest
	FinalDecisionCreditDecisionResponse    *creditDecisionEngineAPI.FICOCreditDecisionResponse
	FlexiCardProductParameters             map[string]string
	ApplicationStatusTransition            *applicationStatusTransistionSchema.ApplicationStatusTransition
}

// GetState ...
func (p *ExecutionData) GetState() workflowengine.State {
	return p.State
}

// SetState ...
func (p *ExecutionData) SetState(state workflowengine.State) {
	p.State = state
}

// Marshal ...
func (p *ExecutionData) Marshal() ([]byte, error) {
	return json.Marshal(p)
}

// Unmarshal ...
func (p *ExecutionData) Unmarshal(byteData []byte) error {
	return json.Unmarshal(byteData, p)
}

// Clone ...
func (p *ExecutionData) Clone() *ExecutionData {
	newCtx := *p
	return &newCtx
}
