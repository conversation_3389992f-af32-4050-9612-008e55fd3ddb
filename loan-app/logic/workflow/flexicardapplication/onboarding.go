package flexicardapplication

import (
	"context"
	"fmt"

	loanAppAPI "gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	we "gitlab.myteksi.net/dakota/workflowengine"
	tags "gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

//nolint:dupl
func (w *WorkflowImpl) persistOnboardingEvent(ctx context.Context, transitionID string, execData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(createFlexiCardApplicationWorkflowLogTag, "Invalid context passed in handleOnboardingRejection state")
		return nil, errInvalidContext
	}

	nextCtx := currCtx.Clone()
	ctx = slog.AddTagsToContext(ctx, tags.TraceID(nextCtx.CreateFlexiCardApplicationRequest.Message.OnboardingApplicationID))

	applicationStatusTransition, ok := params.(*applicationStatusTransistionSchema.ApplicationStatusTransition)
	if !ok {
		slog.FromContext(ctx).Warn(createFlexiCardApplicationWorkflowLogTag, "Invalid params passed in persistOnboardingEvent state")
		return nil, common.ErrInvalidApplicationTransitionDtoPassed
	}

	nextCtx.ApplicationStatusTransition = applicationStatusTransition
	nextCtx.SetState(stOnboardingEventPersisted)
	return nextCtx, nil
}

func (w *WorkflowImpl) handleOnboardingRejection(ctx context.Context, transitionID string, execData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(createFlexiCardApplicationWorkflowLogTag, "Invalid context passed in handleOnboardingRejection state")
		return nil, errInvalidContext
	}

	nextCtx := currCtx.Clone()
	ctx = slog.AddTagsToContext(ctx, tags.TraceID(nextCtx.CreateFlexiCardApplicationRequest.Message.OnboardingApplicationID))
	nextCtx = updateStatusAndStatusReason(nextCtx, loanAppAPI.FlexiCard_ApplicationStatus_REJECTED, loanAppAPI.FlexiCard_ApplicationStatusReason(nextCtx.ApplicationStatusTransition.RejectionReason))

	var stmts []*storage.TransactionStmt
	stmts = append(stmts, w.FlexiCardQueryGenerator.CreateUpdateApplicationStatement(nextCtx.Application))
	stmts = append(stmts, w.FlexiCardQueryGenerator.CreateUpdateOfferStatement(nextCtx.Application))

	err := w.Store.ExecuteTransaction(ctx, w.AppConfig, stmts)
	if err != nil {
		slog.FromContext(ctx).Warn(createFlexiCardApplicationWorkflowLogTag, fmt.Sprintf("Error in update application status and offer status: %s", err.Error()))
		return nil, err
	}

	nextCtx.SetState(stOnboardingChecksRejected)
	return nextCtx, nil
}
