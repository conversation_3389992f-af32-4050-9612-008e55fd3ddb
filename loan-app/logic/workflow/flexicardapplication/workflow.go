package flexicardapplication

import (
	"context"
	"fmt"

	creditDecisionEngineAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	mlScoringServiceAPI "gitlab.com/gx-regional/dakota/lending/loan-app/external/mlscoringservice/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/kafka/publishers"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	digicardCoreAPI "gitlab.myteksi.net/dakota/digicard/digicard-core/api"
	creditBureauServiceAPI "gitlab.myteksi.net/dakota/lending/credit-bureau/api"
	accountServiceAPI "gitlab.myteksi.net/dakota/lending/external/corebanking/accountservice"
	productMaster "gitlab.myteksi.net/dakota/lending/external/corebanking/productmaster"
	grabAPI "gitlab.myteksi.net/dakota/lending/external/grab/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	we "gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"
)

const (
	workflowID                               = "flexi_card_application_workflow"
	createFlexiCardApplicationWorkflowLogTag = "createFlexiCardApplicationWorkflowLog"
	// FlexiCardApplicationWorkflowID should always be same as workflowID but public
	FlexiCardApplicationWorkflowID = workflowID
)

const (
	stInit = we.StateInit

	stCreateApplicationSuccess = we.State(200)

	stFlexiLOCAccountExist        = we.State(205)
	stFlexiLOCAccountDoesNotExist = we.State(206)

	stAggregateEcosystemDataSuccess = we.State(215)

	stPreBureauCDEProcessing        = we.State(225)
	stPreBureauCDEApproved          = we.State(226)
	stPreBureauCDERejected          = we.State(525)
	stPreBureauCDERejectedPublished = we.State(229)

	stFetchCreditBureauDataProcessing = we.State(230)
	stFetchCreditBureauDataCompleted  = we.State(231)
	stFetchCreditBureauDataFailure    = we.State(530)

	stFetchScoringModelDataSuccess = we.State(235)

	stPostBureauCDEProcessing        = we.State(240)
	stPostBureauCDEApproved          = we.State(241)
	stPostBureauCDEApprovedPublished = we.State(242)
	stPostBureauCDERejected          = we.State(540)
	stPostBureauCDERejectedPublished = we.State(243)

	stOnboardingEventPersisted = we.State(246)
	stOnboardingChecksRejected = we.State(545)

	stPersistCustomerResponse = we.State(250)
	stCustomerCardAcceptance  = we.State(251)
	stCustomerCardDeclined    = we.State(551)

	stFlexiCardAccountCreationProcessing = we.State(265)
	stFlexiCardAccountResponsePersisted  = we.State(266)
	stFlexiCardAccountCreationSuccess    = we.State(267)
	stFlexiCardAccountCreationFailed     = we.State(567)

	stFlexiCardCreationSuccess                        = we.State(270)
	stFlexiCardCreationFailed                         = we.State(570)
	stUpdateApplicationStatusFlexiCardCreationSuccess = we.State(271)
	stUpdateApplicationStatusFlexiCardCreationFailed  = we.State(272)

	stUpdateFlexiCardAccountStatus          = we.State(275)
	stUpdateFlexiCardAccountStatusPublished = we.State(276)

	stInterventionNeeded = we.State(500)

	stUpdateCDEWithCustomerAcceptance = we.State(901)
)

const (
	evNoNeed                     = we.EventNoNeed
	evCreateFlexiCardApplication = we.Event(100)
	evStartAsyncWorkflow         = we.Event(101)

	evPersistCreditBureauData = we.Event(120)

	evOnboardingRejected = we.Event(130)

	evCustomerCardOfferResponse      = we.Event(140)
	evUpdateOfferResumeAsyncWorkflow = we.Event(141)

	evPersistFlexiCardAccountResponse = we.Event(160)
)

var (
	wfInit            = we.InitExecution
	wfGet             = we.GetExecution
	wfExecute         = we.Execute
	errInvalidContext = common.ErrInvalidContext
)

// WorkflowImpl : Flexi-card workflow implementation
type WorkflowImpl struct {
	AppConfig                      *config.AppConfig                     `inject:"config"`
	AccountServiceClient           accountServiceAPI.AccountService      `inject:"client.accountService"`
	CreditBureauServiceClient      creditBureauServiceAPI.CreditBureau   `inject:"client.creditBureauService"`
	GrabClient                     grabAPI.Grab                          `inject:"client.grabAPI"`
	MlScoringServiceClient         mlScoringServiceAPI.MlScoringService  `inject:"client.mlScoringService"`
	CreditDecisionEngine           creditDecisionEngineAPI.CrDecisionEng `inject:"client.creditDecisionEng"`
	FlexiCardQueryGenerator        IFlexiCardQueryGenerator              `inject:"flexiCardQueryGenerator"`
	Store                          storage.DatabaseStore                 `inject:"DBStore"`
	DigicardCoreClient             digicardCoreAPI.DigicardCore          `inject:"client.digicardCore"`
	LoanAppLifecycleEventPublisher publishers.Publisher                  `inject:"publishers.loanAppLifecycleEvent"`
	ProductMasterClient            productMaster.ProductMaster           `inject:"client.productMaster"`
}

// Register : register flexi-card application workflow
// nolint: funlen
func (w *WorkflowImpl) Register() {
	createFlexiCardApplication := we.NewWorkflow(workflowID, func() we.ExecutionData {
		return &ExecutionData{}
	})

	var transactionalRetryOption *we.TransitionOptions
	if retryOption := w.AppConfig.WorkflowRetryConfig.CreateFlexiCardApplication.TransactionalRetryOption; retryOption != nil {
		// used for time-sensitive transactional states
		transactionalRetryOption = &we.TransitionOptions{RetryPolicy: &we.RetryPolicy{
			Interval:    retryOption.IntervalInSeconds,
			MaxAttempts: retryOption.MaxAttempt}}
	}

	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stInit, evCreateFlexiCardApplication, w.createFlexiCardApplication, nil, stCreateApplicationSuccess)

	//Check if it is FlexiLoan Customer
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stCreateApplicationSuccess, evStartAsyncWorkflow, w.checkIfFlexiLOCAccountExist, nil, stFlexiLOCAccountExist, stFlexiLOCAccountDoesNotExist)

	//Fetch ecosystem data
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stFlexiLOCAccountDoesNotExist, evNoNeed, w.fetchEcosystemData, transactionalRetryOption, stAggregateEcosystemDataSuccess)

	// Pre Bureau Check
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stAggregateEcosystemDataSuccess, evNoNeed, w.preBureauCDECheck, transactionalRetryOption, stPreBureauCDEProcessing)
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stPreBureauCDEProcessing, evNoNeed, w.completePreBureauCDECheck, nil, stPreBureauCDEApproved, stPreBureauCDERejected, stInterventionNeeded)
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stPreBureauCDERejected, evNoNeed, w.publishStatusToOnboarding, transactionalRetryOption, stPreBureauCDERejectedPublished)

	// Fetch bureau data
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stPreBureauCDEApproved, evNoNeed, w.fetchCreditBureauData, transactionalRetryOption, stFetchCreditBureauDataProcessing)
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stFetchCreditBureauDataProcessing, evPersistCreditBureauData, w.persistCreditBureauData, nil, stFetchCreditBureauDataCompleted, stFetchCreditBureauDataFailure)

	// Fetch ML Score
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stFetchCreditBureauDataCompleted, evNoNeed, w.fetchScoringModelData, transactionalRetryOption, stFetchScoringModelDataSuccess)

	// Post Bureau Check
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stFetchScoringModelDataSuccess, evNoNeed, w.postBureauCDECheck, transactionalRetryOption, stPostBureauCDEProcessing)
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stPostBureauCDEProcessing, evNoNeed, w.completePostBureauCDECheck, nil, stPostBureauCDEApproved, stPostBureauCDERejected, stInterventionNeeded)

	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stPostBureauCDEApproved, evNoNeed, w.publishStatusToOnboarding, transactionalRetryOption, stPostBureauCDEApprovedPublished)
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stPostBureauCDERejected, evNoNeed, w.publishStatusToOnboarding, transactionalRetryOption, stPostBureauCDERejectedPublished)

	// Onboarding rejected
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stPostBureauCDEApprovedPublished, evOnboardingRejected, w.persistOnboardingEvent, nil, stOnboardingEventPersisted)
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stOnboardingEventPersisted, evNoNeed, w.handleOnboardingRejection, nil, stOnboardingChecksRejected)

	// Customer Acceptance
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stPostBureauCDEApprovedPublished, evCustomerCardOfferResponse, w.persistCustomerResponse, nil, stPersistCustomerResponse)
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stPersistCustomerResponse, evUpdateOfferResumeAsyncWorkflow, w.handleCustomerResponse, nil, stCustomerCardAcceptance, stCustomerCardDeclined)

	// Create flexicard account
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stCustomerCardAcceptance, evNoNeed, w.createFlexicardAccount, transactionalRetryOption, stFlexiCardAccountCreationProcessing)

	//Persist flexi-card account creation response
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stFlexiCardAccountCreationProcessing, evPersistFlexiCardAccountResponse, w.persistFlexiCardAccountCreationResponse, nil, stFlexiCardAccountResponsePersisted)
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stFlexiCardAccountResponsePersisted, evNoNeed, w.completeFlexiCardAccountCreation, nil, stFlexiCardAccountCreationSuccess, stFlexiCardAccountCreationFailed)

	// Create Virtual + physical Card
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stFlexiCardAccountCreationSuccess, evNoNeed, w.createFlexiCard, transactionalRetryOption, stFlexiCardCreationSuccess, stFlexiCardCreationFailed, stInterventionNeeded)

	// Update application status
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stFlexiCardCreationSuccess, evNoNeed, w.updateApplicationStatus, transactionalRetryOption, stUpdateApplicationStatusFlexiCardCreationSuccess)
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stFlexiCardCreationFailed, evNoNeed, w.updateApplicationStatus, transactionalRetryOption, stUpdateApplicationStatusFlexiCardCreationFailed)

	// Update Flexi-card account status to active
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stUpdateApplicationStatusFlexiCardCreationSuccess, evNoNeed, w.updateFlexiCardAccountStatus, transactionalRetryOption, stUpdateFlexiCardAccountStatus)
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stUpdateFlexiCardAccountStatus, evNoNeed, w.publishStatusToOnboarding, transactionalRetryOption, stUpdateFlexiCardAccountStatusPublished)

	// Update CDE with Customer Acceptance of flexi-card
	createFlexiCardApplication.AddTransition(createFlexiCardApplicationWorkflowLogTag, stUpdateFlexiCardAccountStatusPublished, evNoNeed, w.updateCDEWithCustomerAcceptance, transactionalRetryOption, stUpdateCDEWithCustomerAcceptance)

	we.RegisterWorkflow(createFlexiCardApplication)
}

// ConsumeCBSData consumes event from credit-bureau event stream and resumes workflow for flexicard application
func ConsumeCBSData(ctx context.Context, cbsDataDTO *dto.CreditBureauEnquiryStreamMessage) error {
	_, err := wfExecute(ctx, we.Execution{
		WorkflowID:     workflowID,
		TransitionID:   cbsDataDTO.ReferenceID,
		ExecutionEvent: evPersistCreditBureauData,
	}, cbsDataDTO)
	if err != nil {
		slog.FromContext(ctx).Warn(createFlexiCardApplicationWorkflowLogTag, fmt.Sprintf("Workflow execute error in ConsumeCBSData: %s", err.Error()))
		return utils.CheckIfResourceNotFound(ctx, err, createFlexiCardApplicationWorkflowLogTag, cbsDataDTO.ReferenceID)
	}
	return nil
}

// ConsumeUpdateOfferDetails consumes customer response and resumes workflow for flexicard application
func ConsumeUpdateOfferDetails(ctx context.Context, updateLoanOfferDetailsRequest *api.UpdateLoanOfferDetailsRequest, onboardingApplicationID string) error {
	execData, err := wfExecute(ctx, we.Execution{
		WorkflowID:     workflowID,
		RequestID:      onboardingApplicationID,
		ExecutionEvent: evCustomerCardOfferResponse,
	}, updateLoanOfferDetailsRequest)
	if err != nil {
		return utils.CheckIfResourceNotFound(ctx, err, createFlexiCardApplicationWorkflowLogTag, onboardingApplicationID)
	}
	if execData.GetState() == stPersistCustomerResponse {
		newCtx := utils.NewCtxWithSpan(ctx)
		gconcurrent.Go(newCtx, createFlexiCardApplicationWorkflowLogTag, utils.ExecuteEventAsync(onboardingApplicationID, evUpdateOfferResumeAsyncWorkflow, createFlexiCardApplicationWorkflowLogTag, workflowID))
	}
	return nil
}

// ConsumeCreditCardAccountCreationStream consumes event from account service flexicard creation stream and resumes workflow for flexicard application
// nolint: dupl
func ConsumeCreditCardAccountCreationStream(ctx context.Context, data *dto.CreditCardAccountCreationStreamMessage) error {
	execData, err := wfGet(ctx, we.Execution{
		WorkflowID: workflowID,
		RequestID:  data.ReferenceID,
	})

	if err != nil {
		slog.FromContext(ctx).Warn(createFlexiCardApplicationWorkflowLogTag, fmt.Sprintf("error when getting existing workflow: %s", err.Error()), utils.GetTraceID(ctx))
		return err
	}

	if execData.GetState() == stFlexiCardAccountCreationProcessing {
		_, err = wfExecute(ctx, we.Execution{
			WorkflowID:     workflowID,
			RequestID:      data.ReferenceID,
			ExecutionEvent: evPersistFlexiCardAccountResponse,
		}, data)
		if err != nil {
			slog.FromContext(ctx).Warn(createFlexiCardApplicationWorkflowLogTag, fmt.Sprintf("Workflow execute error in ConsumeCreditCardAccountCreationStream: %s", err.Error()))
			return utils.CheckIfResourceNotFound(ctx, err, createFlexiCardApplicationWorkflowLogTag, data.ReferenceID)
		}
	}
	return nil
}

// ConsumeOnboardingRejectionEvent consumes onboarding rejected event for flexicard application and resume workflow
// nolint: dupl
func ConsumeOnboardingRejectionEvent(ctx context.Context, data *applicationStatusTransistionSchema.ApplicationStatusTransition) error {
	execData, err := wfGet(ctx, we.Execution{
		WorkflowID: workflowID,
		RequestID:  data.ApplicationID,
	})
	if err != nil {
		slog.FromContext(ctx).Warn(createFlexiCardApplicationWorkflowLogTag, fmt.Sprintf("error when getting existing workflow: %s", err.Error()), utils.GetTraceID(ctx))
		return utils.CheckIfResourceNotFound(ctx, err, createFlexiCardApplicationWorkflowLogTag, data.ApplicationID)
	}

	if execData.GetState() == stPostBureauCDEApprovedPublished {
		_, err = wfExecute(ctx, we.Execution{
			WorkflowID:     workflowID,
			RequestID:      data.ApplicationID,
			ExecutionEvent: evOnboardingRejected,
		}, data)
		if err != nil {
			slog.FromContext(ctx).Warn(createFlexiCardApplicationWorkflowLogTag, fmt.Sprintf("Workflow execute error in ConsumeCreditCardAccountCreationStream: %s", err.Error()))
			return utils.CheckIfResourceNotFound(ctx, err, createFlexiCardApplicationWorkflowLogTag, data.ApplicationID)
		}
	}
	return nil
}
