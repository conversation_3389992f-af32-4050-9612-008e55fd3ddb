package bizflexicreditapplication

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	creditDecisionEngineAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	loanAppAPI "gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	mlScoringServiceAPI "gitlab.com/gx-regional/dakota/lending/loan-app/external/mlscoringservice/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/kafka/publishers"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"
	ekybAPI "gitlab.myteksi.net/dbmy/ekyb-service/api"

	"gitlab.myteksi.net/dakota/common/servicename"
	experianAPI "gitlab.myteksi.net/dakota/experian-adapter/api"
	"gitlab.myteksi.net/dakota/lending/common/stats"
	creditBureauServiceAPI "gitlab.myteksi.net/dakota/lending/credit-bureau/api"
	accountServiceAPI "gitlab.myteksi.net/dakota/lending/external/corebanking/accountservice"
	productMaster "gitlab.myteksi.net/dakota/lending/external/corebanking/productmaster"
	grabAPI "gitlab.myteksi.net/dakota/lending/external/grab/api"
	"gitlab.myteksi.net/dakota/lending/external/notification"
	customerMaster "gitlab.myteksi.net/dakota/lending/external/onboarding/customermaster"
	"gitlab.myteksi.net/dakota/lending/external/transactionStatement"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	whitelistAPI "gitlab.myteksi.net/dakota/whitelist-service/api"
	we "gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"
)

const (
	stInit                     = we.StateInit
	stCreateApplicationSuccess = we.State(100)

	// intermediate states
	stGrabEcosystemDataFetchedBasedOnNRIC     = we.State(200)
	stGrabEcosystemDataFetchedWithPhoneNumber = we.State(204)
	stSingtelEcosystemDataFetchedWithUEN      = we.State(205)
	stGrabEcosystemDataFetchedFromUEN         = we.State(208)
	stGrabEcosystemDataFetchedFromBRN         = we.State(212)
	stGrabEcosystemDataFetchFromNewBRNFailed  = we.State(213)
	stPreBureauCDEProcessing                  = we.State(216)
	stPreBureauCDEApproved                    = we.State(217)
	stFetchCreditBureauDataProcessing         = we.State(224)
	stFetchCreditBureauDataCompleted          = we.State(225)
	stFetchCreditBureauDataFailed             = we.State(226)
	stFetchExperianSMENSReportProcessing      = we.State(230)
	stFetchExperianSMENSReportCompleted       = we.State(231)
	stFetchExperianSMENSReportFailed          = we.State(232)
	stFetchExperianCBSCReportProcessing       = we.State(240)
	stFetchExperianCBSCReportCompleted        = we.State(241)
	stFetchExperianCBSCReportFailed           = we.State(242)
	stFetchScoringModelDataSuccess            = we.State(248)
	stPostBureauCDEProcessing                 = we.State(252)
	stPostBureauCDEApproved                   = we.State(253)

	stPostBureauCDEApprovedPublished                   = we.State(255)
	stCustomerAcceptedOffer                            = we.State(260)
	stPersistedCustomerResponse                        = we.State(266)
	stLOCAccountCreationProcessing                     = we.State(270)
	stOperatingAccountCreated                          = we.State(271)
	stPersistedLOCAccountCreation                      = we.State(272)
	stLOCAccountCreationSuccess                        = we.State(274)
	stFetchCTOSCreditBureauDataProcessing              = we.State(278)
	stFetchCTOSCreditBureauDataCompleted               = we.State(279)
	stFetchCTOSCreditBureauDataFailed                  = we.State(280)
	stUpdatedFinexusPostCreditBureauDataFetch          = we.State(284)
	stUpdatedPostBureauApprovalInFinexus               = we.State(286)
	stUpdatedLoanOfferDetailsInFinexus                 = we.State(288)
	stFetchExperianECSRReportProcessing                = we.State(290)
	stFetchExperianECSRReportCompleted                 = we.State(291)
	stFetchExperianECSRReportFailed                    = we.State(292)
	stFetchExperianEISReportProcessing                 = we.State(294)
	stFetchExperianEISReportCompleted                  = we.State(295)
	stFetchExperianEISReportFailed                     = we.State(296)
	stDelayCompleted                                   = we.State(298)
	stApplciationExpiryPersisted                       = we.State(299)
	stPostBureauIncomeStatementUploadRequired          = we.State(304)
	stIncomeDocumentRequiredPublished                  = we.State(305)
	stIncomeDetailsReceived                            = we.State(307)
	stIncomeDetailsDerived                             = we.State(308)
	stPostIncomeDerivationFetchScoringModelDataSuccess = we.State(309)
	stPostIncomeDerivationBureauCDEProcessing          = we.State(310)
	stPostIncomeDerivationCDEApproved                  = we.State(311)
	stPostIncomeDerivationCDERejected                  = we.State(312)
	stPostIncomeDerivationCDEApprovedPublished         = we.State(313)
	stPostIncomeDerivationCDERejectedPublished         = we.State(314)
	stPersonalGuarantorLetterInitiated                 = we.State(315)
	stPostIncomeDerivationPGLetterInitiated            = we.State(322)
	stInitiateSignedPGLetterGeneration                 = we.State(323)
	stSignedPGLetterGenerated                          = we.State(324)

	// success states
	stLOCCreationSuccessPublished                = we.State(900)
	stLOCAccountCreationSuccessEmailNotified     = we.State(901)
	stLOCAccountCreationSuccessPushInboxNotified = we.State(920)

	// failure states
	stInterventionNeeded                  = we.State(500)
	stPreBureauCDERejected                = we.State(502)
	stPreBureauRejectionPublished         = we.State(503)
	stUpdatedPostBureauRejectionInFinexus = we.State(504)
	stPostBureauCDERejected               = we.State(505)
	stPostBureauCDERejectedPublished      = we.State(506)
	stCustomerRejectedOffer               = we.State(507)
	stOnboardingChecksRejected            = we.State(508)
	stApplicationExpired                  = we.State(509)
	stOnboardingRejectionPersisted        = we.State(510)
	stIncomeDerivationFailed              = we.State(511)
)

const (
	logFlexiCreditApplication = "flexiCreditApplicationLog"
)

var workflowID string

const (
	evNoNeed                         = we.EventNoNeed
	evCustomerResponseReceived       = we.Event(101)
	evLOCAccountCreation             = we.Event(103)
	evPresistLocMessage              = we.Event(104)
	evCreateOrchestrationApplication = we.Event(105)
	evPersistCreditBureauData        = we.Event(106)
	evStartAsyncWorkflow             = we.Event(107)
	evResumeUpdateOfferAsyncWorkflow = we.Event(108)
	evOnboardingRejected             = we.Event(109)
	evApplicationExpired             = we.Event(110)
	evIncomeDerivationCallback       = we.Event(111)
	evResumePostIncomeDerivation     = we.Event(112)
)

var (
	wfInit            = we.InitExecution
	wfGet             = we.GetExecution
	wfExecute         = we.Execute
	errInvalidContext = common.ErrInvalidContext
)

// WorkflowImpl defines update biz loan offer details workflow implementation.
type WorkflowImpl struct {
	StatsD                        statsd.Client                              `inject:"statsD"`
	AppConfig                     *config.AppConfig                          `inject:"config"`
	CustomerMasterClient          customerMaster.CustomerMaster              `inject:"client.customerMaster"`
	ProductMasterClient           productMaster.ProductMaster                `inject:"client.productMaster"`
	Store                         storage.DatabaseStore                      `inject:"DBStore"`
	BizFlexiCreditStatusPublisher publishers.Publisher                       `inject:"publishers.loanAppLifecycleEvent"`
	AccountServiceClient          accountServiceAPI.AccountService           `inject:"client.accountService"`
	GrabClient                    grabAPI.Grab                               `inject:"client.grabAPI"`
	BizFlexiCreditQueryGenerator  IBizFlexiCreditQueryGenerator              `inject:"bizFlexiCreditQueryGenerator"`
	WhitelistServiceClient        whitelistAPI.WhitelistService              `inject:"client.whitelistService"`
	CreditBureauServiceClient     creditBureauServiceAPI.CreditBureau        `inject:"client.creditBureauService"`
	ExperianClient                experianAPI.ExperianAdapter                `inject:"client.experian"`
	MlScoringServiceClient        mlScoringServiceAPI.MlScoringService       `inject:"client.bizMlScoringService"`
	NotificationService           notification.NotificationService           `inject:"client.notificationService"`
	CreditDecisionEngineClient    creditDecisionEngineAPI.CrDecisionEng      `inject:"client.creditDecisionEng"`
	EKYBServiceClient             ekybAPI.EkybService                        `inject:"client.ekybService"`
	TransactionStatementClient    transactionStatement.TransactionStatements `inject:"client.transactionStatement"`
}

// ConsumeBizLOCAccountCreationEvent resumes the workflow execution once the stream message is consumed
func ConsumeBizLOCAccountCreationEvent(ctx context.Context, data *dto.LOCAccountCreationStreamMessage) error {
	execData, err := wfGet(ctx, we.Execution{
		WorkflowID: workflowID,
		RequestID:  data.ReferenceID,
	})

	if err != nil {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Error when getting existing workflow: %s", err.Error()))
		return err
	}

	if execData.GetState() == stLOCAccountCreationProcessing {
		_, consumeErr := wfExecute(ctx, we.Execution{
			WorkflowID:     workflowID,
			RequestID:      data.ReferenceID,
			ExecutionEvent: evLOCAccountCreation,
		}, data)
		if consumeErr != nil {
			slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Workflow execute error in ConsumeLOCAccountCreationEvent: %s", consumeErr.Error()))
			return utils.CheckIfResourceNotFound(ctx, consumeErr, logFlexiCreditApplication, data.ReferenceID)
		}
	}

	return nil
}

// ConsumeCBSData consumes event from credit-bureau event stream and resumes workflow for bizflexicredit application
func ConsumeCBSData(ctx context.Context, cbsDataDTO *dto.CreditBureauEnquiryStreamMessage) error {
	_, err := wfExecute(ctx, we.Execution{
		WorkflowID:     workflowID,
		RequestID:      cbsDataDTO.ReferenceID,
		ExecutionEvent: evPersistCreditBureauData,
	}, cbsDataDTO)
	if err != nil {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Workflow execute error in ConsumeCBSData: %s", err.Error()))
		return err
	}
	return nil
}

func createAuditTrailReferenceID(requestID string, state string) string {
	return requestID + "_" + state
}

// Stats for tracking success and failure responses in LOC creation workflow
func (w *WorkflowImpl) Stats(ctx context.Context, status string, statusReason string) {
	slog.FromContext(ctx).Debug(logFlexiCreditApplication, fmt.Sprintf("publishing %s metric", status))
	w.StatsD.Count1(stats.WorkflowStatusContextTag, stats.CountKey,
		fmt.Sprintf("workflow:%s", logFlexiCreditApplication),
		fmt.Sprintf("status:%s", status),
		fmt.Sprintf("status_reason:%s", statusReason))
}
func saveToAudit(ctx context.Context, eventType string, source string, execData *ExecutionData, auditData []byte, eventVerdict string) {
	auditDataObj := &storage.AuditTrailEvent{
		EventName:     eventType,
		CustomerID:    execData.UserID,
		AuditData:     auditData,
		EventVerdict:  eventVerdict,
		WorkflowRunID: execData.CreateWorkflowRequest.Message.ReferenceID,
		WorkflowID:    workflowID,
		ReferenceID: createAuditTrailReferenceID(execData.CreateWorkflowRequest.Message.ReferenceID,
			strconv.Itoa(int(execData.GetState()))),
		EventSource:             source,
		OnboardingApplicationID: execData.CreateWorkflowRequest.Message.OnboardingApplicationID,
	}
	err := common.StoreAuditTrailEvent(ctx, auditDataObj, storage.AuditTrailEventDao)
	if err != nil {
		slog.FromContext(ctx).Error(constants.AuditTrailLogTag, "Failed to store audit event", slog.CustomTag("err", err), utils.GetTraceID(ctx))
	}
}

func updateStatusAndStatusReason(nextCtx *ExecutionData, status loanAppAPI.ApplicationStatus_ApplicationStatus, statusReason loanAppAPI.ApplicationStatusReason, increaseLimitAllowed bool) *ExecutionData {
	metadata := &dto.Metadata{}
	if nextCtx.Application.Metadata != nil {
		_ = json.Unmarshal(*nextCtx.Application.Metadata, metadata)
	}
	metadata.IncreaseLimitAllowed = increaseLimitAllowed
	jsonMetadata, _ := json.Marshal(metadata)
	appMetadata := json.RawMessage(jsonMetadata)
	nextCtx.Application.Status = string(status)
	nextCtx.Application.StatusReason = string(statusReason)
	nextCtx.Application.UpdatedAt = utils.CurrentDateAndTimeInUTC()
	nextCtx.Application.UpdatedBy = string(servicename.LoanApp)
	nextCtx.Application.Metadata = &appMetadata
	return nextCtx
}

// ConsumeOnboardingRejectionEvent consumes onboarding rejected event for biz flexi credit application and resume workflow
// nolint: dupl
func ConsumeOnboardingRejectionEvent(ctx context.Context, data *applicationStatusTransistionSchema.ApplicationStatusTransition) error {
	execData, getErr := wfGet(ctx, we.Execution{
		WorkflowID: workflowID,
		RequestID:  data.ApplicationID,
	})
	if getErr != nil {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Error when getting existing workflow: %s", getErr.Error()))
		return utils.CheckIfResourceNotFound(ctx, getErr, logFlexiCreditApplication, data.ApplicationID)
	}

	if execData.GetState() == stPostBureauCDEApprovedPublished ||
		execData.GetState() == stPostIncomeDerivationCDEApprovedPublished {
		_, executeErr := wfExecute(ctx, we.Execution{
			WorkflowID:     workflowID,
			RequestID:      data.ApplicationID,
			ExecutionEvent: evOnboardingRejected,
		}, data)
		if executeErr != nil {
			slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Workflow execute error in ConsumeOnboardingRejectionEvent: %s", executeErr.Error()))
			return utils.CheckIfResourceNotFound(ctx, executeErr, logFlexiCreditApplication, data.ApplicationID)
		}
	}
	return nil
}

// ConsumeApplicationExpiredEvent consumes application expired event for biz flexi credit application and resume workflow
// nolint: dupl
func ConsumeApplicationExpiredEvent(ctx context.Context, data *applicationStatusTransistionSchema.ApplicationStatusTransition) error {
	execData, getErr := wfGet(ctx, we.Execution{
		WorkflowID: workflowID,
		RequestID:  data.ApplicationID,
	})
	if getErr != nil {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Error when getting existing workflow: %s", getErr.Error()))
		return utils.CheckIfResourceNotFound(ctx, getErr, logFlexiCreditApplication, data.ApplicationID)
	}

	if execData.GetState() == stPostBureauCDEApprovedPublished ||
		execData.GetState() == stPostIncomeDerivationCDEApprovedPublished {
		_, executeErr := wfExecute(ctx, we.Execution{
			WorkflowID:     workflowID,
			RequestID:      data.ApplicationID,
			ExecutionEvent: evApplicationExpired,
		}, data)
		if executeErr != nil {
			slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Workflow execute error in ConsumeApplicationExpiredEvent: %s", executeErr.Error()))
			return utils.CheckIfResourceNotFound(ctx, executeErr, logFlexiCreditApplication, data.ApplicationID)
		}
	}
	return nil
}

// HandleIncomeDerivationEvent consumes event from perfios event stream and resumes workflow for flexiCredit application
// nolint:dupl
func HandleIncomeDerivationEvent(ctx context.Context, incomeDerivationData *dto.IncomeDerivationStreamMessage) error {
	onboardingApplicationID, err := common.FetchOnboardingApplicationIDFromApplicationID(ctx, incomeDerivationData.ApplicationID, logFlexiCreditApplication)
	if err != nil {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Application not found ApplicationID: %s while handleIncomeDerivationEvent, err: %s", incomeDerivationData.ApplicationID, err.Error()))
		return err
	}
	execData, getErr := wfGet(ctx, we.Execution{
		WorkflowID: workflowID,
		RequestID:  onboardingApplicationID,
	})
	if getErr != nil {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Error when getting existing workflow: %s", getErr.Error()))
		return utils.CheckIfResourceNotFound(ctx, getErr, logFlexiCreditApplication, onboardingApplicationID)
	}
	if execData.GetState() == stPostBureauCDEApprovedPublished ||
		execData.GetState() == stIncomeDocumentRequiredPublished {
		execData, err = wfExecute(ctx, we.Execution{
			WorkflowID:     workflowID,
			RequestID:      onboardingApplicationID,
			ExecutionEvent: evIncomeDerivationCallback,
		}, incomeDerivationData)
		if err != nil {
			slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Workflow execute failed in handleIncomeDerivationEvent, requestID: %s, err: %s", onboardingApplicationID, err.Error()))
			return utils.CheckIfResourceNotFound(ctx, err, logFlexiCreditApplication, incomeDerivationData.ApplicationID)
		}
		newCtx := utils.NewCtxWithLoggerAndSpan(ctx)
		if execData.GetState() == stIncomeDetailsReceived {
			gconcurrent.Go(newCtx, logFlexiCreditApplication, utils.ExecuteEventAsync(onboardingApplicationID, evResumePostIncomeDerivation, logFlexiCreditApplication, workflowID))
		}
	}
	return nil
}
