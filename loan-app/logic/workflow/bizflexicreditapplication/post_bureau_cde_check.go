// Package bizflexicreditapplication ...
// nolint:dupl
package bizflexicreditapplication

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/google/uuid"

	crDecisionEngAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	"gitlab.com/gx-regional/dakota/lending/loan-app/mapper"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	"gitlab.com/gx-regional/dakota/lending/loan-app/validation"
	"gitlab.myteksi.net/dakota/klient/errorhandling"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
	servusData "gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
	tags "gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// nolint: funlen
func (w *WorkflowImpl) initiatePostBureauCDECheck(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, "Invalid context passed in postBureauCDECheck state")
		return nil, errInvalidContext
	}
	nextCtx := currCtx.clone()

	ctx = slog.AddTagsToContext(ctx, tags.TraceID(nextCtx.CreateWorkflowRequest.Message.OnboardingApplicationID))
	postBureauCreditDecisionRequest := mapper.MapPostBureauCreditDecisionRequest(dto.CreditDecisionRequestMapperDTO{
		LoanApplicants:           nextCtx.CreateWorkflowRequest.Message.Application.Applicants,
		Products:                 nextCtx.CreateWorkflowRequest.Message.Application.Products,
		ReferenceID:              uuid.NewString(),
		ApplicationID:            nextCtx.ApplicationID,
		ApplicationType:          nextCtx.CreateWorkflowRequest.Message.Application.ApplicationType,
		AppCreationDate:          nextCtx.Application.CreatedAt,
		CountryCode:              nextCtx.CreateWorkflowRequest.Message.Application.CountryCode,
		Channel:                  nextCtx.CreateWorkflowRequest.Message.Application.Channel,
		Report:                   nextCtx.CreditBureauEnquiryStreamMessage.Report,
		BureauEnquiryDate:        nextCtx.CreditBureauEnquiryStreamMessage.BureauEnquiryDate,
		EcosystemDetails:         nextCtx.ApplicantsEcosystemDetails,
		ApplicationScoreResponse: nextCtx.ApplicationScoreResponse,
		PromoCode:                nextCtx.CreateWorkflowRequest.Message.Application.PromoCode,
		AcquisitionChannelID:     nextCtx.CreateWorkflowRequest.Message.Application.AcquisitionChannelID,
		ExperianEISReport:        nextCtx.ExperianEISGetOrderReportResponse,
		ExperianECSRReport:       nextCtx.ExperianECSRGetOrderReportResponse,
		ExperianSMENSReport:      nextCtx.ExperianSMENSGetOrderReportResponse,
		ExperianCBSCReport:       nextCtx.ExperianCBSCGetOrderReportResponse,
		ExperianIBSReport:        nextCtx.ExperianIBSGetOrderReportResponse,
	})
	//save request to audit trail
	auditData, auditErr := json.Marshal(postBureauCreditDecisionRequest)
	if auditErr == nil {
		saveToAudit(ctx, constants.PostBureauCDEEvent, constants.PostBureauCdeSource, nextCtx, auditData, constants.VerdictProcessing)
	} else {
		saveToAudit(ctx, constants.PostBureauCDEEvent, constants.PostBureauCdeSource, nextCtx, auditData, constants.VerdictFailed)
	}
	postBureauCreditDecisionResponse, err := w.CreditDecisionEngineClient.FICOCreditDecision(ctx, postBureauCreditDecisionRequest)
	if err != nil {
		saveToAudit(ctx, constants.PostBureauCDEEvent, constants.PostBureauCdeSource, nextCtx, []byte(err.Error()), constants.VerdictFailed)
		errObj, ok := err.(*errorhandling.Error)
		if ok {
			slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Error while post bureau check for biz flexi credit application id %s, due to %s with error code %d", nextCtx.ApplicationID, err.Error(), errObj.HTTPCode))
		} else {
			slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Error while post bureau check for biz flexi credit application id %s, due to %s", nextCtx.ApplicationID, err.Error()))
		}
		//save to audit trail
		auditData, auditErr = json.Marshal(err.Error())
		if auditErr == nil {
			saveToAudit(ctx, constants.PostBureauCDEEvent, constants.PostBureauCdeSource, nextCtx, auditData, constants.VerdictFailed)
		}
		return nil, err
	}

	nextCtx.PostBureauCreditDecisionRequest = postBureauCreditDecisionRequest
	nextCtx.PostBureauCreditDecisionResponse = postBureauCreditDecisionResponse

	//save to audit trail
	auditData, auditErr = json.Marshal(postBureauCreditDecisionResponse)
	if auditErr == nil {
		saveToAudit(ctx, constants.PostBureauCDEEvent, constants.PostBureauCdeSource, nextCtx, auditData, constants.VerdictCompleted)
	}
	nextCtx.SetState(stPostBureauCDEProcessing)
	return nextCtx, nil
}

// nolint: funlen
func (w *WorkflowImpl) initiatePostIncomeDerivationCDECheck(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, "Invalid context passed in initiatePostIncomeDerivationCDECheck state")
		return nil, errInvalidContext
	}
	nextCtx := currCtx.clone()
	ctx = slog.AddTagsToContext(ctx, tags.TraceID(nextCtx.CreateWorkflowRequest.Message.OnboardingApplicationID))
	creditDecisionRequestMapperDTO := dto.CreditDecisionRequestMapperDTO{
		LoanApplicants:           nextCtx.CreateWorkflowRequest.Message.Application.Applicants,
		Products:                 nextCtx.CreateWorkflowRequest.Message.Application.Products,
		ReferenceID:              uuid.NewString(),
		ApplicationID:            nextCtx.ApplicationID,
		ApplicationType:          nextCtx.CreateWorkflowRequest.Message.Application.ApplicationType,
		AppCreationDate:          nextCtx.Application.CreatedAt,
		CountryCode:              nextCtx.CreateWorkflowRequest.Message.Application.CountryCode,
		Channel:                  nextCtx.CreateWorkflowRequest.Message.Application.Channel,
		Report:                   nextCtx.CreditBureauEnquiryStreamMessage.Report,
		BureauEnquiryDate:        nextCtx.CreditBureauEnquiryStreamMessage.BureauEnquiryDate,
		EcosystemDetails:         nextCtx.ApplicantsEcosystemDetails,
		ApplicationScoreResponse: nextCtx.ApplicationScoreResponse,
		PromoCode:                nextCtx.CreateWorkflowRequest.Message.Application.PromoCode,
		AcquisitionChannelID:     nextCtx.CreateWorkflowRequest.Message.Application.AcquisitionChannelID,
		ExperianEISReport:        nextCtx.ExperianEISGetOrderReportResponse,
		ExperianECSRReport:       nextCtx.ExperianECSRGetOrderReportResponse,
		ExperianSMENSReport:      nextCtx.ExperianSMENSGetOrderReportResponse,
		ExperianCBSCReport:       nextCtx.ExperianCBSCGetOrderReportResponse,
		ExperianIBSReport:        nextCtx.ExperianIBSGetOrderReportResponse,
	}
	if nextCtx.IncomeDerivationStreamMessage != nil {
		creditDecisionRequestMapperDTO.IncomeDerivationStreamMessage = nextCtx.IncomeDerivationStreamMessage
	}
	enableBizFlexiCreditCdePostIncomeApplicantIncome := w.AppConfig.FeatureFlags.EnableBizFlexiCreditCdePostIncomeApplicantIncome
	postIncomeDerivationCDERequest := mapper.MapPostIncomeDerivationCreditDecisionRequest(creditDecisionRequestMapperDTO, enableBizFlexiCreditCdePostIncomeApplicantIncome, true)
	//save request to audit trail
	auditData, auditErr := json.Marshal(postIncomeDerivationCDERequest)
	if auditErr == nil {
		saveToAudit(ctx, constants.PostIncomeDerivationCDEEvent, constants.PostIncomeDerivationCdeSource, nextCtx, auditData, constants.VerdictProcessing)
	} else {
		saveToAudit(ctx, constants.PostIncomeDerivationCDEEvent, constants.PostIncomeDerivationCdeSource, nextCtx, auditData, constants.VerdictFailed)
	}
	postIncomeDerivationCDEResponse, err := w.CreditDecisionEngineClient.FICOCreditDecision(ctx, postIncomeDerivationCDERequest)
	if err != nil {
		saveToAudit(ctx, constants.PostIncomeDerivationCDEEvent, constants.PostIncomeDerivationCdeSource, nextCtx, []byte(err.Error()), constants.VerdictFailed)
		errObj, ok := err.(*errorhandling.Error)
		if ok {
			slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Error while post income derivation bureau check for biz flexi credit application id %s, due to %s with error code %d", nextCtx.ApplicationID, err.Error(), errObj.HTTPCode))
		} else {
			slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Error while post income derivation bureau check for biz flexi credit application id %s, due to %s", nextCtx.ApplicationID, err.Error()))
		}
		//save to audit trail
		auditData, auditErr = json.Marshal(err.Error())
		if auditErr == nil {
			saveToAudit(ctx, constants.PostIncomeDerivationCDEEvent, constants.PostIncomeDerivationCdeSource, nextCtx, auditData, constants.VerdictFailed)
		}
		return nil, err
	}
	nextCtx.PostIncomeCreditDecisionRequest = postIncomeDerivationCDERequest
	nextCtx.PostIncomeCreditDecisionResponse = postIncomeDerivationCDEResponse
	//save to audit trail
	auditData, auditErr = json.Marshal(postIncomeDerivationCDEResponse)
	if auditErr == nil {
		saveToAudit(ctx, constants.PostIncomeDerivationCDEEvent, constants.PostIncomeDerivationCdeSource, nextCtx, auditData, constants.VerdictCompleted)
	}
	nextCtx.SetState(stPostIncomeDerivationBureauCDEProcessing)
	return nextCtx, nil
}

// nolint: funlen
func (w *WorkflowImpl) completePostBureauCDECheck(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, "Invalid context passed in completePostBureauCDECheck state")
		return nil, errInvalidContext
	}
	nextCtx := currCtx.clone()

	ctx = slog.AddTagsToContext(ctx, tags.TraceID(nextCtx.CreateWorkflowRequest.Message.OnboardingApplicationID))

	errs := validation.ValidatePostBureauResponse(nextCtx.PostBureauCreditDecisionResponse, constants.BizFlexiCreditApplicationProductTypes)
	if len(errs) != 0 {
		saveToAudit(ctx, constants.PostBureauCDEEvent, constants.PostBureauCdeSource, nextCtx, []byte(utils.ToJSON(errs)), constants.VerdictFailed)
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Error validating post-bureau response for biz flexi credit only application %v", utils.ToJSON(errs)), utils.GetTraceID(ctx))
		nextCtx.SetState(stInterventionNeeded)
		return nextCtx, nil
	}

	products := common.GetProductByType(nextCtx.PostBureauCreditDecisionResponse.Message.Application.Products, crDecisionEngAPI.ProductType_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT)
	interventionNeeded := false

	if products[0].ProductDecision != nil {
		switch *products[0].ProductDecision.RecommendedCreditDecision {
		case string(constants.CDERecommendedDecisionApprove), string(constants.CDERecommendedDecisionApproved):
			if isPvtLtdApplication(string(nextCtx.CreateWorkflowRequest.Message.Application.BizApplicationData.ApplicationCategory)) {
				nextCtx.SetState(stPersonalGuarantorLetterInitiated)
			} else {
				nextCtx.SetState(stPostBureauCDEApproved)
			}
			nextCtx = updateStatusAndStatusReason(nextCtx, api.ApplicationStatus_ApplicationStatus_PENDING_ACCEPTANCE, api.ApplicationStatusReason_CDE_POST_BUREAU_APPROVED, true)
		case string(constants.CDERecommendedDecisionRejected):
			nextCtx.SetState(stPostBureauCDERejected)
			nextCtx = updateStatusAndStatusReason(nextCtx, api.ApplicationStatus_ApplicationStatus_REJECTED, api.ApplicationStatusReason_CDE_POST_BUREAU_REJECTED, false)
		case string(constants.CDERecomendedDecisionDocUpload):
			if constants.EnableBizBankStatementsUpload {
				nextCtx.SetState(stPostBureauIncomeStatementUploadRequired)
				nextCtx = updateStatusAndStatusReason(nextCtx, api.ApplicationStatus_ApplicationStatus_PROCESSING, api.ApplicationStatusReason_INCOME_DOCUMENT_REQUIRED, false)
			} else {
				interventionNeeded = true
			}
		default:
			interventionNeeded = true
		}
	}
	if interventionNeeded {
		nextCtx.SetState(stInterventionNeeded)
		nextCtx = updateStatusAndStatusReason(nextCtx, api.ApplicationStatus_ApplicationStatus_PROCESSING, api.ApplicationStatusReason_CDE_PRE_BUREAU_APPROVED, false)
	}

	var stmts []*storage.TransactionStmt
	stmts = append(stmts, w.BizFlexiCreditQueryGenerator.CreateUpdateApplicationStatement(nextCtx.Application))
	stmts = append(stmts, w.BizFlexiCreditQueryGenerator.CreateInsertApplicationDecisionStatement(nextCtx.PostBureauCreditDecisionResponse, nextCtx.ApplicationID))

	// insert offer
	if nextCtx.State == stPostBureauCDEApproved || nextCtx.State == stPersonalGuarantorLetterInitiated {
		stmts = append(stmts, w.BizFlexiCreditQueryGenerator.CreateInsertOfferStatement(nextCtx.PostBureauCreditDecisionResponse.Message.Application.Products[0], nextCtx.Application.ApplicationID, string(api.LoanOfferStatus_PENDING_ACCEPTANCE), w.AppConfig.LocaleConfig.DefaultCurrencyCode)...)
	}
	err := w.Store.ExecuteTransaction(ctx, w.AppConfig, stmts)

	if err != nil {
		saveToAudit(ctx, constants.PostBureauCDEEvent, constants.PostBureauCdeSource, nextCtx, []byte(err.Error()), constants.VerdictFailed)
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Error updating application or application decision or applicant decision in db with err: %s", err.Error()), utils.GetTraceID(ctx))
		return nil, err
	}

	return nextCtx, nil
}

// nolint: funlen
func (w *WorkflowImpl) completePostIncomeDerivationCDECheck(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, "Invalid context passed in completePostIncomeDerivationCDECheck state")
		return nil, errInvalidContext
	}
	nextCtx := currCtx.clone()

	ctx = slog.AddTagsToContext(ctx, tags.TraceID(nextCtx.CreateWorkflowRequest.Message.OnboardingApplicationID))

	errs := validation.ValidatePostBureauResponse(nextCtx.PostIncomeCreditDecisionResponse, constants.BizFlexiCreditApplicationProductTypes)
	if len(errs) != 0 {
		saveToAudit(ctx, constants.PostIncomeDerivationCDEEvent, constants.PostIncomeDerivationCdeSource, nextCtx, []byte(utils.ToJSON(errs)), constants.VerdictFailed)
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Error validating post-bureau response post income derivation for biz flexi credit only application %v", utils.ToJSON(errs)), utils.GetTraceID(ctx))
		nextCtx.SetState(stInterventionNeeded)
		return nextCtx, nil
	}

	products := common.GetProductByType(nextCtx.PostIncomeCreditDecisionResponse.Message.Application.Products, crDecisionEngAPI.ProductType_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT)
	interventionNeeded := false

	if products[0].ProductDecision != nil {
		switch *products[0].ProductDecision.RecommendedCreditDecision {
		case string(constants.CDERecommendedDecisionApprove), string(constants.CDERecommendedDecisionApproved):
			if isPvtLtdApplication(string(nextCtx.CreateWorkflowRequest.Message.Application.BizApplicationData.ApplicationCategory)) {
				nextCtx.SetState(stPostIncomeDerivationPGLetterInitiated)
			} else {
				nextCtx.SetState(stPostIncomeDerivationCDEApproved)
			}
			nextCtx = updateStatusAndStatusReason(nextCtx, api.ApplicationStatus_ApplicationStatus_PENDING_ACCEPTANCE, api.ApplicationStatusReason_POST_INCOME_DERIVATION_CDE_APPROVED, false)
		case string(constants.CDERecommendedDecisionRejected):
			nextCtx.SetState(stPostIncomeDerivationCDERejected)
			nextCtx = updateStatusAndStatusReason(nextCtx, api.ApplicationStatus_ApplicationStatus_REJECTED, api.ApplicationStatusReason_POST_INCOME_DERIVATION_CDE_REJECTED, false)
		default:
			interventionNeeded = true
		}
	}
	if interventionNeeded {
		nextCtx.SetState(stInterventionNeeded)
		nextCtx = updateStatusAndStatusReason(nextCtx, api.ApplicationStatus_ApplicationStatus_PROCESSING, api.ApplicationStatusReason_POST_INCOME_DERIVATION_CDE_APPROVED, false)
	}

	var stmts []*storage.TransactionStmt
	stmts = append(stmts, w.BizFlexiCreditQueryGenerator.CreateUpdateApplicationStatement(nextCtx.Application))
	stmts = append(stmts, w.BizFlexiCreditQueryGenerator.CreateInsertApplicationDecisionStatement(nextCtx.PostIncomeCreditDecisionResponse, nextCtx.ApplicationID))

	// insert offer
	var fetchErr error
	if nextCtx.State == stPostIncomeDerivationCDEApproved || nextCtx.State == stPostIncomeDerivationPGLetterInitiated {
		stmts, fetchErr = appendOfferStmt(ctx, nextCtx, stmts, w)
		if fetchErr != nil {
			return nil, fetchErr
		}
	}
	err := w.Store.ExecuteTransaction(ctx, w.AppConfig, stmts)

	if err != nil {
		saveToAudit(ctx, constants.PostIncomeDerivationCDEEvent, constants.PostIncomeDerivationCdeSource, nextCtx, []byte(err.Error()), constants.VerdictFailed)
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Error updating application or application decision or applicant decision in db post income derivation with err: %s", err.Error()), utils.GetTraceID(ctx))
		return nil, err
	}

	return nextCtx, nil
}

func appendOfferStmt(ctx context.Context, nextCtx *ExecutionData, stmts []*storage.TransactionStmt, w *WorkflowImpl) ([]*storage.TransactionStmt, error) {
	offerDetails, err := common.FetchOfferDetails(ctx, []servusData.Condition{servusData.EqualTo("ApplicationID", nextCtx.ApplicationID)}, logFlexiCreditApplication)
	if err != nil {
		e, ok := err.(servus.ServiceError)
		if ok && e.Code == commonErr.ErrOfferDetailsNotFound.Code {
			slog.FromContext(ctx).Warn(logFlexiCreditApplication, "No offer details found for the given application ID", utils.GetTraceID(ctx))
			stmts = append(stmts, w.BizFlexiCreditQueryGenerator.CreateInsertOfferStatement(nextCtx.PostIncomeCreditDecisionResponse.Message.Application.Products[0], nextCtx.Application.ApplicationID, string(api.LoanOfferStatus_PENDING_ACCEPTANCE), w.AppConfig.LocaleConfig.DefaultCurrencyCode)...)
		} else {
			slog.FromContext(ctx).Warn(logFlexiCreditApplication, "Error fetching offer details for the given application ID", utils.GetTraceID(ctx))
			return nil, err
		}
	}
	if len(offerDetails) > 0 {
		stmts = append(stmts, w.BizFlexiCreditQueryGenerator.UpdateOfferStatement(nextCtx.PostIncomeCreditDecisionResponse.Message.Application.Products[0], nextCtx.Application.ApplicationID, string(api.LoanOfferStatus_PENDING_ACCEPTANCE), w.AppConfig.LocaleConfig.DefaultCurrencyCode)...)
	}
	return stmts, nil
}
