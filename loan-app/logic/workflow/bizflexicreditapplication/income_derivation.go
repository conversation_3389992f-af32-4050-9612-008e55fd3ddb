package bizflexicreditapplication

import (
	"context"

	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	incomeDerivationEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/income_derivation_event"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	we "gitlab.myteksi.net/dakota/workflowengine"
)

func (w *WorkflowImpl) persistIncomeDerivation(ctx context.Context, transitionID string, execData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, "Invalid context passed in persistIncomeDerivation state")
		return nil, errInvalidContext
	}

	nextCtx := currCtx.clone()

	streamMessage, ok := params.(*dto.IncomeDerivationStreamMessage)
	if !ok {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, "Invalid params passed in persistIncomeDerivation state", utils.GetTraceID(ctx))
		return nil, common.ErrInvalidApplicationTransitionDtoPassed
	}

	nextCtx.IncomeDerivationStreamMessage = streamMessage
	nextCtx.SetState(stIncomeDetailsReceived)
	saveToAudit(ctx, constants.RetrievedPerfiosReportEvent, constants.PerfiosSource, nextCtx, []byte("{}"), constants.VerdictCompleted)
	return nextCtx, nil
}

func (w *WorkflowImpl) resumeIncomeDerivation(ctx context.Context, transitionID string, execData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, "Invalid context passed in resumeIncomeDerivation state")
		return nil, errInvalidContext
	}
	nextCtx := currCtx.clone()

	switch currCtx.IncomeDerivationStreamMessage.StatusReason {
	case incomeDerivationEvent.ReportVerified, incomeDerivationEvent.ReferStatement, incomeDerivationEvent.FraudStatement:
		nextCtx.SetState(stIncomeDetailsDerived)
	default:
		nextCtx.SetState(stIncomeDerivationFailed)
	}
	return nextCtx, nil
}
