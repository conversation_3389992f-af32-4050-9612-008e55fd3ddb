package bizflexicreditapplication

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/income_derivation_event"

	"gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/dakota/workflowengine/common/uuid"
)

func Test_persistIncomeDerivationData(t *testing.T) {
	scenarios := []struct {
		desc          string
		params        interface{}
		expectedErr   error
		expectedState workflowengine.State
	}{
		{
			desc: "Success: stream message persisted",
			params: &dto.IncomeDerivationStreamMessage{
				Status:       income_derivation_event.Success,
				StatusReason: income_derivation_event.ReportVerified,
			},
			expectedState: stIncomeDetailsReceived,
		},
	}
	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			w := WorkflowImpl{}
			nextCtx, err := w.persistIncomeDerivation(context.Background(), "transitionID", &ExecutionData{
				CreateWorkflowRequest: &api.CreateWorkflowRequest{
					Message: &api.CreateWorkflowRequest_Message{OnboardingApplicationID: uuid.NewString()},
				},
			}, scenario.params)
			if scenario.expectedErr != nil {
				assert.Nil(t, nextCtx)
				assert.Equal(t, scenario.expectedErr.Error(), err.Error())
			} else {
				assert.Nil(t, err)
				assert.Equal(t, scenario.expectedState, nextCtx.GetState())
			}
		})
	}
}
