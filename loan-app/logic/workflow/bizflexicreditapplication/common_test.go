package bizflexicreditapplication

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	loanAppAPI "gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/requests"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	"gitlab.myteksi.net/dakota/workflowengine"
)

func TestConsumeBizLOCAccountCreationEvent(t *testing.T) {
	errDummy = errors.New("simulate error")
	scenarios := []struct {
		desc        string
		errExecute  error
		errGet      error
		expectedErr error
	}{
		{
			desc:        "nil case",
			errExecute:  nil,
			expectedErr: nil,
		},
		{
			desc:        "error case",
			errExecute:  errDummy,
			expectedErr: errDummy,
		},
	}

	for _, scenario := range scenarios {
		description := fmt.Sprintf("test case #%v", scenario.desc)
		testcase := scenario
		wfGet = func(ctx context.Context, e workflowengine.Execution) (workflowengine.ExecutionData, error) {
			return &ExecutionData{
				State: stLOCAccountCreationProcessing,
			}, scenario.errGet
		}
		wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
			return &ExecutionData{}, scenario.errExecute
		}
		wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
			return nil, testcase.errExecute
		}
		accountStreamMsg := &dto.LOCAccountCreationStreamMessage{
			AccountID: uuid.NewString(),
		}

		resp := ConsumeBizLOCAccountCreationEvent(context.Background(), accountStreamMsg)
		assert.Equal(t, testcase.expectedErr, resp, description)
	}
}

func Test_ConsumeCBSData(t *testing.T) {
	scenarios := []struct {
		desc        string
		errExecute  error
		expectedErr error
	}{
		{desc: "happy case", errExecute: nil, expectedErr: nil},
		{desc: "error case", errExecute: errDummy, expectedErr: errDummy},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
				return nil, scenario.errExecute
			}
			cbsDataDTO := &dto.CreditBureauEnquiryStreamMessage{}
			resp := ConsumeCBSData(context.Background(), cbsDataDTO)
			assert.Equal(t, scenario.expectedErr, resp)
		})
	}
}

func Test_updateStatusAndStatusReason(t *testing.T) {
	scenarios := []struct {
		desc                 string
		expectedStatus       string
		expectedStatusReason string
		status               loanAppAPI.ApplicationStatus_ApplicationStatus
		statusReason         loanAppAPI.ApplicationStatusReason
	}{
		{
			desc:                 "Success: updates received",
			status:               loanAppAPI.ApplicationStatus_ApplicationStatus_PROCESSING,
			statusReason:         loanAppAPI.ApplicationStatusReason_CDE_PRE_BUREAU_APPROVED,
			expectedStatus:       string(loanAppAPI.ApplicationStatus_ApplicationStatus_PROCESSING),
			expectedStatusReason: string(loanAppAPI.ApplicationStatusReason_CDE_PRE_BUREAU_APPROVED),
		},
	}
	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			nextCtx := updateStatusAndStatusReason(&ExecutionData{
				Application: &storage.Application{
					Status:       string(scenario.status),
					StatusReason: string(scenario.statusReason),
				},
			}, scenario.status, scenario.statusReason, false)
			assert.Equal(t, scenario.expectedStatus, nextCtx.Application.Status)
			assert.Equal(t, scenario.expectedStatusReason, nextCtx.Application.StatusReason)
		})
	}
}

func Test_saveToAudit(t *testing.T) {
	type args struct {
		ctx          context.Context
		eventType    string
		source       string
		execData     *ExecutionData
		auditData    []byte
		eventVerdict string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "Valid audit data",
			args: args{
				ctx:       context.Background(),
				eventType: "test_event",
				source:    "test_source",
				execData: &ExecutionData{
					CreateWorkflowRequest: requests.SampleCreateWorkflowRequestWithBizData(),
				},
				auditData:    []byte("test_audit_data"),
				eventVerdict: constants.VerdictCompleted,
			},
		},
		{
			name: "Empty audit data",
			args: args{
				ctx:       context.Background(),
				eventType: "test_event",
				source:    "test_source",
				execData: &ExecutionData{
					CreateWorkflowRequest: requests.SampleCreateWorkflowRequestWithBizData(),
				},
				auditData:    []byte{},
				eventVerdict: constants.VerdictFailed,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockAuditTrailDao := &storage.MockIAuditTrailEventDAO{}
			mockAuditTrailDao.On("Save", mock.Anything, mock.Anything).Return(nil)
			storage.AuditTrailEventDao = mockAuditTrailDao
			saveToAudit(tt.args.ctx, tt.args.eventType, tt.args.source, tt.args.execData, tt.args.auditData, tt.args.eventVerdict)
			if len(tt.args.auditData) != 0 {
				assert.NotEmpty(t, tt.args.auditData)
			} else {
				assert.Empty(t, tt.args.auditData)
			}
		})
	}
}

func Test_ConsumeOnboardingRejectionEvent(t *testing.T) {
	scenarios := []struct {
		desc        string
		errExecute  error
		errGet      error
		expectedErr error
	}{
		{desc: "happy case", errExecute: nil, expectedErr: nil},
		{desc: "error case", errGet: errDummy, expectedErr: errDummy},
		{desc: "error case", errExecute: errDummy, expectedErr: errDummy},
		{desc: "workflow found but workflowID is different", errExecute: errors.New("loadWorkflowByTransID: workflowID does not match"), expectedErr: nil},
		{desc: "workflow not found", errExecute: workflowengine.ErrResourceNotFound, expectedErr: nil},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			wfGet = func(ctx context.Context, e workflowengine.Execution) (workflowengine.ExecutionData, error) {
				return &ExecutionData{
					State: stPostBureauCDEApprovedPublished,
				}, scenario.errGet
			}
			wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
				return &ExecutionData{}, scenario.errExecute
			}
			data := &applicationStatusTransistionSchema.ApplicationStatusTransition{}
			resp := ConsumeOnboardingRejectionEvent(context.Background(), data)
			assert.Equal(t, scenario.expectedErr, resp)
		})
	}
}

func Test_ConsumeApplicationExpiredEvent(t *testing.T) {
	scenarios := []struct {
		desc        string
		errExecute  error
		errGet      error
		expectedErr error
	}{
		{desc: "happy case", errExecute: nil, expectedErr: nil},
		{desc: "error case", errGet: errDummy, expectedErr: errDummy},
		{desc: "error case", errExecute: errDummy, expectedErr: errDummy},
		{desc: "workflow found but workflowID is different", errExecute: errors.New("loadWorkflowByTransID: workflowID does not match"), expectedErr: nil},
		{desc: "workflow not found", errExecute: workflowengine.ErrResourceNotFound, expectedErr: nil},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			wfGet = func(ctx context.Context, e workflowengine.Execution) (workflowengine.ExecutionData, error) {
				return &ExecutionData{
					State: stPostBureauCDEApprovedPublished,
				}, scenario.errGet
			}
			wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
				return &ExecutionData{}, scenario.errExecute
			}
			data := &applicationStatusTransistionSchema.ApplicationStatusTransition{}
			resp := ConsumeApplicationExpiredEvent(context.Background(), data)
			assert.Equal(t, scenario.expectedErr, resp)
		})
	}
}
