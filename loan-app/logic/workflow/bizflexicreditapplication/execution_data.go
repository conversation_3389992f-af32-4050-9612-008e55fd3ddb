package bizflexicreditapplication

import (
	"encoding/json"

	creditDecisionEngineAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	mlScoringServiceAPI "gitlab.com/gx-regional/dakota/lending/loan-app/external/mlscoringservice/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	experianAPI "gitlab.myteksi.net/dakota/experian-adapter/api"
	creditBureauServiceAPI "gitlab.myteksi.net/dakota/lending/credit-bureau/api"
	accountServiceAPI "gitlab.myteksi.net/dakota/lending/external/corebanking/accountservice"
	"gitlab.myteksi.net/dakota/workflowengine"
)

// ExecutionData is execution data for update biz loan offer details workflow
type ExecutionData struct {
	CreateWorkflowRequest                    *api.CreateWorkflowRequest
	UpdateLoanOfferDetailsReq                *api.UpdateLoanOfferDetailsRequest
	State                                    workflowengine.State
	ErrorCode                                string
	ErrorMsg                                 string
	Status                                   string
	StatusReason                             string
	StatusReasonDescription                  string
	OldBRN                                   string
	ApplicantDetails                         *storage.Applicant
	OfferDetails                             *storage.Offer
	UserID                                   string
	BifNumber                                string
	CreateLOCAccountRequest                  *accountServiceAPI.CreateLOCAccountRequest
	CreateLOCAccountResponse                 *accountServiceAPI.CreateLOCAccountResponse
	LOCAccountCreationStreamMessage          *dto.LOCAccountCreationStreamMessage
	Application                              *storage.Application
	ApplicationID                            string
	ApplicantsEcosystemDetails               []*storage.ApplicantEcoSystemDetails
	CreditBureauEnquiryRequest               *creditBureauServiceAPI.ApplicantEnquiryRequest
	CreditBureauEnquiryResponse              *creditBureauServiceAPI.ApplicantEnquiryResponse
	CreditBureauEnquiryStreamMessage         *dto.CreditBureauEnquiryStreamMessage
	FlexiCreditProductParameters             map[string]string
	ExperianECSRInternalOrderID              string
	ExperianECSROrderReportRequest           *experianAPI.OrderReportRequest
	ExperianECSROrderReportResponse          *experianAPI.OrderReportResponse
	ExperianECSRGetOrderReportRequest        *experianAPI.GetOrderReportRequest
	ExperianECSRGetOrderReportResponse       *experianAPI.GetOrderReportResponse
	ExperianEISOrderReportRequest            *experianAPI.OrderReportRequest
	ExperianEISOrderReportResponse           *experianAPI.OrderReportResponse
	ExperianEISGetOrderReportRequest         *experianAPI.GetOrderReportRequest
	ExperianEISGetOrderReportResponse        *experianAPI.GetOrderReportResponse
	ExperianIBSOrderReportRequest            *experianAPI.OrderReportRequest
	ExperianIBSOrderReportResponse           *experianAPI.OrderReportResponse
	ExperianIBSGetOrderReportRequest         *experianAPI.GetOrderReportRequest
	ExperianIBSGetOrderReportResponse        *experianAPI.GetOrderReportResponse
	ExperianSMENSOrderReportRequest          *experianAPI.OrderReportRequest
	ExperianSMENSOrderReportResponse         *experianAPI.OrderReportResponse
	ExperianSMENSGetOrderReportRequest       *experianAPI.GetOrderReportRequest
	ExperianSMENSGetOrderReportResponse      *experianAPI.GetOrderReportResponse
	ExperianCBSCOrderReportRequest           *experianAPI.OrderReportRequest
	ExperianCBSCOrderReportResponse          *experianAPI.OrderReportResponse
	ExperianCBSCGetOrderReportRequest        *experianAPI.GetOrderReportRequest
	ExperianCBSCGetOrderReportResponse       *experianAPI.GetOrderReportResponse
	ApplicationStatusTransitionStreamMessage *applicationStatusTransistionSchema.ApplicationStatusTransition
	ApplicationScoreResponse                 *mlScoringServiceAPI.GetApplicationScoreResponse
	ApplicationScoreRequest                  *mlScoringServiceAPI.GetApplicationScoreRequest
	PreBureauCreditDecisionRequest           *creditDecisionEngineAPI.FICOCreditDecisionRequest
	PreBureauCreditDecisionResponse          *creditDecisionEngineAPI.FICOCreditDecisionResponse
	PostBureauCreditDecisionRequest          *creditDecisionEngineAPI.FICOCreditDecisionRequest
	PostBureauCreditDecisionResponse         *creditDecisionEngineAPI.FICOCreditDecisionResponse
	PostIncomeCreditDecisionRequest          *creditDecisionEngineAPI.FICOCreditDecisionRequest
	PostIncomeCreditDecisionResponse         *creditDecisionEngineAPI.FICOCreditDecisionResponse
	IncomeDerivationStreamMessage            *dto.IncomeDerivationStreamMessage
	PersonalGuarantorFileID                  string
	PGLetterGenerationDate                   string
	PGLetterSignedDate                       string
}

// GetState ...
func (p *ExecutionData) GetState() workflowengine.State {
	return p.State
}

// SetState ...
func (p *ExecutionData) SetState(state workflowengine.State) {
	p.State = state
}

// Marshal ...
func (p *ExecutionData) Marshal() ([]byte, error) {
	return json.Marshal(p)
}

// Unmarshal ...
func (p *ExecutionData) Unmarshal(byteData []byte) error {
	return json.Unmarshal(byteData, p)
}

// clone ...
func (p *ExecutionData) clone() *ExecutionData {
	newCtx := *p
	return &newCtx
}
