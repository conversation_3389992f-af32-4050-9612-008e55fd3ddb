// Package bizflexicreditapplication ...
// nolint:dupl
package bizflexicreditapplication

import (
	"context"
	"fmt"

	loanAppAPI "gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	we "gitlab.myteksi.net/dakota/workflowengine"
	tags "gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

func (w *WorkflowImpl) persistOnboardingRejectionEvent(ctx context.Context, transitionID string, execData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, "Invalid context passed in handleOnboardingRejection state")
		return nil, errInvalidContext
	}

	nextCtx := currCtx.clone()
	ctx = slog.AddTagsToContext(ctx, tags.TraceID(nextCtx.CreateWorkflowRequest.Message.OnboardingApplicationID))

	applicationStatusTransition, ok := params.(*applicationStatusTransistionSchema.ApplicationStatusTransition)
	if !ok {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, "Invalid params passed in persistOnboardingEvent state", utils.GetTraceID(ctx))
		return nil, common.ErrInvalidApplicationTransitionDtoPassed
	}

	nextCtx.ApplicationStatusTransitionStreamMessage = applicationStatusTransition
	nextCtx.SetState(stOnboardingRejectionPersisted)
	return nextCtx, nil
}

func (w *WorkflowImpl) handleOnboardingRejection(ctx context.Context, transitionID string, execData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, "Invalid context passed in handleOnboardingRejection state", utils.GetTraceID(ctx))
		return nil, errInvalidContext
	}

	nextCtx := currCtx.clone()
	ctx = slog.AddTagsToContext(ctx, tags.TraceID(nextCtx.CreateWorkflowRequest.Message.OnboardingApplicationID))
	nextCtx = updateStatusAndStatusReason(nextCtx, loanAppAPI.ApplicationStatus_ApplicationStatus_REJECTED, loanAppAPI.ApplicationStatusReason(nextCtx.ApplicationStatusTransitionStreamMessage.RejectionReason), false)

	var stmts []*storage.TransactionStmt
	stmts = append(stmts, w.BizFlexiCreditQueryGenerator.CreateUpdateApplicationStatement(nextCtx.Application))
	stmts = append(stmts, w.BizFlexiCreditQueryGenerator.CreateUpdateOfferStatement(nextCtx.Application))

	err := w.Store.ExecuteTransaction(ctx, w.AppConfig, stmts)
	if err != nil {
		saveToAudit(ctx, constants.OnboardingRejectionEvent, constants.OnbardingSource, nextCtx, []byte(err.Error()), constants.VerdictFailed)
		slog.FromContext(ctx).Warn(logFlexiCreditApplication, fmt.Sprintf("Error in update application status and offer status: %s", err.Error()), utils.GetTraceID(ctx))
		return nil, err
	}

	nextCtx.SetState(stOnboardingChecksRejected)
	return nextCtx, nil
}
