package bizflexicreditapplication

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	mocks "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api/mock"
	loanAppAPI "gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	mlScoringAPI "gitlab.com/gx-regional/dakota/lending/loan-app/external/mlscoringservice/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/requests"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/resources"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	incomeDerivationEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/income_derivation_event"

	"gitlab.myteksi.net/dakota/lending/common/constant"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/workflowengine"
)

func Test_initiatePostBureauCDECheck(t *testing.T) {
	scenarios := []struct {
		desc                             string
		ficoError                        error
		ficoResponse                     api.FICOCreditDecisionResponse
		expectedErr                      error
		expectedState                    workflowengine.State
		postBureauCreditDecisionResponse api.FICOCreditDecisionResponse
		storeError                       error
		countryCode                      string
		currencyCode                     string
	}{
		{
			desc:          "SG: Success: fico response",
			countryCode:   "SG",
			currencyCode:  "SGD",
			ficoResponse:  resources.SamplePostBureauResponse(),
			expectedState: stPostBureauCDEProcessing,
		},
		{
			desc:         "SG: Failure: failure response from fico",
			countryCode:  "SG",
			currencyCode: "SGD",
			ficoError:    errDummy,
			expectedErr:  errDummy,
		},
		{
			desc:          "MY: Success: mock fico response",
			countryCode:   "MY",
			currencyCode:  "MYR",
			ficoResponse:  resources.SamplePostBureauResponse(),
			expectedState: stPostBureauCDEProcessing,
		},
	}
	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			crDecisionEngMock := mocks.CrDecisionEng{}
			crDecisionEngMock.On("FICOCreditDecision", mock.Anything, mock.Anything).Return(&scenario.ficoResponse, scenario.ficoError)
			mockStore := &storage.MockDatabaseStore{}
			mockStore.On("ExecuteTransaction", mock.Anything, mock.Anything, mock.Anything).Return(scenario.storeError)
			mockAuditTrailDao := &storage.MockIAuditTrailEventDAO{}
			mockAuditTrailDao.On("Save", mock.Anything, mock.Anything).Return(nil)
			storage.AuditTrailEventDao = mockAuditTrailDao
			w := WorkflowImpl{
				CreditDecisionEngineClient: &crDecisionEngMock,
				Store:                      mockStore,
				AppConfig: &config.AppConfig{
					LocaleConfig: config.LocaleConfig{
						DefaultCurrencyCode: scenario.countryCode,
						DefaultCountryCode:  scenario.currencyCode,
					},
				},
			}
			nextCtx, err := w.initiatePostBureauCDECheck(context.Background(), "transitionID", &ExecutionData{
				ApplicationID:         "application-id",
				CreateWorkflowRequest: requests.SampleCreateWorkflowRequest(),
				Application:           resources.SampleBizFlexiCreditStorageApplicationData()[4],
				CreditBureauEnquiryStreamMessage: &dto.CreditBureauEnquiryStreamMessage{
					BureauEnquiryDate: utils.CurrentDateAndTimeInUTC(),
					Report:            map[string]string{},
				},
				ApplicantsEcosystemDetails: []*storage.ApplicantEcoSystemDetails{},
				ApplicationScoreResponse: &mlScoringAPI.GetApplicationScoreResponse{
					Application: &mlScoringAPI.Application{Applicants: []mlScoringAPI.Applicant{}},
				},
			}, nil)
			if scenario.expectedErr != nil {
				assert.Nil(t, nextCtx)
				assert.Equal(t, scenario.expectedErr.Error(), err.Error())
			} else {
				assert.Nil(t, err)
				assert.Equal(t, scenario.expectedState, nextCtx.GetState())
			}
		})
	}
}

func Test_PostIncomeDerivationInitiateCDECheck(t *testing.T) {
	scenarios := []struct {
		desc                             string
		ficoError                        error
		ficoResponse                     api.FICOCreditDecisionResponse
		expectedErr                      error
		expectedState                    workflowengine.State
		postBureauCreditDecisionResponse api.FICOCreditDecisionResponse
		storeError                       error
		countryCode                      string
		currencyCode                     string
	}{
		{
			desc:          "SG: Success: fico response",
			countryCode:   "SG",
			currencyCode:  "SGD",
			ficoResponse:  resources.SamplePostBureauResponse(),
			expectedState: stPostIncomeDerivationBureauCDEProcessing,
		},
		{
			desc:         "SG: Failure: failure response from fico",
			countryCode:  "SG",
			currencyCode: "SGD",
			ficoError:    errDummy,
			expectedErr:  errDummy,
		},
		{
			desc:          "MY: Success: mock fico response",
			countryCode:   "MY",
			currencyCode:  "MYR",
			ficoResponse:  resources.SamplePostBureauResponse(),
			expectedState: stPostIncomeDerivationBureauCDEProcessing,
		},
	}
	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			crDecisionEngMock := mocks.CrDecisionEng{}
			crDecisionEngMock.On("FICOCreditDecision", mock.Anything, mock.Anything).Return(&scenario.ficoResponse, scenario.ficoError)
			mockStore := &storage.MockDatabaseStore{}
			mockStore.On("ExecuteTransaction", mock.Anything, mock.Anything, mock.Anything).Return(scenario.storeError)
			mockAuditTrailDao := &storage.MockIAuditTrailEventDAO{}
			mockAuditTrailDao.On("Save", mock.Anything, mock.Anything).Return(nil)
			storage.AuditTrailEventDao = mockAuditTrailDao
			w := WorkflowImpl{
				CreditDecisionEngineClient: &crDecisionEngMock,
				Store:                      mockStore,
				AppConfig: &config.AppConfig{
					LocaleConfig: config.LocaleConfig{
						DefaultCurrencyCode: scenario.countryCode,
						DefaultCountryCode:  scenario.currencyCode,
					},
				},
			}
			nextCtx, err := w.initiatePostIncomeDerivationCDECheck(context.Background(), "transitionID", &ExecutionData{
				ApplicationID:         "application-id",
				CreateWorkflowRequest: requests.SampleCreateWorkflowRequestWithBizData(),
				Application:           resources.SampleBizFlexiCreditStorageApplicationData()[4],
				CreditBureauEnquiryStreamMessage: &dto.CreditBureauEnquiryStreamMessage{
					BureauEnquiryDate: utils.CurrentDateAndTimeInUTC(),
					Report:            map[string]string{},
				},
				ApplicantsEcosystemDetails: []*storage.ApplicantEcoSystemDetails{},
				ApplicationScoreResponse: &mlScoringAPI.GetApplicationScoreResponse{
					Application: &mlScoringAPI.Application{Applicants: []mlScoringAPI.Applicant{}},
				},
				IncomeDerivationStreamMessage: &dto.IncomeDerivationStreamMessage{
					ReferenceID:   uuid.New().String(),
					ApplicationID: "application-id",
					DocumentType:  incomeDerivationEvent.BankStatement,
					Status:        incomeDerivationEvent.Success,
					StatusReason:  incomeDerivationEvent.ReportVerified,
					UserDetails: &dto.UserDetails{
						Name: "test",
					},
					Metadata: &dto.IncomeDerivationMetadata{
						PerfiosStatementStatus:   "VERIFIED",
						SecondaryStatementStatus: nil,
					},
					BankStatementReport: resources.SamplePerfiosBankStatementReport(),
				},
			}, nil)
			if scenario.expectedErr != nil {
				assert.Nil(t, nextCtx)
				assert.Equal(t, scenario.expectedErr.Error(), err.Error())
			} else {
				assert.Nil(t, err)
				assert.Equal(t, scenario.expectedState, nextCtx.GetState())
			}
		})
	}
}

func Test_completePostBureauCDECheck(t *testing.T) {
	scenarios := []struct {
		desc                             string
		postBureauCreditDecisionResponse api.FICOCreditDecisionResponse
		storeError                       error
		expectedErr                      error
		expectedState                    workflowengine.State
		expectedStatus                   string
		expectedStatusReason             string
		countryCode                      string
		currencyCode                     string
		enableBizBankStatementsUpload    bool
	}{
		{
			desc:                             "SG: Failure: validation failure",
			countryCode:                      "SG",
			currencyCode:                     "SGD",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse3(),
			expectedState:                    stInterventionNeeded,
			expectedStatus:                   string(loanAppAPI.ApplicationStatus_ApplicationStatus_PROCESSING),
			expectedStatusReason:             string(loanAppAPI.ApplicationStatusReason_CDE_PRE_BUREAU_APPROVED),
		},
		{
			desc:                             "SG: Success: fico approved",
			countryCode:                      "SG",
			currencyCode:                     "SGD",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse(),
			expectedState:                    stPostBureauCDEApproved,
			expectedStatus:                   string(loanAppAPI.ApplicationStatus_ApplicationStatus_PENDING_ACCEPTANCE),
			expectedStatusReason:             string(loanAppAPI.ApplicationStatusReason_CDE_POST_BUREAU_APPROVED),
		},
		{
			desc:                             "SG: Success: fico income statement upload and enableBizBankStatementsUpload ON",
			countryCode:                      "SG",
			currencyCode:                     "SGD",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse4(),
			enableBizBankStatementsUpload:    true,
			expectedState:                    stPostBureauIncomeStatementUploadRequired,
			expectedStatus:                   string(loanAppAPI.ApplicationStatus_ApplicationStatus_PROCESSING),
			expectedStatusReason:             string(loanAppAPI.ApplicationStatusReason_INCOME_DOCUMENT_REQUIRED),
		},
		{
			desc:                             "SG: Success: fico income statement upload and enableBizBankStatementsUpload OFF",
			countryCode:                      "SG",
			currencyCode:                     "SGD",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse4(),
			enableBizBankStatementsUpload:    false,
			expectedState:                    stInterventionNeeded,
			expectedStatus:                   string(loanAppAPI.ApplicationStatus_ApplicationStatus_PROCESSING),
			expectedStatusReason:             string(loanAppAPI.ApplicationStatusReason_CDE_PRE_BUREAU_APPROVED),
		},
		{
			desc:                             "SG: Success: fico returns invalid decision",
			countryCode:                      "SG",
			currencyCode:                     "SGD",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse5(),
			enableBizBankStatementsUpload:    false,
			expectedState:                    stInterventionNeeded,
			expectedStatus:                   string(loanAppAPI.ApplicationStatus_ApplicationStatus_PROCESSING),
			expectedStatusReason:             string(loanAppAPI.ApplicationStatusReason_CDE_PRE_BUREAU_APPROVED),
		},
		{
			desc:                             "SG: Success: fico rejected",
			countryCode:                      "SG",
			currencyCode:                     "SGD",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse2(),
			expectedState:                    stPostBureauCDERejected,
			expectedStatus:                   string(loanAppAPI.ApplicationStatus_ApplicationStatus_REJECTED),
			expectedStatusReason:             string(loanAppAPI.ApplicationStatusReason_CDE_POST_BUREAU_REJECTED),
		},
		{
			desc:                             "SG: Failure: db Store ",
			countryCode:                      "SG",
			currencyCode:                     "SGD",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse(),
			expectedErr:                      errDummy,
			storeError:                       errDummy,
		},
		{
			desc:                             "MY: Success: mock for fico approved",
			countryCode:                      "MY",
			currencyCode:                     "MYR",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse(),
			expectedState:                    stPostBureauCDEApproved,
			expectedStatus:                   string(loanAppAPI.ApplicationStatus_ApplicationStatus_PENDING_ACCEPTANCE),
			expectedStatusReason:             string(loanAppAPI.ApplicationStatusReason_CDE_POST_BUREAU_APPROVED),
		},
	}
	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			mockStore := &storage.MockDatabaseStore{}
			constants.EnableBizBankStatementsUpload = scenario.enableBizBankStatementsUpload
			mockStore.On("ExecuteTransaction", mock.Anything, mock.Anything, mock.Anything).Return(scenario.storeError)
			w := WorkflowImpl{
				Store:                        mockStore,
				BizFlexiCreditQueryGenerator: &SGBizFlexiCreditQueryGeneratorImpl{},
				AppConfig: &config.AppConfig{
					LocaleConfig: config.LocaleConfig{
						DefaultCurrencyCode: scenario.countryCode,
						DefaultCountryCode:  scenario.currencyCode,
					},
					FeatureFlags: config.FeatureFlags{
						EnableBizBankStatementsUpload: scenario.enableBizBankStatementsUpload,
					},
				},
			}

			nextCtx, err := w.completePostBureauCDECheck(context.Background(), "transitionID", &ExecutionData{
				PostBureauCreditDecisionResponse: &scenario.postBureauCreditDecisionResponse,
				CreateWorkflowRequest:            requests.SampleCreateWorkflowRequest(),
				Application: &storage.Application{
					ApplicationID: "application-id",
					Status:        string(loanAppAPI.ApplicationStatus_ApplicationStatus_PROCESSING),
					StatusReason:  string(loanAppAPI.ApplicationStatusReason_CDE_PRE_BUREAU_APPROVED),
				},
				State: stPostBureauCDEProcessing,
				CreditBureauEnquiryStreamMessage: &dto.CreditBureauEnquiryStreamMessage{
					Status:        (string)(constant.CbsReportStatusSuccess),
					ApplicationID: "application-id",
				},
			}, nil)

			if scenario.expectedErr != nil {
				assert.Nil(t, nextCtx)
				assert.Equal(t, scenario.expectedErr.Error(), err.Error())
			} else {
				assert.Nil(t, err)
				assert.Equal(t, scenario.expectedState, nextCtx.GetState())
				assert.Equal(t, scenario.expectedStatus, nextCtx.(*ExecutionData).Application.Status)
				assert.Equal(t, scenario.expectedStatusReason, nextCtx.(*ExecutionData).Application.StatusReason)
			}
		})
	}
}

func Test_PostIncomeDerivationCompleteCDECheck(t *testing.T) {
	scenarios := []struct {
		desc                             string
		postBureauCreditDecisionResponse api.FICOCreditDecisionResponse
		storeError                       error
		expectedErr                      error
		expectedState                    workflowengine.State
		expectedStatus                   string
		expectedStatusReason             string
		countryCode                      string
		currencyCode                     string
		enableBizBankStatementsUpload    bool
		enablePrivateLimited             bool
		offerError                       error
	}{
		{
			desc:                             "SG: Failure: validation failure",
			countryCode:                      "SG",
			currencyCode:                     "SGD",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse3(),
			expectedState:                    stInterventionNeeded,
			expectedStatus:                   string(loanAppAPI.ApplicationStatus_ApplicationStatus_PROCESSING),
			expectedStatusReason:             string(loanAppAPI.ApplicationStatusReason_CDE_PRE_BUREAU_APPROVED),
		},
		{
			desc:                             "SG: Success: fico income statement upload and enableBizBankStatementsUpload OFF",
			countryCode:                      "SG",
			currencyCode:                     "SGD",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse4(),
			enableBizBankStatementsUpload:    false,
			expectedState:                    stInterventionNeeded,
			expectedStatus:                   string(loanAppAPI.ApplicationStatus_ApplicationStatus_PROCESSING),
			expectedStatusReason:             string(loanAppAPI.ApplicationStatusReason_POST_INCOME_DERIVATION_CDE_APPROVED),
		},
		{
			desc:                             "SG: Success: fico returns invalid decision",
			countryCode:                      "SG",
			currencyCode:                     "SGD",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse5(),
			enableBizBankStatementsUpload:    false,
			expectedState:                    stInterventionNeeded,
			expectedStatus:                   string(loanAppAPI.ApplicationStatus_ApplicationStatus_PROCESSING),
			expectedStatusReason:             string(loanAppAPI.ApplicationStatusReason_POST_INCOME_DERIVATION_CDE_APPROVED),
		},
		{
			desc:                             "SG: Failure: db Store ",
			countryCode:                      "SG",
			currencyCode:                     "SGD",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse(),
			expectedErr:                      errDummy,
			storeError:                       errDummy,
		},
		{
			desc:                             "MY: Success: mock for fico approved",
			countryCode:                      "MY",
			currencyCode:                     "MYR",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse(),
			expectedState:                    stPostIncomeDerivationCDEApproved,
			expectedStatus:                   string(loanAppAPI.ApplicationStatus_ApplicationStatus_PENDING_ACCEPTANCE),
			expectedStatusReason:             string(loanAppAPI.ApplicationStatusReason_POST_INCOME_DERIVATION_CDE_APPROVED),
		},
		{
			desc:                             "SG: Success: for pte ltd application",
			countryCode:                      "SG",
			currencyCode:                     "SGD",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse(),
			enableBizBankStatementsUpload:    false,
			expectedState:                    stPostIncomeDerivationPGLetterInitiated,
			expectedStatus:                   string(loanAppAPI.ApplicationStatus_ApplicationStatus_PENDING_ACCEPTANCE),
			expectedStatusReason:             string(loanAppAPI.ApplicationStatusReason_POST_INCOME_DERIVATION_CDE_APPROVED),
			enablePrivateLimited:             true,
		},
		{
			desc:                             "SG: Success: mock for fico approved",
			countryCode:                      "SG",
			currencyCode:                     "SGD",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse(),
			expectedState:                    stPostIncomeDerivationCDEApproved,
			expectedStatus:                   string(loanAppAPI.ApplicationStatus_ApplicationStatus_PENDING_ACCEPTANCE),
			expectedStatusReason:             string(loanAppAPI.ApplicationStatusReason_POST_INCOME_DERIVATION_CDE_APPROVED),
		},
		{
			desc:                             "SG: Error: fetch offer error",
			countryCode:                      "SG",
			currencyCode:                     "SGD",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse(),
			expectedErr:                      servus.ServiceError{Code: "1808", Message: "Database error"},
			offerError:                       errDummy,
		},
		{
			desc:                             "SG: Success: fetch offer error not found",
			countryCode:                      "SG",
			currencyCode:                     "SGD",
			postBureauCreditDecisionResponse: resources.SampleBizFlexiCreditPostBureauResponse(),
			offerError:                       data.ErrNoData,
			expectedState:                    stPostIncomeDerivationCDEApproved,
			expectedStatus:                   string(loanAppAPI.ApplicationStatus_ApplicationStatus_PENDING_ACCEPTANCE),
			expectedStatusReason:             string(loanAppAPI.ApplicationStatusReason_POST_INCOME_DERIVATION_CDE_APPROVED),
		},
	}
	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			mockOfferDao := &storage.MockIOfferDAO{}
			mockOfferDao.On("Find", mock.Anything, mock.Anything).
				Return(resources.SampleOfferData("PENDING_ACCEPTANCE", time.Now()), scenario.offerError)
			storage.OfferDao = mockOfferDao
			mockStore := &storage.MockDatabaseStore{}
			constants.EnableBizBankStatementsUpload = scenario.enableBizBankStatementsUpload
			mockStore.On("ExecuteTransaction", mock.Anything, mock.Anything, mock.Anything).Return(scenario.storeError)
			w := WorkflowImpl{
				Store:                        mockStore,
				BizFlexiCreditQueryGenerator: &SGBizFlexiCreditQueryGeneratorImpl{},
				AppConfig: &config.AppConfig{
					LocaleConfig: config.LocaleConfig{
						DefaultCurrencyCode: scenario.countryCode,
						DefaultCountryCode:  scenario.currencyCode,
					},
					FeatureFlags: config.FeatureFlags{
						EnableBizBankStatementsUpload: scenario.enableBizBankStatementsUpload,
						EnablePrivateLimited:          scenario.enablePrivateLimited,
					},
				},
			}
			constants.DefaultCountryCode = scenario.countryCode
			constants.EnablePrivateLimited = scenario.enablePrivateLimited
			createWorkFlowRequest := requests.SampleCreateWorkflowRequest()
			if scenario.enablePrivateLimited {
				createWorkFlowRequest = requests.SampleCreateWorkflowRequestForPteLtd()
			}
			nextCtx, err := w.completePostIncomeDerivationCDECheck(context.Background(), "transitionID", &ExecutionData{
				PostIncomeCreditDecisionResponse: &scenario.postBureauCreditDecisionResponse,
				CreateWorkflowRequest:            createWorkFlowRequest,
				Application: &storage.Application{
					ApplicationID: "application-id",
					Status:        string(loanAppAPI.ApplicationStatus_ApplicationStatus_PROCESSING),
					StatusReason:  string(loanAppAPI.ApplicationStatusReason_CDE_PRE_BUREAU_APPROVED),
				},
				State: stPostIncomeDerivationBureauCDEProcessing,
				CreditBureauEnquiryStreamMessage: &dto.CreditBureauEnquiryStreamMessage{
					Status:        (string)(constant.CbsReportStatusSuccess),
					ApplicationID: "application-id",
				},
			}, nil)

			if scenario.expectedErr != nil {
				assert.Nil(t, nextCtx)
				assert.Equal(t, scenario.expectedErr.Error(), err.Error())
			} else {
				assert.Nil(t, err)
				assert.Equal(t, scenario.expectedState, nextCtx.GetState())
				assert.Equal(t, scenario.expectedStatus, nextCtx.(*ExecutionData).Application.Status)
				assert.Equal(t, scenario.expectedStatusReason, nextCtx.(*ExecutionData).Application.StatusReason)
			}
		})
	}
}
