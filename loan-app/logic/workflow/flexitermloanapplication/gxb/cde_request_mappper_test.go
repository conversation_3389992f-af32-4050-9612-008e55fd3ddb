package gxb

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	creditDecisionEngineAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	incomeDerivationEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/income_derivation_event"
)

func TestGetCDERequest(t *testing.T) {
	appCreationTime := time.Now()
	bureauData := "{\"CTOSDerivations\":{\"ccrisCCUtilization\":0.04,\"ccrisCommitment\":1300,\"ccrisCountAppliedAppsOffUsL6M\":0,\"ccrisCountCCAndSupp\":1,\"ccrisCountCCPLEnquiryL6M\":0,\"ccrisCountGE1MIAL6M\":-99990,\"ccrisCountHL\":0,\"ccrisCountNotPL\":2,\"ccrisCountPL\":0,\"ccrisCountUnsecuredApproved\":0,\"ccrisCountUnsecuredPending\":1,\"ccrisEmployerName\":\"LEMBAGA PELABUHAN SABAH\",\"ccrisEntityWarning\":\"2\",\"ccrisLegalStatus\":\"\",\"ccrisMaxVintage\":34,\"ccrisMinVintage\":22,\"ccrisMonthsSinceLastGE2MIA\":-99990,\"ccrisNegativeFlag\":\"\",\"ccrisNumberOfCC\":1,\"ccrisOccupation\":\"SECURITY GUARD\",\"ccrisOldestMOB\":34,\"ccrisSpecialAttentionAccountFlag\":false,\"ccrisTotalLimitCC\":50000,\"ccrisTotalOsBalCC\":2000,\"ccrisUnsecToSecBalRatio\":0.03,\"ccrisUnsecuredExposure\":50000,\"ccrisWorstConductL1M\":0,\"ccrisWorstConductL6M\":0,\"ccrisWorstDelqL12M\":0,\"ctosAngkasaNonFIDebtCommitment\":0,\"ctosBankruptcyFlag\":false,\"ctosCompanyDetails\":[{\"companyName\":\"HUGO LEE SDN BHD\",\"totalShareHoldingPercentage\":100,\"transformedCompanyName\":\"\"},{\"companyName\":\"CHAMPION LEAD ENTERPRISE\",\"totalShareHoldingPercentage\":100,\"transformedCompanyName\":\"\"}],\"ctosIEMIncome\":6501,\"ctosLegalFlag\":false,\"ctosReportStatus\":\"SINGLE_ENTITY_FOUND\",\"transformedBureauEmployerName\":\"\"},\"CTOSEnquiryDate\":\"2024-11-21T11:11:09Z\",\"modelDetails\":[{\"modelId\":\"CTOS\",\"modelName\":\"CTOS\",\"score\":772}]}"
	scenarios := []struct {
		desc         string
		transitionID string
		currCtx      *ExecutionData
		options      []func(request *creditDecisionEngineAPI.FICOCreditDecisionRequest) error
		expectedReq  *creditDecisionEngineAPI.FICOCreditDecisionRequest
	}{
		{
			desc:         "CDE request on post bureau ",
			transitionID: "transition-123",
			currCtx: &ExecutionData{
				ApplicationID: "dummyApplicationID",
				FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{
					Message: &api.Message{
						Application: &api.FlexiTermLoanApplication{
							Channel:         api.Channel_GXS,
							CountryCode:     "MY",
							AppCreationDate: appCreationTime,
							Applicants: []api.FlexiTermLoanApplicant{
								{
									SafeID:            "dummySafeID",
									ApplicantID:       "dummyApplicantID",
									FundsPurposeOfUse: "others",
									ApplicantType:     api.ApplicantType_PRIMARY,
									PrimaryID: &api.IDDetails{
										Type: api.IDType_MYKAD,
									},
									FullName:      "John Doe",
									ContactType:   "MOBILE",
									ContactNumber: "0123456789",
									DateOfBirth:   "1980-01-01",
									Gender:        api.Gender_MALE,
									Nationality:   "MY",
									AddressDetails: []api.AddressDetails{
										{
											AddressType: "REGISTERED",
											City:        "Kuala Lumpur",
											Province:    "WP",
											PostalCode:  "50000",
										},
										{
											AddressType: "MAILING",
											City:        "Kuala Lumpur1",
											Province:    "WP1",
											PostalCode:  "50001",
										},
									},
									EmploymentDetail: &api.EmploymentDetail{
										EmploymentType:   api.EmploymentType_SE,
										Position:         "dummyPosition",
										OccupationCode:   "dummyOccupationCode",
										IndustryCode:     "dummyIndustryCode",
										IndustrySector:   "dummyIndustrySector",
										EmployerName:     "dummyEmployerName",
										NatureOfBusiness: "dummyNatureOfBusiness",
										IncomePerMonth:   "5000",
									},
									Education: 1,
									AdditionalData: []api.AdditionalData{
										{
											Key:   nonFiCommitmentKey,
											Value: "1,000.00",
										},
										{
											Key:   existingAssetsKey,
											Value: "[{\"productCode\":\"CASA\",\"subProductCode\":\"DEPOSITS_ACCOUNT\",\"accountNumber\":\"*************\",\"acctStatus\":\"ACTIVE\",\"openDate\":\"2024-11-21T08:20:47.364322Z\",\"closeDate\":\"0001-01-01T00:00:00Z\"}]",
										},
									},
								},
							},
							Products: []api.Product{
								{
									ProductType:           api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
									SubProductType:        api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
									RequestedLoanAmount:   10000,
									RequestedCurrencyCode: "MYR",
								},
							},
						},
					},
				},
				EnquiryStreamMsg: &creditBureauEnquiryStreamMessage{
					Report: map[string]string{
						"RSP_REPORT": bureauData,
					},
				},
				EcosystemData: &dto.EcosystemDataResponse{
					RemScore:      510,
					ApplicantType: dto.ApplicantTypeEnum_ETP,
					ExportedDate:  time.Now().String(),
				},
			},
			options: []func(request *creditDecisionEngineAPI.FICOCreditDecisionRequest) error{
				func(request *creditDecisionEngineAPI.FICOCreditDecisionRequest) error {
					request.Message.ReferenceID = utils.GetPointer("dummyValue")
					request.Message.CallType = utils.GetPointer(creditDecisionEngineAPI.CallType_POST_BUREAU)
					return nil
				},
			},
			expectedReq: &creditDecisionEngineAPI.FICOCreditDecisionRequest{
				Message: &creditDecisionEngineAPI.Message{
					CallType: utils.GetPointer(creditDecisionEngineAPI.CallType_POST_BUREAU),
					Application: &creditDecisionEngineAPI.Application{
						Channel:         utils.GetPointer(creditDecisionEngineAPI.Channel_GXS),
						CountryCode:     utils.GetPointer("MY"),
						AppCreationDate: utils.GetPointer(appCreationTime),
						ApplicationType: utils.GetPointer(creditDecisionEngineAPI.ApplicationType_NEW),
						ApplicationID:   utils.GetPointer("dummyApplicationID"),
						Products: []creditDecisionEngineAPI.Product{
							{
								ProductType:           utils.GetPointer(creditDecisionEngineAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
								SubProductType:        utils.GetPointer("DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"),
								RequestedLoanAmount:   utils.GetPointer(10000.0),
								RequestedCurrencyCode: utils.GetPointer("MYR"),
								IsPrimaryProduct:      utils.GetPointer(true),
							},
						},
						Applicants: []creditDecisionEngineAPI.Applicant{
							{
								Bureau: utils.ExtractFieldsFromJSONString(bureauData),
								ModelDetails: []creditDecisionEngineAPI.ModelDetail{
									{
										ModelId:   utils.GetPointer("CTOS"),
										ModelName: utils.GetPointer("CTOS"),
										Score:     utils.GetPointer(772.0),
									},
									{
										ModelId:   utils.GetPointer(grabPaylaterETP),
										ModelName: utils.GetPointer(grabPaylaterETP),
										Score:     utils.GetPointer(510.0),
									},
								},
								SafeID:        utils.GetPointer("dummySafeID"),
								ApplicantID:   utils.GetPointer("dummyApplicantID"),
								ApplicantType: utils.GetPointer(creditDecisionEngineAPI.ApplicantType_PRIMARY),
								PrimaryID: &creditDecisionEngineAPI.IDDetails{
									Type: utils.GetPointer(creditDecisionEngineAPI.IDType_MYKAD),
								},
								FullName:      utils.GetPointer("John Doe"),
								ContactType:   utils.GetPointer("MOBILE"),
								ContactNumber: utils.GetPointer("0123456789"),
								DateOfBirth:   utils.GetPointer("1980-01-01"),
								Gender:        utils.GetPointer(creditDecisionEngineAPI.Gender_MALE),
								IDType:        utils.GetPointer("MYKAD"),
								Nationality:   utils.GetPointer("MY"),
								AddressDetails: []creditDecisionEngineAPI.AddressDetails{
									{
										AddressType: utils.GetPointer(creditDecisionEngineAPI.AddressType_REGISTERED),
										City:        utils.GetPointer("Kuala Lumpur"),
										Province:    utils.GetPointer("WP"),
										PostalCode:  utils.GetPointer("50000"),
									},
									{
										AddressType: utils.GetPointer(creditDecisionEngineAPI.AddressType_MAILING),
										City:        utils.GetPointer("Kuala Lumpur1"),
										Province:    utils.GetPointer("WP1"),
										PostalCode:  utils.GetPointer("50001"),
									},
								},
								FundsPurposeOfUse: utils.GetPointer("others"),
								EmploymentDetail: &creditDecisionEngineAPI.EmploymentDetail{
									EmploymentType:   utils.GetPointer(creditDecisionEngineAPI.EmploymentType_SA),
									OccupationCode:   utils.GetPointer("dummyOccupationCode"),
									IndustryCode:     utils.GetPointer("dummyIndustryCode"),
									IndustrySector:   utils.GetPointer("dummyIndustrySector"),
									EmployerName:     utils.GetPointer("dummyEmployerName"),
									NatureOfBusiness: utils.GetPointer("dummyNatureOfBusiness"),
									IncomePerMonth:   utils.GetPointer("5000"),
								},
								Education: utils.GetPointer(int64(1)),
								ExistingAssets: []creditDecisionEngineAPI.ExistingAsset{{
									ProductCode:    utils.GetPointer("CASA"),
									SubProductCode: utils.GetPointer("DEPOSITS_ACCOUNT"),
									AccountNumber:  utils.GetPointer("*************"),
									AcctStatus:     utils.GetPointer("ACTIVE"),
									OpenDate:       utils.GetPointer("2024-11-21T08:20:47.364322Z"),
								}},
								Income:                      &creditDecisionEngineAPI.Income{},
								IsPR:                        utils.GetPointer(true),
								ExistingCustomer:            utils.GetPointer(true),
								IsExistingCustomer:          utils.GetPointer(true),
								DeclaredNonFIDebtCommitment: utils.GetPointer(1000.0),
								IsGrabCustomer:              utils.GetPointer(true),
								IsGrabPayLaterCustomer:      utils.GetPointer(true),
							},
						},
					},
					ReferenceID: utils.GetPointer("dummyValue"),
				},
			},
		},
		{
			desc:         "CDE request on post income derivation ",
			transitionID: "transition-123",
			currCtx: &ExecutionData{
				ApplicationID: "dummyApplicationID",
				FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{
					Message: &api.Message{
						Application: &api.FlexiTermLoanApplication{
							Channel:         api.Channel_GXS,
							CountryCode:     "MY",
							AppCreationDate: appCreationTime,
							Applicants: []api.FlexiTermLoanApplicant{
								{
									SafeID:            "dummySafeID",
									ApplicantID:       "dummyApplicantID",
									FundsPurposeOfUse: "others",
									ApplicantType:     api.ApplicantType_PRIMARY,
									PrimaryID: &api.IDDetails{
										Type: api.IDType_MYKAD,
									},
									FullName:      "John Doe",
									ContactType:   "MOBILE",
									ContactNumber: "0123456789",
									DateOfBirth:   "1980-01-01",
									Gender:        api.Gender_MALE,
									Nationality:   "MY",
									AddressDetails: []api.AddressDetails{
										{
											AddressType: "REGISTERED",
											City:        "Kuala Lumpur",
											Province:    "WP",
											PostalCode:  "50000",
										},
										{
											AddressType: "MAILING",
											City:        "Kuala Lumpur1",
											Province:    "WP1",
											PostalCode:  "50001",
										},
									},
									EmploymentDetail: &api.EmploymentDetail{
										EmploymentType:   api.EmploymentType_SE,
										Position:         "dummyPosition",
										OccupationCode:   "dummyOccupationCode",
										IndustryCode:     "dummyIndustryCode",
										IndustrySector:   "dummyIndustrySector",
										EmployerName:     "dummyEmployerName",
										NatureOfBusiness: "dummyNatureOfBusiness",
										IncomePerMonth:   "5000",
									},
									Education: 1,
									AdditionalData: []api.AdditionalData{
										{
											Key:   nonFiCommitmentKey,
											Value: "1000",
										},
										{
											Key:   existingAssetsKey,
											Value: "[{\"productCode\":\"CASA\",\"subProductCode\":\"DEPOSITS_ACCOUNT\",\"accountNumber\":\"*************\",\"acctStatus\":\"ACTIVE\",\"openDate\":\"2024-11-21T08:20:47.364322Z\",\"closeDate\":\"0001-01-01T00:00:00Z\"}]",
										},
									},
								},
							},
							Products: []api.Product{
								{
									ProductType:           api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
									SubProductType:        api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
									RequestedLoanAmount:   10000,
									RequestedCurrencyCode: "MYR",
								},
							},
						},
					},
				},
				EnquiryStreamMsg: &creditBureauEnquiryStreamMessage{
					Report: map[string]string{
						"RSP_REPORT": bureauData,
					},
				},
				IncomeDerivationEvent: &dto.IncomeDerivationStreamMessage{
					DocumentType: incomeDerivationEvent.EpfStatement,
					UserDetails: &dto.UserDetails{
						Name: "John Doe",
					},
					Metadata: &dto.IncomeDerivationMetadata{
						IncomeAlert:                           false,
						SixMonthsEPF:                          false,
						PerfiosStatementStatus:                "VERIFIED",
						EmployerNameMatch:                     utils.GetPointer(true),
						PositiveKeywordMatch:                  utils.GetPointer(true),
						NegativeKeywordMatch:                  utils.GetPointer(false),
						DepositDescriptionConsistency:         utils.GetPointer(true),
						CreditAmountConsistency:               utils.GetPointer(true),
						CreditDateConsistency:                 utils.GetPointer(false),
						StatementAccountHolderNameConsistency: utils.GetPointer(true),
						StatementAccountNoConsistency:         utils.GetPointer(false),
					},
					IncomeAmount:         1000,
					IncomeDocumentSource: "PERFIOS",
				},
				EcosystemData: &dto.EcosystemDataResponse{
					RemScore:      630,
					ApplicantType: dto.ApplicantTypeEnum_NTP,
					ExportedDate:  time.Now().String(),
				},
			},
			options: []func(request *creditDecisionEngineAPI.FICOCreditDecisionRequest) error{
				func(request *creditDecisionEngineAPI.FICOCreditDecisionRequest) error {
					request.Message.ReferenceID = utils.GetPointer("dummyValue1")
					request.Message.CallType = utils.GetPointer(creditDecisionEngineAPI.CallType_POST_INCOME_DERIVATION)
					return nil
				},
			},
			expectedReq: &creditDecisionEngineAPI.FICOCreditDecisionRequest{
				Message: &creditDecisionEngineAPI.Message{
					CallType: utils.GetPointer(creditDecisionEngineAPI.CallType_POST_INCOME_DERIVATION),
					Application: &creditDecisionEngineAPI.Application{
						Channel:         utils.GetPointer(creditDecisionEngineAPI.Channel_GXS),
						CountryCode:     utils.GetPointer("MY"),
						AppCreationDate: utils.GetPointer(appCreationTime),
						ApplicationType: utils.GetPointer(creditDecisionEngineAPI.ApplicationType_NEW),
						ApplicationID:   utils.GetPointer("dummyApplicationID"),
						Products: []creditDecisionEngineAPI.Product{
							{
								ProductType:           utils.GetPointer(creditDecisionEngineAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
								SubProductType:        utils.GetPointer("DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"),
								RequestedLoanAmount:   utils.GetPointer(10000.0),
								RequestedCurrencyCode: utils.GetPointer("MYR"),
								IsPrimaryProduct:      utils.GetPointer(true),
							},
						},
						Applicants: []creditDecisionEngineAPI.Applicant{
							{
								Bureau: utils.ExtractFieldsFromJSONString(bureauData),
								ModelDetails: []creditDecisionEngineAPI.ModelDetail{
									{
										ModelId:   utils.GetPointer("CTOS"),
										ModelName: utils.GetPointer("CTOS"),
										Score:     utils.GetPointer(772.0),
									},
									{
										ModelId:   utils.GetPointer(grabPaylaterNTP),
										ModelName: utils.GetPointer(grabPaylaterNTP),
										Score:     utils.GetPointer(630.0),
									},
								},
								SafeID:        utils.GetPointer("dummySafeID"),
								ApplicantID:   utils.GetPointer("dummyApplicantID"),
								ApplicantType: utils.GetPointer(creditDecisionEngineAPI.ApplicantType_PRIMARY),
								PrimaryID: &creditDecisionEngineAPI.IDDetails{
									Type: utils.GetPointer(creditDecisionEngineAPI.IDType_MYKAD),
								},
								FullName:      utils.GetPointer("John Doe"),
								ContactType:   utils.GetPointer("MOBILE"),
								ContactNumber: utils.GetPointer("0123456789"),
								DateOfBirth:   utils.GetPointer("1980-01-01"),
								Gender:        utils.GetPointer(creditDecisionEngineAPI.Gender_MALE),
								IDType:        utils.GetPointer("MYKAD"),
								Nationality:   utils.GetPointer("MY"),
								AddressDetails: []creditDecisionEngineAPI.AddressDetails{
									{
										AddressType: utils.GetPointer(creditDecisionEngineAPI.AddressType_REGISTERED),
										City:        utils.GetPointer("Kuala Lumpur"),
										Province:    utils.GetPointer("WP"),
										PostalCode:  utils.GetPointer("50000"),
									},
									{
										AddressType: utils.GetPointer(creditDecisionEngineAPI.AddressType_MAILING),
										City:        utils.GetPointer("Kuala Lumpur1"),
										Province:    utils.GetPointer("WP1"),
										PostalCode:  utils.GetPointer("50001"),
									},
								},
								FundsPurposeOfUse: utils.GetPointer("others"),
								EmploymentDetail: &creditDecisionEngineAPI.EmploymentDetail{
									EmploymentType:   utils.GetPointer(creditDecisionEngineAPI.EmploymentType_SA),
									OccupationCode:   utils.GetPointer("dummyOccupationCode"),
									IndustryCode:     utils.GetPointer("dummyIndustryCode"),
									IndustrySector:   utils.GetPointer("dummyIndustrySector"),
									EmployerName:     utils.GetPointer("dummyEmployerName"),
									NatureOfBusiness: utils.GetPointer("dummyNatureOfBusiness"),
									IncomePerMonth:   utils.GetPointer("5000"),
								},
								Education: utils.GetPointer(int64(1)),
								ExistingAssets: []creditDecisionEngineAPI.ExistingAsset{{
									ProductCode:    utils.GetPointer("CASA"),
									SubProductCode: utils.GetPointer("DEPOSITS_ACCOUNT"),
									AccountNumber:  utils.GetPointer("*************"),
									AcctStatus:     utils.GetPointer("ACTIVE"),
									OpenDate:       utils.GetPointer("2024-11-21T08:20:47.364322Z"),
								}},
								Income: &creditDecisionEngineAPI.Income{
									IncomeDocuments: []creditDecisionEngineAPI.IncomeDocument{
										{
											IncomeDocumentType:   utils.GetPointer(creditDecisionEngineAPI.IncomeDocumentType_EPF_STATEMENT),
											IncomeDocumentSource: utils.GetPointer("PERFIOS"),
											IncomeDocumentName:   utils.GetPointer("John Doe"),
											IncomeAmount:         utils.GetPointer(1000.0),
										},
									},
									Perfios: &creditDecisionEngineAPI.Perfios{
										PerfiosIncomeAlert:                    utils.GetPointer(false),
										Perfios6MonthsEPF:                     utils.GetPointer(false),
										PerfiosStatementStatus:                utils.GetPointer("VERIFIED"),
										EmployerNameMatch:                     utils.GetPointer(true),
										PositiveKeywordMatch:                  utils.GetPointer(true),
										NegativeKeywordMatch:                  utils.GetPointer(false),
										DepositDescriptionConsistency:         utils.GetPointer(true),
										CreditAmountConsistency:               utils.GetPointer(true),
										CreditDateConsistency:                 utils.GetPointer(false),
										StatementAccountHolderNameConsistency: utils.GetPointer(true),
										StatementAccountNoConsistency:         utils.GetPointer(false),
									},
								},
								IsPR:                        utils.GetPointer(true),
								ExistingCustomer:            utils.GetPointer(true),
								IsExistingCustomer:          utils.GetPointer(true),
								DeclaredNonFIDebtCommitment: utils.GetPointer(1000.0),
								IsGrabCustomer:              utils.GetPointer(true),
								IsGrabPayLaterCustomer:      utils.GetPointer(false),
							},
						},
					},
					ReferenceID: utils.GetPointer("dummyValue1"),
				},
			},
		},
		{
			desc:         "CDE final CDE call ",
			transitionID: "transition-123",
			currCtx: &ExecutionData{
				ApplicationID: "dummyApplicationID",
				FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{
					Message: &api.Message{
						Application: &api.FlexiTermLoanApplication{
							Channel:         api.Channel_GXS,
							CountryCode:     "MY",
							AppCreationDate: appCreationTime,
							Applicants: []api.FlexiTermLoanApplicant{
								{
									SafeID:            "dummySafeID",
									ApplicantID:       "dummyApplicantID",
									FundsPurposeOfUse: "others",
									ApplicantType:     api.ApplicantType_PRIMARY,
									PrimaryID: &api.IDDetails{
										Type: api.IDType_MYKAD,
									},
									FullName:      "John Doe",
									ContactType:   "MOBILE",
									ContactNumber: "0123456789",
									DateOfBirth:   "1980-01-01",
									Gender:        api.Gender_MALE,
									Nationality:   "MY",
									AddressDetails: []api.AddressDetails{
										{
											AddressType: "REGISTERED",
											City:        "Kuala Lumpur",
											Province:    "WP",
											PostalCode:  "50000",
										},
										{
											AddressType: "MAILING",
											City:        "Kuala Lumpur1",
											Province:    "WP1",
											PostalCode:  "50001",
										},
									},
									EmploymentDetail: &api.EmploymentDetail{
										EmploymentType:   api.EmploymentType_SE,
										Position:         "dummyPosition",
										OccupationCode:   "dummyOccupationCode",
										IndustryCode:     "dummyIndustryCode",
										IndustrySector:   "dummyIndustrySector",
										EmployerName:     "dummyEmployerName",
										NatureOfBusiness: "dummyNatureOfBusiness",
										IncomePerMonth:   "5000",
									},
									Education: 1,
									AdditionalData: []api.AdditionalData{
										{
											Key:   nonFiCommitmentKey,
											Value: "1000",
										},
										{
											Key:   existingAssetsKey,
											Value: "[{\"productCode\":\"CASA\",\"subProductCode\":\"DEPOSITS_ACCOUNT\",\"accountNumber\":\"*************\",\"acctStatus\":\"ACTIVE\",\"openDate\":\"2024-11-21T08:20:47.364322Z\",\"closeDate\":\"0001-01-01T00:00:00Z\"}]",
										},
									},
								},
							},
							Products: []api.Product{
								{
									ProductType:           api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
									SubProductType:        api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
									RequestedLoanAmount:   10000,
									RequestedCurrencyCode: "MYR",
								},
							},
						},
					},
				},
				EnquiryStreamMsg: &creditBureauEnquiryStreamMessage{
					Report: map[string]string{
						"RSP_REPORT": bureauData,
					},
				},
				IncomeDerivationEvent: &dto.IncomeDerivationStreamMessage{
					DocumentType: incomeDerivationEvent.EpfStatement,
					UserDetails: &dto.UserDetails{
						Name: "John Doe",
					},
					Metadata: &dto.IncomeDerivationMetadata{
						IncomeAlert:            false,
						SixMonthsEPF:           false,
						PerfiosStatementStatus: "VERIFIED",
					},
					IncomeAmount:         1000,
					IncomeDocumentSource: "PERFIOS",
				},
				UserAcceptanceData: &userAcceptanceData{
					AcceptedCredit: &api.Money{
						CurrencyCode: "MYR",
						Val:          1000,
					},
					Status:        api.LoanOfferStatus_ACCEPTED,
					DeclineReason: nil,
				},
			},
			options: []func(request *creditDecisionEngineAPI.FICOCreditDecisionRequest) error{
				func(request *creditDecisionEngineAPI.FICOCreditDecisionRequest) error {
					request.Message.ReferenceID = utils.GetPointer("dummyValue2")
					request.Message.CallType = utils.GetPointer(creditDecisionEngineAPI.CallType_FINAL_DECISION)
					return nil
				},
			},
			expectedReq: &creditDecisionEngineAPI.FICOCreditDecisionRequest{
				Message: &creditDecisionEngineAPI.Message{
					CallType: utils.GetPointer(creditDecisionEngineAPI.CallType_FINAL_DECISION),
					Application: &creditDecisionEngineAPI.Application{
						Channel:         utils.GetPointer(creditDecisionEngineAPI.Channel_GXS),
						CountryCode:     utils.GetPointer("MY"),
						AppCreationDate: utils.GetPointer(appCreationTime),
						ApplicationType: utils.GetPointer(creditDecisionEngineAPI.ApplicationType_NEW),
						ApplicationID:   utils.GetPointer("dummyApplicationID"),
						Products: []creditDecisionEngineAPI.Product{
							{
								ProductType:           utils.GetPointer(creditDecisionEngineAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
								SubProductType:        utils.GetPointer("DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"),
								RequestedLoanAmount:   utils.GetPointer(10000.0),
								RequestedCurrencyCode: utils.GetPointer("MYR"),
								IsPrimaryProduct:      utils.GetPointer(true),
								ProductDecision: &creditDecisionEngineAPI.ProductDecision{
									FinalDecision: &creditDecisionEngineAPI.FinalDecision{
										FinalDecision:      utils.GetPointer("APPROVED"),
										FinalAcceptedLimit: utils.GetPointer(10.0),
									},
								},
							},
						},
						Applicants: []creditDecisionEngineAPI.Applicant{
							{
								Bureau: utils.ExtractFieldsFromJSONString(bureauData),
								ModelDetails: []creditDecisionEngineAPI.ModelDetail{
									{
										ModelId:   utils.GetPointer("CTOS"),
										ModelName: utils.GetPointer("CTOS"),
										Score:     utils.GetPointer(772.0),
									},
								},
								SafeID:        utils.GetPointer("dummySafeID"),
								ApplicantID:   utils.GetPointer("dummyApplicantID"),
								ApplicantType: utils.GetPointer(creditDecisionEngineAPI.ApplicantType_PRIMARY),
								PrimaryID: &creditDecisionEngineAPI.IDDetails{
									Type: utils.GetPointer(creditDecisionEngineAPI.IDType_MYKAD),
								},
								FullName:      utils.GetPointer("John Doe"),
								ContactType:   utils.GetPointer("MOBILE"),
								ContactNumber: utils.GetPointer("0123456789"),
								DateOfBirth:   utils.GetPointer("1980-01-01"),
								Gender:        utils.GetPointer(creditDecisionEngineAPI.Gender_MALE),
								IDType:        utils.GetPointer("MYKAD"),
								Nationality:   utils.GetPointer("MY"),
								AddressDetails: []creditDecisionEngineAPI.AddressDetails{
									{
										AddressType: utils.GetPointer(creditDecisionEngineAPI.AddressType_REGISTERED),
										City:        utils.GetPointer("Kuala Lumpur"),
										Province:    utils.GetPointer("WP"),
										PostalCode:  utils.GetPointer("50000"),
									},
									{
										AddressType: utils.GetPointer(creditDecisionEngineAPI.AddressType_MAILING),
										City:        utils.GetPointer("Kuala Lumpur1"),
										Province:    utils.GetPointer("WP1"),
										PostalCode:  utils.GetPointer("50001"),
									},
								},
								FundsPurposeOfUse: utils.GetPointer("others"),
								EmploymentDetail: &creditDecisionEngineAPI.EmploymentDetail{
									EmploymentType:   utils.GetPointer(creditDecisionEngineAPI.EmploymentType_SA),
									OccupationCode:   utils.GetPointer("dummyOccupationCode"),
									IndustryCode:     utils.GetPointer("dummyIndustryCode"),
									IndustrySector:   utils.GetPointer("dummyIndustrySector"),
									EmployerName:     utils.GetPointer("dummyEmployerName"),
									NatureOfBusiness: utils.GetPointer("dummyNatureOfBusiness"),
									IncomePerMonth:   utils.GetPointer("5000"),
								},
								Education: utils.GetPointer(int64(1)),
								ExistingAssets: []creditDecisionEngineAPI.ExistingAsset{{
									ProductCode:    utils.GetPointer("CASA"),
									SubProductCode: utils.GetPointer("DEPOSITS_ACCOUNT"),
									AccountNumber:  utils.GetPointer("*************"),
									AcctStatus:     utils.GetPointer("ACTIVE"),
									OpenDate:       utils.GetPointer("2024-11-21T08:20:47.364322Z"),
								}},
								Income: &creditDecisionEngineAPI.Income{
									IncomeDocuments: []creditDecisionEngineAPI.IncomeDocument{
										{
											IncomeDocumentType:   utils.GetPointer(creditDecisionEngineAPI.IncomeDocumentType_EPF_STATEMENT),
											IncomeDocumentSource: utils.GetPointer("PERFIOS"),
											IncomeDocumentName:   utils.GetPointer("John Doe"),
											IncomeAmount:         utils.GetPointer(1000.0),
										},
									},
									Perfios: &creditDecisionEngineAPI.Perfios{
										PerfiosIncomeAlert:     utils.GetPointer(false),
										Perfios6MonthsEPF:      utils.GetPointer(false),
										PerfiosStatementStatus: utils.GetPointer("VERIFIED"),
									},
								},
								IsPR:                        utils.GetPointer(true),
								ExistingCustomer:            utils.GetPointer(true),
								IsExistingCustomer:          utils.GetPointer(true),
								DeclaredNonFIDebtCommitment: utils.GetPointer(1000.0),
								IsGrabCustomer:              utils.GetPointer(false),
								IsGrabPayLaterCustomer:      utils.GetPointer(false),
							},
						},
					},
					ReferenceID: utils.GetPointer("dummyValue2"),
				},
			},
		},
	}
	for _, scenario := range scenarios {
		tc := scenario
		constants.EnablePersonalBankStatementsUpload = true
		t.Run(tc.desc, func(t *testing.T) {
			cdeReq, _ := tc.currCtx.getCDERequest(tc.options...)
			assert.Equal(t, tc.expectedReq, cdeReq, tc.desc)
		})
	}
}
