package gxb

import (
	"context"

	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	ftl "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication"
	incomeDerivationEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/income_derivation_event"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
)

func (w *WorkflowImpl) persistIncomeDerivation(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logTag, "Invalid context passed in persistIncomeDerivation state")
		return nil, errInvalidContext
	}
	nextCtx := currCtx.Clone()

	streamMessage, ok := params.(*dto.IncomeDerivationStreamMessage)
	if !ok {
		slog.FromContext(ctx).Warn(logTag, "Wrong params passed in persistIncomeDerivation")
		return nil, common.ErrInvalidParams
	}
	nextCtx.IncomeDerivationEvent = streamMessage
	nextCtx.SetState(ftl.StIncomeDetailsReceived)
	return nextCtx, nil
}

func (w *WorkflowImpl) resumeIncomeDerivation(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logTag, "Invalid context passed in persistIncomeDerivation state")
		return nil, errInvalidContext
	}
	nextCtx := currCtx.Clone()

	err := w.updateTrackerInfo(ctx, transitionID, currCtx, nextCtx)
	if err != nil {
		return nil, err
	}

	switch currCtx.IncomeDerivationEvent.StatusReason {
	case incomeDerivationEvent.ReportVerified, incomeDerivationEvent.ReferStatement, incomeDerivationEvent.FraudStatement:
		nextCtx.SetState(ftl.StIncomeDetailsDerived)
	default:
		nextCtx.SetState(ftl.StApplicationFailed)
		updateNextStatusAndStatusReason(ctx, currCtx, nextCtx)
	}
	return nextCtx, nil
}
