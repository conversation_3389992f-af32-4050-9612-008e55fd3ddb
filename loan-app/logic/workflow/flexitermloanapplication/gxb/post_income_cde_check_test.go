package gxb

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	creditDecisionEngineAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	creditDecisionEngineMockAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api/mock"
	loanAppAPI "gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	ftl "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/base"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	incomeDerivationEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/income_derivation_event"

	"gitlab.myteksi.net/dakota/klient/errorhandling"
	"gitlab.myteksi.net/dakota/workflowengine"
)

func Test_PostIncomeCDECall(t *testing.T) {
	tests := []struct {
		name         string
		execData     workflowengine.ExecutionData
		mockFunc     func(m *creditDecisionEngineMockAPI.CrDecisionEng)
		expectedResp *creditDecisionEngineAPI.FICOCreditDecisionResponse
		expectErr    bool
		expectedErr  error
		state        workflowengine.State
	}{
		{
			name:     "happy-path",
			execData: sampleExecDataPostIncomeCDELoanApplicationRequest(),
			mockFunc: func(m *creditDecisionEngineMockAPI.CrDecisionEng) {
				m.On("FICOCreditDecision", mock.Anything, mock.Anything).Return(samplePostIncomeCreditDecisionResponse(), nil).Once()
			},
			state:       ftl.StApplicationRejected,
			expectErr:   false,
			expectedErr: nil,
		},
		{
			name:     "sad-path-error-cde-failed",
			execData: sampleExecDataPostIncomeCDELoanApplicationRequest(),
			mockFunc: func(m *creditDecisionEngineMockAPI.CrDecisionEng) {
				err := &errorhandling.Error{
					HTTPCode: 400,
					Message:  constants.CreditDecisionEngineFailedError,
				}
				m.On("FICOCreditDecision", mock.Anything, mock.Anything).Return(nil, err)
			},
			state:        ftl.StApplicationFailed,
			expectedResp: nil,
			expectErr:    false,
			expectedErr:  nil,
		},
		{
			name:        "sad-path-invalid-context",
			execData:    nil,
			expectErr:   true,
			expectedErr: errInvalidContext,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			ctx := context.Background()
			mockCDE := &creditDecisionEngineMockAPI.CrDecisionEng{}
			constants.EnablePersonalBankStatementsUpload = true
			workflow := WorkflowImpl{
				CreditDecisionEngine: mockCDE,
				WorkflowImpl: base.WorkflowImpl{
					AppConfig: &config.AppConfig{
						FeatureFlags: config.FeatureFlags{
							EnableOfflineEcosystemDataFetcher: true,
						},
					},
				},
			}

			// Apply the mock behaviour of CreditDecisionEngine if any, defined in each test case
			if test.mockFunc != nil {
				test.mockFunc(mockCDE)
			}

			resp, err := workflow.postIncomeCDECheck(ctx, "TestId", test.execData, nil)
			if !test.expectErr {
				execData := resp.(*ExecutionData)
				assert.Equal(t, test.state, execData.GetState())
			} else {
				assert.ErrorIs(t, err, test.expectedErr)
			}
		})
	}
}

func sampleExecDataPostIncomeCDELoanApplicationRequest() *ExecutionData {
	perfiosMetadata := dto.IncomeDerivationMetadata{
		IncomeAlert:                           true, // Set based on actual test data
		SixMonthsEPF:                          true, // Set based on actual test data
		PerfiosStatementStatus:                "ACTIVE",
		EmployerNameMatch:                     utils.GetPointer(true),
		PositiveKeywordMatch:                  utils.GetPointer(true),
		NegativeKeywordMatch:                  utils.GetPointer(false),
		DepositDescriptionConsistency:         utils.GetPointer(true),
		CreditAmountConsistency:               utils.GetPointer(true),
		CreditDateConsistency:                 utils.GetPointer(false),
		StatementAccountHolderNameConsistency: utils.GetPointer(true),
		StatementAccountNoConsistency:         utils.GetPointer(false), // Set based on actual test data
	}

	return &ExecutionData{
		IncomeDerivationEvent: &dto.IncomeDerivationStreamMessage{
			Status:               incomeDerivationEvent.Success,
			DocumentType:         incomeDerivationEvent.BankStatement,
			IncomeAmount:         1000.0,
			IncomeDocumentSource: "test-source",
			UserDetails: &dto.UserDetails{
				Name: "test-name",
			},
			Metadata: &perfiosMetadata,
		},
		EnquiryStreamMsg: &creditBureauEnquiryStreamMessage{
			ReferenceID:       "test-reference-id",
			BureauEnquiryDate: time.Date(2024, 6, 27, 15, 30, 0, 0, time.UTC),
		},
		FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{
			Message: &loanAppAPI.Message{
				Application: &loanAppAPI.FlexiTermLoanApplication{
					Applicants: []loanAppAPI.FlexiTermLoanApplicant{
						{
							SafeID:      "test-safe-id",
							ApplicantID: "test-applicant-id",
							PrimaryID: &loanAppAPI.IDDetails{
								Type:   "PRIMARY",
								Number: "test-id-number",
							},
							EmploymentDetail: &loanAppAPI.EmploymentDetail{
								EmploymentType: "test-employment-type",
								OccupationCode: "B72",
								EmployerName:   "test-employer-name",
							},
						},
					},
					Products: []loanAppAPI.Product{
						{
							ProductType: loanAppAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
						},
					},
				},
			},
		},
		EcosystemData: &dto.EcosystemDataResponse{
			RemScore:      630,
			ApplicantType: dto.ApplicantTypeEnum_ETP,
			ExportedDate:  time.Date(2024, 6, 27, 15, 30, 0, 0, time.UTC).String(),
		},
		ApplicationMetadata: &dto.Metadata{},
	}
}

func samplePostIncomeCreditDecisionResponse() *creditDecisionEngineAPI.FICOCreditDecisionResponse {
	return &creditDecisionEngineAPI.FICOCreditDecisionResponse{
		Message: &creditDecisionEngineAPI.Message{
			ReferenceID:       utils.GetPointer("ref123"),
			ResponseTimestamp: utils.GetPointer(time.Date(2024, 1, 2, 3, 3, 4, 5, time.UTC)),
			Application: &creditDecisionEngineAPI.Application{
				ApplicationID: utils.ValToPtr("test-application-id"),
				Applicants: []creditDecisionEngineAPI.Applicant{
					{
						SafeID:      utils.ValToPtr("test-safe-id"),
						ApplicantID: utils.ValToPtr("test-applicant-id"),
					},
				},
				ApplicationDecision: &creditDecisionEngineAPI.ApplicationDecision{
					RecommendedDecision: utils.ValToPtr(string(constants.CDERecommendedDecisionRejected)),
				},
				Products: []creditDecisionEngineAPI.Product{
					{
						ProductType: utils.ValToPtr(creditDecisionEngineAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
						ProductDecision: &creditDecisionEngineAPI.ProductDecision{
							UwCriterias: []creditDecisionEngineAPI.UWCriteria{
								{
									ReasonCode: utils.ValToPtr("AAB"),
								},
							},
						},
					},
				},
			},
		},
	}
}
