package gxb

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	creditDecisionEngAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common/tracker"
	trackerMock "gitlab.com/gx-regional/dakota/lending/loan-app/logic/common/tracker/mocks"
	ftl "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/base"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/income_derivation_event"

	"gitlab.myteksi.net/dakota/workflowengine"
)

func Test_persistIncomeDerivationData(t *testing.T) {
	scenarios := []struct {
		desc          string
		params        interface{}
		expectedErr   error
		expectedState workflowengine.State
	}{
		{
			desc: "Success: stream message persisted",
			params: &dto.IncomeDerivationStreamMessage{
				Status:       income_derivation_event.Success,
				StatusReason: income_derivation_event.ReportVerified,
			},
			expectedState: ftl.StIncomeDetailsReceived,
		},
		{
			desc:        "Failure: Wrong params passed in persistIncomeDerivationData",
			params:      map[string]string{},
			expectedErr: common.ErrInvalidParams,
		},
		{
			desc: "Failure: Invalid status",
			params: &dto.IncomeDerivationStreamMessage{
				Status: income_derivation_event.Failed,
			},
			expectedState: ftl.StIncomeDetailsReceived,
		},
	}
	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			w := WorkflowImpl{}
			nextCtx, err := w.persistIncomeDerivation(context.Background(), "transitionID", &ExecutionData{
				FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{
					Message: &api.Message{
						OnboardingApplicationID: "onboarding-application-id",
					},
				},
			}, scenario.params)
			if scenario.expectedErr != nil {
				assert.Nil(t, nextCtx)
				assert.Equal(t, scenario.expectedErr.Error(), err.Error())
			} else {
				assert.Nil(t, err)
				assert.Equal(t, scenario.expectedState, nextCtx.GetState())
			}
		})
	}
}

func TestResumeIncomeDerivation(t *testing.T) {
	eventTime := time.Now()
	var nilExecutionData *ExecutionData
	scenarios := []struct {
		name                         string
		enableUtilisationTrackerFlag bool
		enableIEMFlag                bool
		isIncomeDerivationBypassed   bool
		currCtx                      *ExecutionData
		expectedCtx                  *ExecutionData
		mockFunc                     func(trackerMock *trackerMock.Client)
		expectedErr                  error
	}{
		{
			name:                         "happy-path: resume expired application with test cell offset",
			enableUtilisationTrackerFlag: true,
			enableIEMFlag:                true,
			currCtx: &ExecutionData{
				FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{Message: &api.Message{
					Application: &api.FlexiTermLoanApplication{
						Applicants: []api.FlexiTermLoanApplicant{
							{
								SafeID: "dummySafeID",
							},
						},
						Products: []api.Product{{
							ProductType:    api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
							SubProductType: api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
						}},
					},
				}},
				State: ftl.StIncomeDetailsReceived,
				CDEResponse: &creditDecisionResponseDto{
					CDEProduct: []creditDecisionEngAPI.Product{
						{
							ProductType: utils.GetPointer(creditDecisionEngAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
							ProductDecision: &creditDecisionEngAPI.ProductDecision{
								Offers: []creditDecisionEngAPI.Offer{
									{
										ApprovedCreditLimit: utils.GetPointer(500000.00),
										InterestRate:        utils.GetPointer(10.00),
										ApprovedTenor:       utils.GetPointer(int64(1)),
									},
								},
							},
						},
					},
				},
				EventTrackers: []eventTracker{{
					Event:           tracker.EventIEM,
					UtilizationType: tracker.INCR,
					UtilizedLimit:   500000,
					EventTime:       eventTime,
				}},
				IncomeDerivationEvent: &dto.IncomeDerivationStreamMessage{
					StatusReason: income_derivation_event.ReportVerified,
				}},
			expectedCtx: &ExecutionData{
				FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{Message: &api.Message{
					Application: &api.FlexiTermLoanApplication{
						Applicants: []api.FlexiTermLoanApplicant{
							{
								SafeID: "dummySafeID",
							},
						},
						Products: []api.Product{{
							ProductType:    api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
							SubProductType: api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
						}},
					},
				}},
				State: ftl.StIncomeDetailsDerived,
				CDEResponse: &creditDecisionResponseDto{
					CDEProduct: []creditDecisionEngAPI.Product{
						{
							ProductType: utils.GetPointer(creditDecisionEngAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
							ProductDecision: &creditDecisionEngAPI.ProductDecision{
								Offers: []creditDecisionEngAPI.Offer{
									{
										ApprovedCreditLimit: utils.GetPointer(500000.00),
										InterestRate:        utils.GetPointer(10.00),
										ApprovedTenor:       utils.GetPointer(int64(1)),
									},
								},
							},
						},
					},
				},
				EventTrackers: []eventTracker{{
					Event:           tracker.EventIEM,
					UtilizationType: tracker.INCR,
					UtilizedLimit:   500000,
					EventTime:       eventTime,
				},
					{
						Event:           tracker.EventIEM,
						UtilizationType: tracker.DECR,
						UtilizedLimit:   500000,
						EventTime:       eventTime,
					}},
				IncomeDerivationEvent: &dto.IncomeDerivationStreamMessage{
					StatusReason: income_derivation_event.ReportVerified,
				}},
			mockFunc: func(trackerMock *trackerMock.Client) {
				trackerMock.On("UpdateUtilizedLimit", mock.Anything, mock.Anything).Return(nil, nil)
			},
			expectedErr: nil,
		},
		{
			name:                         "happy-path: Failed income derivation",
			enableUtilisationTrackerFlag: true,
			enableIEMFlag:                true,
			currCtx: &ExecutionData{
				FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{Message: &api.Message{
					Application: &api.FlexiTermLoanApplication{
						Applicants: []api.FlexiTermLoanApplicant{
							{
								SafeID: "dummySafeID",
							},
						},
						Products: []api.Product{{
							ProductType:    api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
							SubProductType: api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
						}},
					},
				}},
				State: ftl.StIncomeDetailsReceived,
				CDEResponse: &creditDecisionResponseDto{
					CDEProduct: []creditDecisionEngAPI.Product{
						{
							ProductType: utils.GetPointer(creditDecisionEngAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
							ProductDecision: &creditDecisionEngAPI.ProductDecision{
								Offers: []creditDecisionEngAPI.Offer{
									{
										ApprovedCreditLimit: utils.GetPointer(500000.00),
										InterestRate:        utils.GetPointer(10.00),
										ApprovedTenor:       utils.GetPointer(int64(1)),
									},
								},
							},
						},
					},
				},
				EventTrackers: []eventTracker{{
					Event:           tracker.EventIEM,
					UtilizationType: tracker.INCR,
					UtilizedLimit:   500000,
					EventTime:       eventTime,
				}},
				IncomeDerivationEvent: &dto.IncomeDerivationStreamMessage{
					StatusReason: income_derivation_event.UnknownStatement,
				}},
			expectedCtx: &ExecutionData{
				FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{Message: &api.Message{
					Application: &api.FlexiTermLoanApplication{
						Applicants: []api.FlexiTermLoanApplicant{
							{
								SafeID: "dummySafeID",
							},
						},
						Products: []api.Product{{
							ProductType:    api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
							SubProductType: api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
						}},
					},
				}},
				State:        ftl.StApplicationFailed,
				Status:       api.ApplicationStatus_ApplicationStatus_FAILED,
				StatusReason: api.ApplicationStatusReason_INCOME_DERIVATION_FAILED,
				CDEResponse: &creditDecisionResponseDto{
					CDEProduct: []creditDecisionEngAPI.Product{
						{
							ProductType: utils.GetPointer(creditDecisionEngAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
							ProductDecision: &creditDecisionEngAPI.ProductDecision{
								Offers: []creditDecisionEngAPI.Offer{
									{
										ApprovedCreditLimit: utils.GetPointer(500000.00),
										InterestRate:        utils.GetPointer(10.00),
										ApprovedTenor:       utils.GetPointer(int64(1)),
									},
								},
							},
						},
					},
				},
				EventTrackers: []eventTracker{
					{
						Event:           tracker.EventIEM,
						UtilizationType: tracker.INCR,
						UtilizedLimit:   500000,
						EventTime:       eventTime,
					},
					{
						Event:           tracker.EventIEM,
						UtilizationType: tracker.DECR,
						UtilizedLimit:   500000,
						EventTime:       eventTime,
					},
				},
				IncomeDerivationEvent: &dto.IncomeDerivationStreamMessage{
					StatusReason: income_derivation_event.UnknownStatement,
				}},
			mockFunc: func(trackerMock *trackerMock.Client) {
				trackerMock.On("UpdateUtilizedLimit", mock.Anything, mock.Anything).Return(nil, nil)
			},
			expectedErr: nil,
		},
		{
			name:                         "happy-path: application already offset",
			enableUtilisationTrackerFlag: true,
			enableIEMFlag:                true,
			currCtx: &ExecutionData{
				FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{Message: &api.Message{
					Application: &api.FlexiTermLoanApplication{
						Applicants: []api.FlexiTermLoanApplicant{
							{
								SafeID: "dummySafeID",
							},
						},
						Products: []api.Product{{
							ProductType:    api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
							SubProductType: api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
						}},
					},
				}},
				State: ftl.StIncomeDetailsReceived,
				CDEResponse: &creditDecisionResponseDto{
					CDEProduct: []creditDecisionEngAPI.Product{
						{
							ProductType: utils.GetPointer(creditDecisionEngAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
							ProductDecision: &creditDecisionEngAPI.ProductDecision{
								Offers: []creditDecisionEngAPI.Offer{
									{
										ApprovedCreditLimit: utils.GetPointer(500000.00),
										InterestRate:        utils.GetPointer(10.00),
										ApprovedTenor:       utils.GetPointer(int64(1)),
									},
								},
							},
						},
					},
				},
				EventTrackers: []eventTracker{
					{
						Event:           tracker.EventIEM,
						UtilizationType: tracker.INCR,
						UtilizedLimit:   500000,
						EventTime:       eventTime,
					},
					{
						Event:           tracker.EventIEM,
						UtilizationType: tracker.DECR,
						UtilizedLimit:   500000,
						EventTime:       eventTime,
					},
				},
				IncomeDerivationEvent: &dto.IncomeDerivationStreamMessage{
					StatusReason: income_derivation_event.ReportVerified,
				},
			},
			expectedCtx: &ExecutionData{
				FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{Message: &api.Message{
					Application: &api.FlexiTermLoanApplication{
						Applicants: []api.FlexiTermLoanApplicant{
							{
								SafeID: "dummySafeID",
							},
						},
						Products: []api.Product{{
							ProductType:    api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
							SubProductType: api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
						}},
					},
				}},
				State: ftl.StIncomeDetailsDerived,
				CDEResponse: &creditDecisionResponseDto{
					CDEProduct: []creditDecisionEngAPI.Product{
						{
							ProductType: utils.GetPointer(creditDecisionEngAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
							ProductDecision: &creditDecisionEngAPI.ProductDecision{
								Offers: []creditDecisionEngAPI.Offer{
									{
										ApprovedCreditLimit: utils.GetPointer(500000.00),
										InterestRate:        utils.GetPointer(10.00),
										ApprovedTenor:       utils.GetPointer(int64(1)),
									},
								},
							},
						},
					},
				},
				EventTrackers: []eventTracker{{
					Event:           tracker.EventIEM,
					UtilizationType: tracker.INCR,
					UtilizedLimit:   500000,
					EventTime:       eventTime,
				},
					{
						Event:           tracker.EventIEM,
						UtilizationType: tracker.DECR,
						UtilizedLimit:   500000,
						EventTime:       eventTime,
					}},
				IncomeDerivationEvent: &dto.IncomeDerivationStreamMessage{
					StatusReason: income_derivation_event.ReportVerified,
				}},

			mockFunc: func(trackerMock *trackerMock.Client) {
				trackerMock.On("UpdateUtilizedLimit", mock.Anything, mock.Anything).Return(nil, nil)
			},
			expectedErr: nil,
		},
		{
			name:                         "happy-path: application there is no event tracker",
			enableUtilisationTrackerFlag: true,
			enableIEMFlag:                true,
			currCtx: &ExecutionData{
				FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{Message: &api.Message{
					Application: &api.FlexiTermLoanApplication{
						Applicants: []api.FlexiTermLoanApplicant{
							{
								SafeID: "dummySafeID",
							},
						},
						Products: []api.Product{{
							ProductType:    api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
							SubProductType: api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
						}},
					},
				}},
				State: ftl.StIncomeDetailsReceived,
				CDEResponse: &creditDecisionResponseDto{
					CDEProduct: []creditDecisionEngAPI.Product{
						{
							ProductType: utils.GetPointer(creditDecisionEngAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
							ProductDecision: &creditDecisionEngAPI.ProductDecision{
								Offers: []creditDecisionEngAPI.Offer{
									{
										ApprovedCreditLimit: utils.GetPointer(500000.00),
										InterestRate:        utils.GetPointer(10.00),
										ApprovedTenor:       utils.GetPointer(int64(1)),
									},
								},
							},
						},
					},
				},
				IncomeDerivationEvent: &dto.IncomeDerivationStreamMessage{
					StatusReason: income_derivation_event.ReportVerified,
				},
			},
			expectedCtx: &ExecutionData{
				FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{Message: &api.Message{
					Application: &api.FlexiTermLoanApplication{
						Applicants: []api.FlexiTermLoanApplicant{
							{
								SafeID: "dummySafeID",
							},
						},
						Products: []api.Product{{
							ProductType:    api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
							SubProductType: api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
						}},
					},
				}},
				State: ftl.StIncomeDetailsDerived,
				CDEResponse: &creditDecisionResponseDto{
					CDEProduct: []creditDecisionEngAPI.Product{
						{
							ProductType: utils.GetPointer(creditDecisionEngAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
							ProductDecision: &creditDecisionEngAPI.ProductDecision{
								Offers: []creditDecisionEngAPI.Offer{
									{
										ApprovedCreditLimit: utils.GetPointer(500000.00),
										InterestRate:        utils.GetPointer(10.00),
										ApprovedTenor:       utils.GetPointer(int64(1)),
									},
								},
							},
						},
					},
				},
				IncomeDerivationEvent: &dto.IncomeDerivationStreamMessage{
					StatusReason: income_derivation_event.ReportVerified,
				},
			},
			mockFunc: func(trackerMock *trackerMock.Client) {
				trackerMock.On("UpdateUtilizedLimit", mock.Anything, mock.Anything).Return(nil, nil)
			},
			expectedErr: nil,
		},
		{
			name:                         "happy-path: application already offset",
			enableUtilisationTrackerFlag: true,
			enableIEMFlag:                true,
			currCtx: &ExecutionData{
				FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{Message: &api.Message{
					Application: &api.FlexiTermLoanApplication{
						Applicants: []api.FlexiTermLoanApplicant{
							{
								SafeID: "dummySafeID",
							},
						},
						Products: []api.Product{{
							ProductType:    api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
							SubProductType: api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
						}},
					},
				}},
				State: ftl.StIncomeDetailsReceived,
				CDEResponse: &creditDecisionResponseDto{
					CDEProduct: []creditDecisionEngAPI.Product{
						{
							ProductType: utils.GetPointer(creditDecisionEngAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
							ProductDecision: &creditDecisionEngAPI.ProductDecision{
								Offers: []creditDecisionEngAPI.Offer{
									{
										ApprovedCreditLimit: utils.GetPointer(500000.00),
										InterestRate:        utils.GetPointer(10.00),
										ApprovedTenor:       utils.GetPointer(int64(1)),
									},
								},
							},
						},
					},
				},
				EventTrackers: []eventTracker{
					{
						Event:           tracker.EventIEM,
						UtilizationType: tracker.INCR,
						UtilizedLimit:   500000,
						EventTime:       eventTime,
					},
					{
						Event:           tracker.EventIEM,
						UtilizationType: tracker.DECR,
						UtilizedLimit:   500000,
						EventTime:       eventTime,
					},
				},
				IncomeDerivationEvent: &dto.IncomeDerivationStreamMessage{
					StatusReason: income_derivation_event.ReportVerified,
				},
			},
			expectedCtx: &ExecutionData{
				FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{Message: &api.Message{
					Application: &api.FlexiTermLoanApplication{
						Applicants: []api.FlexiTermLoanApplicant{
							{
								SafeID: "dummySafeID",
							},
						},
						Products: []api.Product{{
							ProductType:    api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
							SubProductType: api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
						}},
					},
				}},
				State: ftl.StIncomeDetailsDerived,
				CDEResponse: &creditDecisionResponseDto{
					CDEProduct: []creditDecisionEngAPI.Product{
						{
							ProductType: utils.GetPointer(creditDecisionEngAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
							ProductDecision: &creditDecisionEngAPI.ProductDecision{
								Offers: []creditDecisionEngAPI.Offer{
									{
										ApprovedCreditLimit: utils.GetPointer(500000.00),
										InterestRate:        utils.GetPointer(10.00),
										ApprovedTenor:       utils.GetPointer(int64(1)),
									},
								},
							},
						},
					},
				},
				EventTrackers: []eventTracker{{
					Event:           tracker.EventIEM,
					UtilizationType: tracker.INCR,
					UtilizedLimit:   500000,
					EventTime:       eventTime,
				},
					{
						Event:           tracker.EventIEM,
						UtilizationType: tracker.DECR,
						UtilizedLimit:   500000,
						EventTime:       eventTime,
					}},
				IncomeDerivationEvent: &dto.IncomeDerivationStreamMessage{
					StatusReason: income_derivation_event.ReportVerified,
				},
			},

			mockFunc: func(trackerMock *trackerMock.Client) {
				trackerMock.On("UpdateUtilizedLimit", mock.Anything, mock.Anything).Return(nil, nil)
			},
			expectedErr: nil,
		},
		{
			name:                         "sad-path:failed to update tracker",
			enableUtilisationTrackerFlag: true,
			enableIEMFlag:                true,
			currCtx: &ExecutionData{
				FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{Message: &api.Message{
					Application: &api.FlexiTermLoanApplication{
						Applicants: []api.FlexiTermLoanApplicant{
							{
								SafeID: "dummySafeID",
							},
						},
						Products: []api.Product{{
							ProductType:    api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
							SubProductType: api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
						}},
					},
				}},
				State: ftl.StIncomeDetailsReceived,
				CDEResponse: &creditDecisionResponseDto{
					CDEProduct: []creditDecisionEngAPI.Product{
						{
							ProductType: utils.GetPointer(creditDecisionEngAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
							ProductDecision: &creditDecisionEngAPI.ProductDecision{
								Offers: []creditDecisionEngAPI.Offer{
									{
										ApprovedCreditLimit: utils.GetPointer(500000.00),
										InterestRate:        utils.GetPointer(10.00),
										ApprovedTenor:       utils.GetPointer(int64(1)),
									},
								},
							},
						},
					},
				},
				EventTrackers: []eventTracker{{
					Event:           tracker.EventIEM,
					UtilizationType: tracker.INCR,
					UtilizedLimit:   500000,
					EventTime:       eventTime,
				}},
				IncomeDerivationEvent: &dto.IncomeDerivationStreamMessage{
					StatusReason: income_derivation_event.ReportVerified,
				},
			},
			mockFunc: func(trackerMock *trackerMock.Client) {
				trackerMock.On("UpdateUtilizedLimit", mock.Anything, mock.Anything).Return(nil, tracker.ErrFailedToUpdate)
			},
			expectedErr: tracker.ErrFailedToUpdate,
			expectedCtx: nilExecutionData,
		},
	}

	for _, scenario := range scenarios {
		tc := scenario
		t.Run(tc.name, func(t *testing.T) {
			mockTrackerClient := &trackerMock.Client{}
			tc.mockFunc(mockTrackerClient)

			workflowImpl := &WorkflowImpl{
				WorkflowImpl: base.WorkflowImpl{
					AppConfig: &config.AppConfig{
						FeatureFlags: config.FeatureFlags{
							EnableUtilisationTracker: tc.enableUtilisationTrackerFlag,
							EnableCDEEligibleProgram: tc.enableIEMFlag,
						},
					},
				},
				Tracker: mockTrackerClient,
			}
			constants.EnableUtilisationTracker = tc.enableUtilisationTrackerFlag
			constants.EnableCDEEligibleProgram = tc.enableIEMFlag
			nextCtx, err := workflowImpl.resumeIncomeDerivation(context.Background(), "", tc.currCtx, nil)
			if nextCtx != nil {
				assert.Equal(t, tc.expectedCtx, nextCtx.(*ExecutionData), tc.name)
			}
			assert.Equal(t, tc.expectedErr, err, tc.name)
		})
	}
}
