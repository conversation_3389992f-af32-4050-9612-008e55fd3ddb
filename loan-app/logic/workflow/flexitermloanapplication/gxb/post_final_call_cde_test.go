package gxb

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	creditDecisionEngineAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	creditDecisionEngineMockAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api/mock"
	loanAppAPI "gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	ftl "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/base"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	incomeDerivationEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/income_derivation_event"

	"gitlab.myteksi.net/dakota/workflowengine"
)

func Test_FinalCDECall(t *testing.T) {
	tests := []struct {
		name         string
		execData     workflowengine.ExecutionData
		mockFunc     func(m *creditDecisionEngineMockAPI.CrDecisionEng)
		expectedResp *creditDecisionEngineAPI.FICOCreditDecisionResponse
		expectErr    bool
		expectedErr  error
		state        workflowengine.State
	}{
		{
			name:     "happy-path",
			execData: sampleExecDataFinalCDELoanApplicationRequest(),
			mockFunc: func(m *creditDecisionEngineMockAPI.CrDecisionEng) {
				m.On("FICOCreditDecision", mock.Anything, mock.Anything).Return(sampleFinalCreditDecisionResponse(), nil).Once()
			},
			state:       ftl.StUserAcceptanceCDENotified,
			expectErr:   false,
			expectedErr: nil,
		},
		{
			name:        "sad-path-invalid-context",
			execData:    nil,
			expectErr:   true,
			expectedErr: errInvalidContext,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			ctx := context.Background()
			mockCDE := &creditDecisionEngineMockAPI.CrDecisionEng{}
			workflow := WorkflowImpl{
				CreditDecisionEngine: mockCDE,
				WorkflowImpl: base.WorkflowImpl{
					AppConfig: &config.AppConfig{
						FeatureFlags: config.FeatureFlags{
							EnableOfflineEcosystemDataFetcher: true,
						},
					},
				},
			}

			// Apply the mock behaviour of CreditDecisionEngine if any, defined in each test case
			if test.mockFunc != nil {
				test.mockFunc(mockCDE)
			}

			resp, err := workflow.notifyCDE(ctx, "TestId", test.execData, nil)
			if !test.expectErr {
				execData := resp.(*ExecutionData)
				assert.Equal(t, test.state, execData.GetState())
			} else {
				assert.ErrorIs(t, err, test.expectedErr)
			}
		})
	}
}

func sampleExecDataFinalCDELoanApplicationRequest() *ExecutionData {
	perfiosMetadata := dto.IncomeDerivationMetadata{
		IncomeAlert:            true,     // Set based on actual test data
		SixMonthsEPF:           true,     // Set based on actual test data
		PerfiosStatementStatus: "ACTIVE", // Set based on actual test data
	}

	return &ExecutionData{
		IncomeDerivationEvent: &dto.IncomeDerivationStreamMessage{
			Status:               incomeDerivationEvent.Success,
			DocumentType:         incomeDerivationEvent.BankStatement,
			IncomeAmount:         1000.0,
			IncomeDocumentSource: "test-source",
			UserDetails: &dto.UserDetails{
				Name: "test-name",
			},
			Metadata: &perfiosMetadata,
		},
		EnquiryStreamMsg: &creditBureauEnquiryStreamMessage{
			ReferenceID:       "test-reference-id",
			BureauEnquiryDate: time.Date(2024, 6, 27, 15, 30, 0, 0, time.UTC),
		},
		FlexiTermLoanRequest: &dto.CreateFlexiTermLoanApplicationWorkflowRequest{
			Message: &loanAppAPI.Message{
				Application: &loanAppAPI.FlexiTermLoanApplication{
					ApplicationID: "test-application-id",
					Applicants: []loanAppAPI.FlexiTermLoanApplicant{
						{
							SafeID:      "test-safe-id",
							ApplicantID: "test-applicant-id",
							PrimaryID: &loanAppAPI.IDDetails{
								Type:   "PRIMARY",
								Number: "test-id-number",
							},
							EmploymentDetail: &loanAppAPI.EmploymentDetail{
								EmploymentType: "test-employment-type",
								OccupationCode: "B72",
								EmployerName:   "test-employer-name",
							},
						},
					},
					Products: []loanAppAPI.Product{
						{
							ProductType: loanAppAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
						},
					},
				},
			},
		},
		UserAcceptanceData: &userAcceptanceData{
			Status: loanAppAPI.LoanOfferStatus_ACCEPTED,
			AcceptedCredit: &loanAppAPI.Money{
				CurrencyCode: "MY",
				Val:          21,
			},
		},
	}
}

func sampleFinalCreditDecisionResponse() *creditDecisionEngineAPI.FICOCreditDecisionResponse {
	return &creditDecisionEngineAPI.FICOCreditDecisionResponse{
		Message: &creditDecisionEngineAPI.Message{
			Application: &creditDecisionEngineAPI.Application{
				ApplicationID: utils.ValToPtr("test-application-id"),
				Applicants: []creditDecisionEngineAPI.Applicant{
					{
						SafeID:      utils.ValToPtr("test-safe-id"),
						ApplicantID: utils.ValToPtr("test-applicant-id"),
					},
				},
				ApplicationDecision: &creditDecisionEngineAPI.ApplicationDecision{
					RecommendedDecision: utils.ValToPtr(string(constants.CDERecommendedDecisionRejected)),
				},
				Products: []creditDecisionEngineAPI.Product{
					{
						ProductType: utils.ValToPtr(creditDecisionEngineAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
						ProductDecision: &creditDecisionEngineAPI.ProductDecision{
							UwCriterias: []creditDecisionEngineAPI.UWCriteria{
								{
									ReasonCode: utils.ValToPtr("AAB"),
								},
							},
						},
					},
				},
			},
		},
	}
}
