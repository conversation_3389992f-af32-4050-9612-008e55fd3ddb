package gxs

import (
	"encoding/json"

	creditDecisionEngineAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	mlScoringServiceAPI "gitlab.com/gx-regional/dakota/lending/loan-app/external/mlscoringservice/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	creditBureauServiceAPI "gitlab.myteksi.net/dakota/lending/credit-bureau/api"
	accountServiceAPI "gitlab.myteksi.net/dakota/lending/external/corebanking/accountservice"
	"gitlab.myteksi.net/dakota/workflowengine"
)

type ExecutionData struct {
	State                                 workflowengine.State
	CreateFlexiTermLoanApplicationRequest *dto.CreateFlexiTermLoanApplicationWorkflowRequest
	ApplicationID                         string
	ApplicationData                       map[string]*storage.Application
	ApplicantsEcosystemDetails            []*storage.ApplicantEcoSystemDetails
	PreBureauCreditDecisionRequest        *creditDecisionEngineAPI.FICOCreditDecisionRequest
	PreBureauCreditDecisionResponse       *creditDecisionEngineAPI.FICOCreditDecisionResponse
	CreditBureauEnquiryRequest            *creditBureauServiceAPI.ApplicantEnquiryRequest
	CreditBureauEnquiryResponse           *creditBureauServiceAPI.ApplicantEnquiryResponse
	CreditBureauEnquiryStreamMessage      *dto.CreditBureauEnquiryStreamMessage
	OfferedProduct                        api.ProductType
	ApplicationStatusTransition           *applicationStatusTransistionSchema.ApplicationStatusTransition
	ApplicationScoreRequest               *mlScoringServiceAPI.GetApplicationScoreRequest
	ApplicationScoreResponse              *mlScoringServiceAPI.GetApplicationScoreResponse
	CreateLOCAccountRequest               *accountServiceAPI.CreateLOCAccountRequest
	CreateLOCAccountResponse              *accountServiceAPI.CreateLOCAccountResponse
	LOCAccountCreationStreamMessage       *dto.LOCAccountCreationStreamMessage
	PostBureauCreditDecisionRequest       *creditDecisionEngineAPI.FICOCreditDecisionRequest
	PostBureauCreditDecisionResponse      *creditDecisionEngineAPI.FICOCreditDecisionResponse
	HasPostBureauApprovedLoan             bool
	UpdateOfferDetailsRequest             *api.UpdateLoanOfferDetailsRequest
	OverarchingAccountID                  string
	LendingAccountCreationStreamMessage   *dto.LendingAccountCreationDTO
}

func (p *ExecutionData) GetState() workflowengine.State {
	return p.State
}

// SetState ...
func (p *ExecutionData) SetState(state workflowengine.State) {
	p.State = state
}

// Marshal ...
func (p *ExecutionData) Marshal() ([]byte, error) {
	return json.Marshal(p)
}

// Unmarshal ...
func (p *ExecutionData) Unmarshal(byteData []byte) error {
	return json.Unmarshal(byteData, p)
}

// Clone ...
func (p *ExecutionData) Clone() *ExecutionData {
	newCtx := *p
	return &newCtx
}

// GetNewExecutionData ...
func (w *WorkflowImpl) GetNewExecutionData(req *dto.CreateFlexiTermLoanApplicationWorkflowRequest) workflowengine.ExecutionData {
	return &ExecutionData{CreateFlexiTermLoanApplicationRequest: req, ApplicationID: req.Message.Application.ApplicationID}
}
