package gxs

import (
	"context"
	"errors"
	"fmt"

	crDecisionEng "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	loanAppAPI "gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	ftl "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	we "gitlab.myteksi.net/dakota/workflowengine"
	tags "gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// nolint:dupl
func (w *WorkflowImpl) persistOnboardingEvent(ctx context.Context, transitionID string, executionData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := executionData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(createFlexiLoanWorkFlowLogTag, "Invalid context passed in persistOnboardingEvent state")
		return nil, errInvalidContext
	}
	nextCtx := currCtx.Clone()
	ctx = slog.AddTagsToContext(ctx, tags.TraceID(nextCtx.CreateFlexiTermLoanApplicationRequest.Message.OnboardingApplicationID))

	applicationStatusTransition, ok := params.(*applicationStatusTransistionSchema.ApplicationStatusTransition)
	if !ok {
		slog.FromContext(ctx).Warn(createFlexiLoanWorkFlowLogTag, "Invalid params passed in persistOnboardingEvent state")
		return nil, common.ErrInvalidApplicationTransitionDtoPassed
	}
	nextCtx.ApplicationStatusTransition = applicationStatusTransition
	nextCtx.SetState(ftl.StOnboardingEventPersisted)
	return nextCtx, nil
}

func (w *WorkflowImpl) handleOnboardingRejection(ctx context.Context, transitionID string, executionData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := executionData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(createFlexiLoanWorkFlowLogTag, "Invalid context passed in handleOnboardingRejection state")
		return nil, errInvalidContext
	}
	nextCtx := currCtx.Clone()
	ctx = slog.AddTagsToContext(ctx, tags.TraceID(nextCtx.CreateFlexiTermLoanApplicationRequest.Message.OnboardingApplicationID))

	var stmts []*storage.TransactionStmt
	if nextCtx.OfferedProduct == loanAppAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT && nextCtx.ApplicationData[string(loanAppAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT)].OnboardingApplicationID == nextCtx.ApplicationStatusTransition.ApplicationID {
		nextCtx = updateStatusAndStatusReason(nextCtx, string(crDecisionEng.ProductType_FLEXI_LOAN_LINE_OF_CREDIT), string(loanAppAPI.ApplicationStatus_ApplicationStatus_REJECTED), nextCtx.ApplicationStatusTransition.RejectionReason)
		stmts = append(stmts, w.FlexiLoanQueryGenerator.CreateUpdateApplicationStatement(nextCtx.ApplicationData, []crDecisionEng.ProductType{crDecisionEng.ProductType_FLEXI_LOAN_LINE_OF_CREDIT})...)
		stmts = append(stmts, w.FlexiLoanQueryGenerator.CreateUpdateOfferStatement(nextCtx.ApplicationData[string(loanAppAPI.ProductType_FLEXI_LOAN_LINE_OF_CREDIT)]))
	} else {
		slog.FromContext(ctx).Warn(createFlexiLoanWorkFlowLogTag, "Application ID in application transition event doesn't match to flexi Loan application Ids")
		return nil, errors.New("Application ID in application transition event doesn't match to flexi Loan application Ids")
	}
	err := w.Store.ExecuteTransaction(ctx, w.AppConfig, stmts)
	if err != nil {
		slog.FromContext(ctx).Warn(createFlexiLoanWorkFlowLogTag, fmt.Sprintf("Error in update application status and offer status: %s", err.Error()))
		return nil, err
	}
	nextCtx.SetState(ftl.StOnboardingChecksRejected)
	return nextCtx, nil
}
