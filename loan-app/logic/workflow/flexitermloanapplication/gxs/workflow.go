package gxs

import (
	"context"
	"fmt"

	creditDecisionEngineAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	mlScoringServiceAPI "gitlab.com/gx-regional/dakota/lending/loan-app/external/mlscoringservice/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	ftl "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/base"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	creditBureauServiceAPI "gitlab.myteksi.net/dakota/lending/credit-bureau/api"
	grabAPI "gitlab.myteksi.net/dakota/lending/external/grab/api"
	"gitlab.myteksi.net/dakota/lending/external/notification"
	customerMasterAPI "gitlab.myteksi.net/dakota/lending/external/onboarding/customermaster"
	LoanCoreClient "gitlab.myteksi.net/dakota/lending/loan-core/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	we "gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"
)

const (
	createFlexiLoanWorkFlowLogTag = "createFlexiLoanApplicationWorkFlowLog"
)

var (
	errInvalidContext = common.ErrInvalidContext
)

var (
	wfExecute = we.Execute
	wfGet     = we.GetExecution
)

type WorkflowImpl struct {
	base.WorkflowImpl
	CreditDecisionEngine      creditDecisionEngineAPI.CrDecisionEng `inject:"client.creditDecisionEng"`
	GrabClient                grabAPI.Grab                          `inject:"client.grabAPI"`
	FlexiLoanQueryGenerator   IFlexiLoanGenerator                   `inject:"flexiLoanQueryGenerator"`
	CreditBureauServiceClient creditBureauServiceAPI.CreditBureau   `inject:"client.creditBureauService"`
	MlScoringServiceClient    mlScoringServiceAPI.MlScoringService  `inject:"client.mlScoringService"`
	CustomerMasterClient      customerMasterAPI.CustomerMaster      `inject:"client.customerMaster"`
	NotificationService       notification.NotificationService      `inject:"client.notificationService"`
	LoanCoreClient            LoanCoreClient.LoanCore               `inject:"client.loanCoreClient"`
}

// Register : register flexi-loan application workflow
// nolint : funlen
func (w *WorkflowImpl) Register() {
	flexiLoanWorkFlow := we.NewWorkflow(constants.GXSWorkflowID.ToString(), func() we.ExecutionData { return &ExecutionData{} })

	var (
		transactionalRetryOption *we.TransitionOptions
		auxiliaryRetryOption     *we.TransitionOptions
	)
	if retryOption := w.AppConfig.WorkflowRetryConfig.CreateFlexiTermLoanApplication.TransactionalRetryOption; retryOption != nil {
		// used for time-sensitive transactional states
		transactionalRetryOption = &we.TransitionOptions{
			RetryPolicy: &we.RetryPolicy{
				Interval:    retryOption.IntervalInSeconds,
				MaxAttempts: retryOption.MaxAttempt,
			},
		}
	}
	if retryOption := w.AppConfig.WorkflowRetryConfig.CreateFlexiTermLoanApplication.AuxiliaryRetryOption; retryOption != nil {
		auxiliaryRetryOption = &we.TransitionOptions{RetryPolicy: &we.RetryPolicy{
			Interval:    retryOption.IntervalInSeconds,
			MaxAttempts: retryOption.MaxAttempt}}
	}

	// Create flexi-loan application
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StInit, ftl.EvCreateApplication, w.createApplication, nil, ftl.StApplicationCreated)

	// Fetch Ecosystem Data
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StApplicationCreated, ftl.EvStartAsyncWorkflow, w.fetchEcosystemData, transactionalRetryOption, ftl.StEcoSystemDataFetched)

	// Pre bureau Checks
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StEcoSystemDataFetched, ftl.EvNoNeed, w.preBureauCDECheck, transactionalRetryOption, ftl.StPreBureauCDECheckPassed, ftl.StPreBureauCDECheckRejected, ftl.StInterventionNeeded)

	// Fetch credit bureau data
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StPreBureauCDECheckPassed, ftl.EvNoNeed, w.fetchCreditBureauData, transactionalRetryOption, ftl.StCreditBureauDataRequested)
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StCreditBureauDataRequested, ftl.EvSaveCreditBureauDataCallback, w.persistCreditBureauData, nil, ftl.StFetchCreditBureauDataCompleted, ftl.StFetchCreditBureauDataFailure)

	// Application scoring
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StFetchCreditBureauDataCompleted, ftl.EvHandleCreditBureauResume, w.fetchScoringModelData, transactionalRetryOption, ftl.StFetchScoringModelDataSuccess)

	// Post Credit Bureau
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StFetchScoringModelDataSuccess, ftl.EvNoNeed, w.postBureauCDECheck, transactionalRetryOption, ftl.StPostBureauCDEApproved, ftl.StPostBureauCDERejected, ftl.StInterventionNeeded)

	// Publish post bureau events (onboarding checks)
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StPostBureauCDERejected, ftl.EvNoNeed, w.publishStatusToOnboarding, transactionalRetryOption, ftl.StPostBureauCDERejectedPublished)
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StPostBureauCDEApproved, ftl.EvNoNeed, w.publishStatusToOnboarding, transactionalRetryOption, ftl.StPostBureauCDEApprovedPublished)

	// Handle onboarding Rejection
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StPostBureauCDEApprovedPublished, ftl.EvOnboardingRejected, w.persistOnboardingEvent, nil, ftl.StOnboardingEventPersisted)
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StOnboardingEventPersisted, ftl.EvResumeOnboardingRejected, w.handleOnboardingRejection, nil, ftl.StOnboardingChecksRejected)

	// Expire
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StPostBureauCDEApprovedPublished, ftl.EvExpireCallback, w.expireApplicationCallBack, nil, ftl.StExpireCallbackReceived)

	// customer Response
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StPostBureauCDEApprovedPublished, ftl.EvCustomerOfferResponse, w.persistCustomerResponse, nil, ftl.StPersistCustomerResponse)
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StPersistCustomerResponse, ftl.EvUpdateOfferResumeAsyncWorkflow, w.handleCustomerResponse, nil, ftl.StCustomerAcceptedLoanOffer, ftl.StCustomerDeclinedLoanOffer)
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StCustomerDeclinedLoanOffer, ftl.EvNoNeed, w.publishStatusToOnboarding, transactionalRetryOption, ftl.StCustomerDeclinedLoanOfferPublished)

	// Create Overarching account
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StCustomerAcceptedLoanOffer, ftl.EvNoNeed, w.createOverarchingAccount, transactionalRetryOption, ftl.StOverarchingAccCreationInitiated, ftl.StOverarchingAccCheckPassed)
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StOverarchingAccCreationInitiated, ftl.EvOverarchingAccountCreationCallback, w.persistOverarchingAccountCreationResponse, nil, ftl.StOverarchingCallbackReceived)
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StCustomerAcceptedLoanOffer, ftl.EvOverarchingAccountCreationCallback, w.persistOverarchingAccountCreationResponse, nil, ftl.StOverarchingCallbackReceived)
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StOverarchingCallbackReceived, ftl.EvResumeOverarchingCreation, w.completeOverarchingAccountCreation, nil, ftl.StOverarchingAccCheckPassed, ftl.StOverarchingAccountCreationFailed)

	// Create LOC account
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StOverarchingAccCheckPassed, ftl.EvNoNeed, w.createFlexiLimitAccount, transactionalRetryOption, ftl.StLineCreationInitiated)
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StLineCreationInitiated, ftl.EvFlexiLimitAccountCreationSuccess, w.persistFlexiLimitAccountCreationResponse, nil, ftl.StLineCreationCallbackReceived)
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StOverarchingAccCheckPassed, ftl.EvFlexiLimitAccountCreationSuccess, w.persistFlexiLimitAccountCreationResponse, nil, ftl.StLineCreationCallbackReceived)
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StLineCreationCallbackReceived, ftl.EvResumeLineCreation, w.completeFlexiLimitAccountCreation, nil, ftl.StFlexiLimitAccountCreationSuccess, ftl.StFlexiLimitAccountCreationFailed)
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StFlexiLimitAccountCreationSuccess, ftl.EvNoNeed, w.publishStatusToOnboarding, transactionalRetryOption, ftl.StFlexiLimitAccountCreationSuccessPublished)

	//Notify success LOC Account Creation
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StFlexiLimitAccountCreationSuccessPublished, ftl.EvNoNeed, w.sendLOCAccountCreatedPushInboxNotification, auxiliaryRetryOption, ftl.StLOCAccountCreationSuccessPushInboxNotified)
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StLOCAccountCreationSuccessPushInboxNotified, ftl.EvNoNeed, w.sendLOCAccountCreatedEmailNotification, auxiliaryRetryOption, ftl.StLOCAccountCreationSuccessEmailNotified)

	// update cde with customer acceptance
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StLOCAccountCreationSuccessEmailNotified, ftl.EvNoNeed, w.updateCDEWithCustomerAcceptance, transactionalRetryOption, ftl.StUpdateCDEWithCustomerAcceptance)

	// Rejections
	flexiLoanWorkFlow.AddTransition(createFlexiLoanWorkFlowLogTag, ftl.StPreBureauCDECheckRejected, ftl.EvNoNeed, w.publishStatusToOnboarding, transactionalRetryOption, ftl.StPreBureauCDERejectedPublished)
	we.RegisterWorkflow(flexiLoanWorkFlow)

}

// ConsumeLOCAccountCreationEvent consumes event from account service loc creation stream and resumes workflow for flexi loan application
func ConsumeLOCAccountCreationEvent(ctx context.Context, data *dto.LOCAccountCreationStreamMessage) error {
	execData, err := wfGet(ctx, we.Execution{
		WorkflowID: constants.GXSWorkflowID.ToString(),
		RequestID:  data.ReferenceID,
	})

	if err != nil {
		slog.FromContext(ctx).Warn(createFlexiLoanWorkFlowLogTag, fmt.Sprintf("Error when getting existing workflow: %s", err.Error()))
		return err
	}
	if execData.GetState() == ftl.StOverarchingAccCheckPassed || execData.GetState() == ftl.StLineCreationInitiated {
		_, consumeErr := wfExecute(ctx, we.Execution{
			WorkflowID:     constants.GXSWorkflowID.ToString(),
			RequestID:      data.ReferenceID,
			ExecutionEvent: ftl.EvFlexiLimitAccountCreationSuccess,
		}, data)
		if consumeErr != nil {
			slog.FromContext(ctx).Warn(createFlexiLoanWorkFlowLogTag, fmt.Sprintf("Workflow execute error in ConsumeLOCAccountCreationEvent: %s", consumeErr.Error()))
			return utils.CheckIfResourceNotFound(ctx, consumeErr, createFlexiLoanWorkFlowLogTag, data.ReferenceID)
		}
	}

	// execute async processing
	slog.FromContext(ctx).Info(createFlexiLoanWorkFlowLogTag, fmt.Sprintf("starting async workflow execution in ConsumeLOCAccountCreationEvent for referenceID: %s", data.ReferenceID), utils.GetTraceID(ctx))
	newCtx := utils.NewCtxWithLoggerAndSpan(ctx)
	gconcurrent.Go(newCtx, createFlexiLoanWorkFlowLogTag, utils.ExecuteEventAsync(data.ReferenceID, ftl.EvResumeLineCreation, createFlexiLoanWorkFlowLogTag, constants.GXSWorkflowID.ToString()))
	return nil
}

// ConsumeCBSData consumes event from credit-bureau event stream and resumes workflow for Flexi Loan application
func ConsumeCBSData(ctx context.Context, cbsDataDTO *dto.CreditBureauEnquiryStreamMessage) error {
	nextCtx, err := wfExecute(ctx, we.Execution{
		WorkflowID:     constants.GXSWorkflowID.ToString(),
		TransitionID:   cbsDataDTO.ReferenceID,
		ExecutionEvent: ftl.EvSaveCreditBureauDataCallback,
	}, cbsDataDTO)
	if err != nil {
		slog.FromContext(ctx).Warn(createFlexiLoanWorkFlowLogTag, fmt.Sprintf("Workflow execute error in ConsumeCBSData: %s", err.Error()))
		return utils.CheckIfResourceNotFound(ctx, err, createFlexiLoanWorkFlowLogTag, cbsDataDTO.ReferenceID)
	}
	currCtx, ok := nextCtx.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(createFlexiLoanWorkFlowLogTag, "Invalid context passed in ConsumeCBSData ")
		return nil
	}
	newCtx := utils.NewCtxWithLoggerAndSpan(ctx)
	gconcurrent.Go(newCtx, createFlexiLoanWorkFlowLogTag, utils.ExecuteEventAsync(currCtx.CreateFlexiTermLoanApplicationRequest.Message.OnboardingApplicationID, ftl.EvHandleCreditBureauResume, createFlexiLoanWorkFlowLogTag, constants.GXSWorkflowID.ToString()))
	return nil
}

// ConsumeLendingAccountCreationData consumes event from account-service event stream and resumes workflow for Flexi Loan application
func ConsumeLendingAccountCreationData(ctx context.Context, lendingAccCreationDTO *dto.LendingAccountCreationDTO) error {
	nextCtx, consumeErr := wfExecute(ctx, we.Execution{
		WorkflowID:     constants.GXSWorkflowID.ToString(),
		TransitionID:   lendingAccCreationDTO.ReferenceID,
		ExecutionEvent: ftl.EvOverarchingAccountCreationCallback,
	}, lendingAccCreationDTO)
	if consumeErr != nil {
		slog.FromContext(ctx).Warn(createFlexiLoanWorkFlowLogTag, fmt.Sprintf("Workflow execute error in ConsumeOverarchingAccountCreationEvent: %s", consumeErr.Error()))
		return utils.CheckIfResourceNotFound(ctx, consumeErr, createFlexiLoanWorkFlowLogTag, lendingAccCreationDTO.ReferenceID)
	}

	// execute async processing
	currCtx, ok := nextCtx.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(createFlexiLoanWorkFlowLogTag, "Invalid context passed in ConsumeOverarchingAccountCreationEvent ")
		return nil
	}
	slog.FromContext(ctx).Info(createFlexiLoanWorkFlowLogTag, fmt.Sprintf("starting async workflow execution in ConsumeOverarchingAccountCreationEvent for referenceID: %s", lendingAccCreationDTO.ReferenceID), utils.GetTraceID(ctx))
	newCtx := utils.NewCtxWithLoggerAndSpan(ctx)
	gconcurrent.Go(newCtx, createFlexiLoanWorkFlowLogTag, utils.ExecuteEventAsync(currCtx.CreateFlexiTermLoanApplicationRequest.Message.OnboardingApplicationID, ftl.EvResumeOverarchingCreation, createFlexiLoanWorkFlowLogTag, constants.GXSWorkflowID.ToString()))
	return nil
}

// ConsumeOnboardingRejectionEvent consumes onboarding rejected event for flexi Loan application and resume workflow
func ConsumeOnboardingRejectionEvent(ctx context.Context, data *applicationStatusTransistionSchema.ApplicationStatusTransition) error {
	execData, err := wfGet(ctx, we.Execution{
		WorkflowID: constants.GXSWorkflowID.ToString(),
		RequestID:  data.ApplicationID,
	})
	if err != nil {
		slog.FromContext(ctx).Warn(createFlexiLoanWorkFlowLogTag, fmt.Sprintf("Error when getting existing workflow: %s", err.Error()))
		return utils.CheckIfResourceNotFound(ctx, err, createFlexiLoanWorkFlowLogTag, data.ApplicationID)
	}
	if execData.GetState() == ftl.StPostBureauCDEApprovedPublished {
		_, err2 := wfExecute(ctx, we.Execution{
			WorkflowID:     constants.GXSWorkflowID.ToString(),
			RequestID:      data.ApplicationID,
			ExecutionEvent: ftl.EvOnboardingRejected,
		}, data)
		if err2 != nil {
			slog.FromContext(ctx).Warn(createFlexiLoanWorkFlowLogTag, fmt.Sprintf("Workflow execute error in ConsumeOnboardingRejectionEvent: %s", err2.Error()))
			return utils.CheckIfResourceNotFound(ctx, err2, createFlexiLoanWorkFlowLogTag, data.ApplicationID)
		}
		newCtx := utils.NewCtxWithSpan(ctx)
		gconcurrent.Go(newCtx, createFlexiLoanWorkFlowLogTag, utils.ExecuteEventAsync(data.ApplicationID, ftl.EvResumeOnboardingRejected, createFlexiLoanWorkFlowLogTag, constants.GXSWorkflowID.ToString()))
	}
	return nil
}

// ConsumeUpdateOfferDetails consumes customer response and resumes workflow for Flexi Loan application
func ConsumeUpdateOfferDetails(ctx context.Context, updateLoanOfferDetailsRequest *api.UpdateLoanOfferDetailsRequest, onboardingApplicationID string) error {
	execData, err := wfExecute(ctx, we.Execution{
		WorkflowID:     constants.GXSWorkflowID.ToString(),
		RequestID:      onboardingApplicationID,
		ExecutionEvent: ftl.EvCustomerOfferResponse,
	}, updateLoanOfferDetailsRequest)
	if err != nil {
		return utils.CheckIfResourceNotFound(ctx, err, createFlexiLoanWorkFlowLogTag, onboardingApplicationID)
	}
	if execData.GetState() == ftl.StPersistCustomerResponse {
		newCtx := utils.NewCtxWithSpan(ctx)
		gconcurrent.Go(newCtx, createFlexiLoanWorkFlowLogTag, utils.ExecuteEventAsync(onboardingApplicationID, ftl.EvUpdateOfferResumeAsyncWorkflow, createFlexiLoanWorkFlowLogTag, constants.GXSWorkflowID.ToString()))
	}
	return nil
}

// ExpireLoanApplication
func (w *WorkflowImpl) ExpireLoanApplication(ctx context.Context, referenceID string) error {
	_, err := wfExecute(ctx, we.Execution{
		WorkflowID:     constants.GXSWorkflowID.ToString(),
		RequestID:      referenceID,
		ExecutionEvent: ftl.EvExpireCallback,
	}, "")
	if err != nil {
		slog.FromContext(ctx).Warn(createFlexiLoanWorkFlowLogTag, fmt.Sprintf("Workflow execute error in ExpireLoanApplication: %s", err.Error()))
		return utils.CheckIfResourceNotFound(ctx, err, createFlexiLoanWorkFlowLogTag, referenceID)
	}

	// execute async processing
	slog.FromContext(ctx).Info(createFlexiLoanWorkFlowLogTag, fmt.Sprintf("starting async workflow execution in ExpireLoanApplication for referenceID: %s ", referenceID), utils.GetTraceID(ctx))
	newCtx := utils.NewCtxWithLoggerAndSpan(ctx)
	gconcurrent.Go(newCtx, createFlexiLoanWorkFlowLogTag, utils.ExecuteEventAsync(referenceID, ftl.EvExpireResume, createFlexiLoanWorkFlowLogTag, constants.GXSWorkflowID.ToString()))
	return nil
}
