// Package applicationstatustransition ...
package applicationstatustransition

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	loanAppDTO "gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/external/appian/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/bizflexicreditapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/bundledapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexicardapplication"
	gxsFlexiTermLoanApplication "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/gxs"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"

	"gitlab.myteksi.net/dakota/common/servicename"
	servusData "gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/data"
)

// HandleApplicationStatusTransitionEvent ...
// nolint: funlen,gocognit
func (a *ApplicationStatusTransitionImpl) HandleApplicationStatusTransitionEvent(ctx context.Context, data *applicationStatusTransistionSchema.ApplicationStatusTransition) error {
	if data == nil {
		slog.FromContext(ctx).Warn(constants.ApplicationStatusTransitionKafkaConsumerLogTag, "empty event stream msg", utils.GetTraceID(ctx))
		return nil
	}
	rejectionReasons := []string{
		string(ApplicationRejectionReasonECDDFailed),
		string(ApplicationRejectionReasonHighRiskScore),
		string(ApplicationRejectionReasonFraudCheckRejected),
	}

	if data.ApplicationType == string(ApplicationTypeBizFlexiCredit) || data.ApplicationType == string(ApplicationTypeBizFlexiCreditForGXB) {
		slog.FromContext(ctx).Info(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Application Status Transition data received for biz flexi credit: %s onboarding id %s", utils.ToJSON(data), data.ApplicationID))
		err := handleConsumeOnboardingEventForBizFlexiCredit(ctx, data)
		if err != nil {
			slog.FromContext(ctx).Error(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Error in consume onboarding event for biz flexi credit %s", err.Error()))
			return err
		}
	}

	if (data.ApplicationType == string(ApplicationTypeLending) || data.ApplicationType == string(ApplicationTypeFlexicard)) && utils.StringInSlice(data.RejectionReason, rejectionReasons) {
		slog.FromContext(ctx).Info(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Application Status Transition data received : %s onboarding id %s", utils.ToJSON(data), data.ApplicationID))
		applications, applicationFindErr := storage.ApplicationDao.Find(ctx, servusData.EqualTo("OnboardingApplicationID", data.ApplicationID))
		if applicationFindErr != nil {
			if applicationFindErr == servusData.ErrNoData {
				return nil
			}
			slog.FromContext(ctx).Warn(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Error in fetching application from db, err: %s", applicationFindErr.Error()))
			return applicationFindErr
		}

		// Checking if application is loan only(appian), flexicard only (workflow) or bundled application (workflow)
		application := applications[0]
		// If metadata has workflow id of flexicard/bundled
		if application.Metadata != nil {
			metadata := loanAppDTO.Metadata{}
			err := json.Unmarshal(*application.Metadata, &metadata)
			if err != nil {
				slog.FromContext(ctx).Warn(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Error unmarshalling application metadata for onboarding id %s", data.ApplicationID))
				return fmt.Errorf("Error unmarshalling application metadata for onboarding id %s", data.ApplicationID)
			}
			if metadata.WorkflowID == flexicardapplication.FlexiCardApplicationWorkflowID {
				err := flexicardapplication.ConsumeOnboardingRejectionEvent(ctx, data)
				if err != nil {
					return err
				}
				return nil
			} else if metadata.WorkflowID == bundledapplication.BundledApplicationWorkflowID {
				err := bundledapplication.ConsumeOnboardingRejectionEvent(ctx, data)
				if err != nil {
					return err
				}
				return nil
			} else if metadata.WorkflowID == constants.GXSWorkflowID.ToString() {
				err := gxsFlexiTermLoanApplication.ConsumeOnboardingRejectionEvent(ctx, data)
				if err != nil {
					return err
				}
				return nil
			}
		}

		//default its loan only (appian)
		slog.FromContext(ctx).Info(constants.ApplicationStatusTransitionKafkaConsumerLogTag, "starting to update application status to rejected in lending and appian", utils.GetTraceID(ctx))
		err := a.updateApplicationStatusToRejected(ctx, data.ApplicationID, data.RejectionReason)
		if err != nil {
			return err
		}
	}
	return nil
}

func (a *ApplicationStatusTransitionImpl) updateApplicationStatusToRejected(ctx context.Context, onboardingApplicationID string, rejectionReason string) error {
	applications, err := storage.ApplicationDao.Find(ctx, servusData.EqualTo("Status", string(api.ApplicationStatus_ApplicationStatus_PENDING_ACCEPTANCE)),
		servusData.EqualTo("OnboardingApplicationID", onboardingApplicationID),
	)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Error in finding the application in database, err: %s", utils.ToJSON(err)), utils.GetTraceID(ctx))
		if err != data.ErrNoData {
			return err
		}
		return nil
	}

	application := applications[0]
	applicationID := application.ApplicationID
	application.Status = string(api.ApplicationStatus_ApplicationStatus_REJECTED)
	application.StatusReason = rejectionReason
	application.UpdatedAt = time.Now().UTC()
	application.UpdatedBy = servicename.ApplicationService.ToString()
	err = storage.ApplicationDao.Update(ctx, application)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Error while updating application in DB: %s", utils.ToJSON(err)), utils.GetTraceID(ctx))
		return err
	}
	err = updateLoanOfferExpired(ctx, applicationID)
	if err != nil {
		return err
	}
	err = a.updateLoanOfferAppianExpired(ctx, applicationID)
	if err != nil {
		return err
	}
	return nil
}

func updateLoanOfferExpired(ctx context.Context, applicationID string) error {
	offerDetails, err := common.FetchOfferDetails(ctx, []servusData.Condition{servusData.EqualTo("ApplicationID", applicationID)}, constants.ApplicationStatusTransitionKafkaConsumerLogTag)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Error while fetching offer details from DB: %s", utils.ToJSON(err)), utils.GetTraceID(ctx))
		return err
	}
	offer := offerDetails[0]
	offer.Status = string(api.LoanOfferStatus_EXPIRED)
	offer.UpdatedAt = time.Now().UTC()
	offer.UpdatedBy = servicename.ApplicationService.ToString()
	err = storage.OfferDao.Update(ctx, offer)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Error while updating offer details in DB: %s", utils.ToJSON(err)), utils.GetTraceID(ctx))
		return err
	}
	return nil
}

func (a *ApplicationStatusTransitionImpl) updateLoanOfferAppianExpired(ctx context.Context, applicationID string) error {
	req := &dto.UpdateOfferDetailsRequest{
		ReferenceID:   uuid.NewString(),
		ApplicationID: applicationID,
		Status:        string(api.LoanOfferStatus_EXPIRED),
	}
	if err := common.UpdateOfferDetailsInAppian(ctx, a.Config, a.AppianClient, req); err != nil {
		slog.FromContext(ctx).Warn(constants.ApplicationStatusTransitionKafkaConsumerLogTag, fmt.Sprintf("Error while updating offer details in appian: %s", utils.ToJSON(err)), utils.GetTraceID(ctx))
		return err
	}
	return nil
}

func handleConsumeOnboardingEventForBizFlexiCredit(ctx context.Context, data *applicationStatusTransistionSchema.ApplicationStatusTransition) error {
	// handling for application rejection
	rejectionReasonsForBizFlexiCredit := []string{
		string(ApplicationRejectionReasonECDDFailed),
		string(ApplicationRejectionReasonHighRiskScore),
		string(ApplicationRejectionReasonFraudCheckRejected),
		string(ApplicationRejectionReasonHighRiskScore),
		string(ApplicationRejectionReasonECDDFailed),
		string(ApplicationRejectionReasonFraudCheckFailed),
		string(ApplicationRejectionReasonAsEKYCFailed),
		string(ApplicationRejectionReasonAsNameScreeningSanctioned),
		string(ApplicationRejectionReasonAsUndefinedReason),
		string(ApplicationRejectionReasonAsUndefinedCountry),
		string(ApplicationRejectionReasonECDDFailedForGXB),
	}
	if data.ToStatus == string(ApplicationStatusRejected) && utils.StringInSlice(data.RejectionReason, rejectionReasonsForBizFlexiCredit) {
		err := bizflexicreditapplication.ConsumeOnboardingRejectionEvent(ctx, data)
		if err != nil {
			return err
		}
	}
	// handling for application expiry
	if data.ToStatus == string(ApplicationStatusExpired) {
		err := bizflexicreditapplication.ConsumeApplicationExpiredEvent(ctx, data)
		if err != nil {
			return err
		}
	}
	return nil
}
