package applicationstatustransition

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dakota/lending/loan-app/external/appian/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/external/appian/mocks"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/resources"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils/cache"
	applicationStatusTransistionSchema "gitlab.com/gx-regional/dakota/schemas/streams/apis/application_status_transition"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/streaminfo"

	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	sndconfig "gitlab.myteksi.net/snd/streamsdk/kafka/config"
)

func TestApplicationStatusTransitionImpl_HandleApplicationStatusTransitionEvent(t *testing.T) {
	tests := []struct {
		name                              string
		data                              *applicationStatusTransistionSchema.ApplicationStatusTransition
		applicationDAOFindExpectedCalls   int
		applicationDAOUpdateExpectedCalls int
		offerDAOExpectedCalls             int
		appianClientExpectedCalls         int
	}{
		{
			name: "test 1, happy path",
			data: &applicationStatusTransistionSchema.ApplicationStatusTransition{
				StreamInfo:      streaminfo.StreamInfo{},
				CustomerID:      "TEST-ID",
				FromStatus:      "IN_REVIEW",
				ToStatus:        "AS_REJECTED",
				ApplicationType: "AT_LENDING",
				ApplicationID:   "000",
				RejectionReason: "AS_HIGH_RISKSCORE",
				Metadata:        nil,
				EventTime:       time.Time{},
			},
			applicationDAOFindExpectedCalls:   2,
			applicationDAOUpdateExpectedCalls: 1,
			offerDAOExpectedCalls:             1,
			appianClientExpectedCalls:         1,
		},
		{
			name: "test 2, valid data but invalid application type",
			data: &applicationStatusTransistionSchema.ApplicationStatusTransition{
				StreamInfo:      streaminfo.StreamInfo{},
				CustomerID:      "TEST-ID",
				FromStatus:      "IN_REVIEW",
				ToStatus:        "AS_REJECTED",
				ApplicationType: "AT_DEPOSIT",
				ApplicationID:   "000",
				RejectionReason: "AS_HIGH_RISKSCORE",
				Metadata:        nil,
				EventTime:       time.Time{},
			},
			applicationDAOFindExpectedCalls:   0,
			applicationDAOUpdateExpectedCalls: 0,
			offerDAOExpectedCalls:             0,
			appianClientExpectedCalls:         0,
		},
		{
			name: "test 3, happy path with different rejection reason",
			data: &applicationStatusTransistionSchema.ApplicationStatusTransition{
				StreamInfo:      streaminfo.StreamInfo{},
				CustomerID:      "TEST-ID",
				FromStatus:      "IN_REVIEW",
				ToStatus:        "AS_REJECTED",
				ApplicationType: "AT_LENDING",
				ApplicationID:   "000",
				RejectionReason: "AS_ECDD_FAILED",
				Metadata:        nil,
				EventTime:       time.Time{},
			},
			applicationDAOFindExpectedCalls:   2,
			applicationDAOUpdateExpectedCalls: 1,
			offerDAOExpectedCalls:             1,
			appianClientExpectedCalls:         1,
		},
		{
			name: "test 4, negative path with different rejection reason",
			data: &applicationStatusTransistionSchema.ApplicationStatusTransition{
				StreamInfo:      streaminfo.StreamInfo{},
				CustomerID:      "TEST-ID",
				FromStatus:      "IN_REVIEW",
				ToStatus:        "AS_REJECTED",
				ApplicationType: "AT_LENDING",
				ApplicationID:   "000",
				RejectionReason: "AS_UNKNOWN",
				Metadata:        nil,
				EventTime:       time.Time{},
			},
			applicationDAOFindExpectedCalls:   0,
			applicationDAOUpdateExpectedCalls: 0,
			offerDAOExpectedCalls:             0,
			appianClientExpectedCalls:         0,
		},
		{
			name:                              "test 5, data nil",
			data:                              nil,
			applicationDAOFindExpectedCalls:   0,
			applicationDAOUpdateExpectedCalls: 0,
			offerDAOExpectedCalls:             0,
			appianClientExpectedCalls:         0,
		},
		{
			name: "test 6, happy path with application type AT_BIZ_OA_LENDING and valid rejection reason",
			data: &applicationStatusTransistionSchema.ApplicationStatusTransition{
				StreamInfo:      streaminfo.StreamInfo{},
				CustomerID:      "TEST-ID",
				FromStatus:      "PENDING_USER_ACCEPTANCE",
				ToStatus:        "AS_REJECTED",
				ApplicationType: "AT_BIZ_OA_LENDING",
				ApplicationID:   "000",
				RejectionReason: "AS_HIGH_RISKSCORE",
				Metadata:        nil,
				EventTime:       time.Time{},
			},
			applicationDAOFindExpectedCalls:   0,
			applicationDAOUpdateExpectedCalls: 0,
			offerDAOExpectedCalls:             0,
			appianClientExpectedCalls:         0,
		},
		{
			name: "test 6, happy path with application type AT_BIZ_OA_LENDING and valid rejection reason for GXB",
			data: &applicationStatusTransistionSchema.ApplicationStatusTransition{
				StreamInfo:      streaminfo.StreamInfo{},
				CustomerID:      "TEST-ID",
				FromStatus:      "PENDING_USER_ACCEPTANCE",
				ToStatus:        "AS_REJECTED",
				ApplicationType: "AT_BIZ_NTB_ONBOARDING_DBMY",
				ApplicationID:   "000",
				RejectionReason: "ECDD_FAILED",
				Metadata:        nil,
				EventTime:       time.Time{},
			},
			applicationDAOFindExpectedCalls:   0,
			applicationDAOUpdateExpectedCalls: 0,
			offerDAOExpectedCalls:             0,
			appianClientExpectedCalls:         0,
		},
		{
			name: "test 7, happy path with application type AT_BIZ_OA_LENDING and invalid rejection reason",
			data: &applicationStatusTransistionSchema.ApplicationStatusTransition{
				StreamInfo:      streaminfo.StreamInfo{},
				CustomerID:      "TEST-ID",
				FromStatus:      "PENDING_USER_ACCEPTANCE",
				ToStatus:        "AS_REJECTED",
				ApplicationType: "AT_BIZ_OA_LENDING",
				ApplicationID:   "000",
				RejectionReason: "some invalid reason",
				Metadata:        nil,
				EventTime:       time.Time{},
			},
			applicationDAOFindExpectedCalls:   0,
			applicationDAOUpdateExpectedCalls: 0,
			offerDAOExpectedCalls:             0,
			appianClientExpectedCalls:         0,
		},
		{
			name: "test 7, happy path with application type AT_BIZ_OA_LENDING and invalid rejection reason for GXB",
			data: &applicationStatusTransistionSchema.ApplicationStatusTransition{
				StreamInfo:      streaminfo.StreamInfo{},
				CustomerID:      "TEST-ID",
				FromStatus:      "PENDING_USER_ACCEPTANCE",
				ToStatus:        "AS_REJECTED",
				ApplicationType: "AT_BIZ_NTB_ONBOARDING_DBMY",
				ApplicationID:   "000",
				RejectionReason: "some invalid reason",
				Metadata:        nil,
				EventTime:       time.Time{},
			},
			applicationDAOFindExpectedCalls:   0,
			applicationDAOUpdateExpectedCalls: 0,
			offerDAOExpectedCalls:             0,
			appianClientExpectedCalls:         0,
		},
		{
			name: "test 8, happy path with application type AT_BIZ_OA_LENDING and onboarding status is application expired",
			data: &applicationStatusTransistionSchema.ApplicationStatusTransition{
				StreamInfo:      streaminfo.StreamInfo{},
				CustomerID:      "TEST-ID",
				FromStatus:      "PENDING_USER_ACCEPTANCE",
				ToStatus:        "AS_EXPIRED",
				ApplicationType: "AT_BIZ_OA_LENDING",
				ApplicationID:   "000",
				Metadata:        nil,
				EventTime:       time.Time{},
			},
			applicationDAOFindExpectedCalls:   0,
			applicationDAOUpdateExpectedCalls: 0,
			offerDAOExpectedCalls:             0,
			appianClientExpectedCalls:         0,
		},
		{
			name: "test 9, happy path with application type AT_BIZ_OA_LENDING and onboarding status is application expired for GXB",
			data: &applicationStatusTransistionSchema.ApplicationStatusTransition{
				StreamInfo:      streaminfo.StreamInfo{},
				CustomerID:      "TEST-ID",
				FromStatus:      "PENDING_USER_ACCEPTANCE",
				ToStatus:        "AS_EXPIRED",
				ApplicationType: "AT_BIZ_NTB_ONBOARDING_DBMY",
				ApplicationID:   "000",
				Metadata:        nil,
				EventTime:       time.Time{},
			},
			applicationDAOFindExpectedCalls:   0,
			applicationDAOUpdateExpectedCalls: 0,
			offerDAOExpectedCalls:             0,
			appianClientExpectedCalls:         0,
		},
	}
	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			mockApplicationDao := &storage.MockIApplicationDAO{}
			mockApplicationDao.On("Find", mock.Anything, mock.Anything, mock.Anything).
				Return(resources.SampleApplicationData(), nil)
			mockApplicationDao.On("Update", mock.Anything, mock.Anything).Return(nil)
			storage.ApplicationDao = mockApplicationDao

			mockOfferDao := &storage.MockIOfferDAO{}
			mockOfferDao.On("Find", mock.Anything, mock.Anything).
				Return(resources.SampleOfferData("PENDING_ACCEPTANCE", time.Now()), nil)
			mockOfferDao.On("Update", mock.Anything, mock.Anything).Return(nil)
			storage.OfferDao = mockOfferDao

			mockRedis := &redisMock.Client{}
			cache.RedisClient = mockRedis
			mockRedis.On("GetBytes", mock.Anything, mock.Anything).Return(nil, nil)
			mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)

			mockAppianClient := &mocks.Appian{}
			mockAppianClient.On("GetAccessToken", mock.Anything, mock.Anything).
				Return(&dto.GetAccessTokenResponse{AccessToken: "Token"}, nil)
			mockAppianClient.On("UpdateOfferDetails", mock.Anything, mock.Anything).
				Return(&dto.UpdateOfferDetailsResponse{}, nil)

			appConfig := &config.AppConfig{
				FeatureFlags: config.FeatureFlags{AppianFlag: true},
				ApplicationStatusTransitionKafkaConfig: &config.KafkaConfig{
					KafkaConfig: &sndconfig.KafkaConfig{},
					Enable:      true,
				},
			}

			a := &ApplicationStatusTransitionImpl{
				Config:       appConfig,
				AppianClient: mockAppianClient,
			}
			a.HandleApplicationStatusTransitionEvent(context.Background(), tt.data)
			mockApplicationDao.AssertNumberOfCalls(t, "Find", tt.applicationDAOFindExpectedCalls)
			mockApplicationDao.AssertNumberOfCalls(t, "Update", tt.applicationDAOUpdateExpectedCalls)

			mockOfferDao.AssertNumberOfCalls(t, "Find", tt.offerDAOExpectedCalls)
			mockOfferDao.AssertNumberOfCalls(t, "Update", tt.offerDAOExpectedCalls)

			mockAppianClient.AssertNumberOfCalls(t, "GetAccessToken", tt.appianClientExpectedCalls)
			mockAppianClient.AssertNumberOfCalls(t, "UpdateOfferDetails", tt.appianClientExpectedCalls)
		})
	}
}
