package handlers

import (
	"context"
	"fmt"
	"net/http"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/offers"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateOrUpdatePreSelectOfferDetails : API to create or  update pre-selected offer details
func (l *LoanAppService) CreateOrUpdatePreSelectOfferDetails(ctx context.Context, req *api.CreateOrUpdatePreSelectOfferDetailsRequest) (*api.CreateOrUpdatePreSelectOfferDetailsResponse, error) {
	slog.FromContext(ctx).Info(constants.CreateOrUpdatePreSelectOfferDetailsLogTag, fmt.Sprintf("Received Request, applicationID: %s", req.RefApplicationID), utils.GetTraceID(ctx))
	if errs := offers.ValidateCreateOrUpdatePreSelectOfferDetailsRequest(ctx, req); len(errs) != 0 {
		return nil, commonErr.BuildErrorResponseWithErrorDetail(
			http.StatusBadRequest, commonErr.ErrInvalidUpdateLoanOfferDetailsRequest.Code,
			commonErr.ErrInvalidUpdateLoanOfferDetailsRequest.Message, errs)
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(constants.RefApplicationID, req.RefApplicationID))
	resp, err := l.OfferDetailsClient.CreateOrUpdatePreSelectOfferDetails(ctx, req)
	if err != nil {
		return nil, err
	}

	slog.FromContext(ctx).Info(constants.CreateOrUpdatePreSelectOfferDetailsLogTag, fmt.Sprintf("Successfully updated pre-selected offer details: %s", req.RefApplicationID))
	return resp, nil
}
