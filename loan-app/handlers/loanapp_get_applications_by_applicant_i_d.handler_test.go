package handlers

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/requests"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/resources"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/responses"
	"gitlab.myteksi.net/dakota/klient/errorhandling"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	productMasterAPI "gitlab.myteksi.net/dakota/lending/external/corebanking/productmaster"
	"gitlab.myteksi.net/dakota/lending/external/corebanking/productmaster/mocks"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetApplicationByApplicantID(t *testing.T) {
	const appian = "Appian"
	applicantID := uuid.NewString()
	onboardingApplicationID := uuid.NewString()
	onboardingApplicationID2 := uuid.NewString()

	loanApplicationID := "********-f700-36bc-1aaa-24282b3bd4e"
	cardApplicationID := uuid.NewString()
	timeNowStringInRequiredFormat := time.Now().Format(time.RFC3339)
	timeNow, _ := time.ParseInLocation(time.RFC3339, timeNowStringInRequiredFormat, time.FixedZone("UTC", int(constants.DefaultTimeZoneOffset)))
	defaultLoanApplication := resources.ConstructApplication(loanApplicationID, onboardingApplicationID, "ACTIVE",
		"LOAN_APPLICATION_CREATED",
		"FLEXI_LOAN_LINE_OF_CREDIT",
		"DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT")
	defaultLoanApplication.CreatedAt = timeNow
	defaultLoanApplication.UpdatedAt = timeNow
	defaultLoanApplication.CreatedBy = appian
	defaultLoanApplication.UpdatedBy = appian

	defaultCardApplication := resources.ConstructApplication(cardApplicationID, onboardingApplicationID, "ACTIVE",
		"CARD_APPLICATION_CREATED",
		"FLEXI_CREDIT_CARD",
		"DEFAULT_FLEXI_CREDIT_CARD")
	defaultCardApplication.CreatedAt = timeNow
	defaultCardApplication.UpdatedAt = timeNow
	defaultCardApplication.CreatedBy = appian
	defaultCardApplication.UpdatedBy = appian

	defaultCardApplicationForSeparateApplication := resources.ConstructApplication(cardApplicationID, onboardingApplicationID2, "ACTIVE",
		"CARD_APPLICATION_CREATED",
		"FLEXI_CREDIT_CARD",
		"DEFAULT_FLEXI_CREDIT_CARD")
	defaultCardApplicationForSeparateApplication.CreatedAt = timeNow
	defaultCardApplicationForSeparateApplication.UpdatedAt = timeNow
	defaultCardApplicationForSeparateApplication.CreatedBy = appian
	defaultCardApplicationForSeparateApplication.UpdatedBy = appian

	defaultOfferData := resources.SampleOfferData(string(api.LoanOfferStatus_ACCEPTED), timeNow)
	offerData := defaultOfferData[0]
	defaultCardOfferData := resources.SampleOfferData(string(api.LoanOfferStatus_ACCEPTED), timeNow)
	defaultCardOfferData[0].ApplicationID = cardApplicationID

	acceptedCreditFloat, _ := offerData.AcceptedCredit.Mul(decimal.New(100, 0)).Float64()
	offeredCreditFloat, _ := offerData.OfferedCredit.Mul(decimal.New(100, 0)).Float64()

	mappedProductParam := make(map[string]string)
	mappedProductParam[constants.MinCreditLimitAmount] = "500"

	scenarios := []struct {
		desc               string
		req                *api.GetApplicationsByApplicantIDRequest
		applicantID        string
		applicantData      []*storage.Applicant
		applicantDBError   error
		applicationData    []*storage.Application
		applicationDBError error
		offerData          []*storage.Offer
		offerDBError       error
		productData        *productMasterAPI.ListEffectiveProductVariantParametersResponse
		productDBError     error
		expectedRes        *api.GetApplicationsByApplicantIDResponse
		expectedErr        error
	}{
		{
			desc:        "missing fields validation error",
			req:         &api.GetApplicationsByApplicantIDRequest{},
			expectedErr: responses.CreateGetApplicationByApplicantIDMissingFieldResponse(),
		},
		{
			desc:             "Applicant DB failure",
			applicantID:      applicantID,
			applicantDBError: commonErr.BuildErrorResponse(http.StatusInternalServerError, commonErr.ErrDatabaseOperationFailure.Code, commonErr.ErrDatabaseOperationFailure.Message),
			expectedErr:      commonErr.BuildErrorResponse(http.StatusInternalServerError, commonErr.ErrDatabaseOperationFailure.Code, commonErr.ErrDatabaseOperationFailure.Message),
		},
		{
			desc:             "Failure applicant not found",
			applicantID:      applicantID,
			applicantData:    nil,
			applicantDBError: data.ErrNoData,
			expectedErr:      commonErr.BuildErrorResponse(http.StatusNotFound, commonErr.ErrApplicantNotAssociated.Code, commonErr.ErrApplicantNotAssociated.Message),
		},
		{
			desc:        "Application DB failure",
			applicantID: applicantID,
			applicantData: []*storage.Applicant{
				resources.ConstructLoanApplicant(101, applicantID, loanApplicationID),
			},
			expectedErr:        commonErr.BuildErrorResponse(http.StatusInternalServerError, commonErr.ErrDatabaseOperationFailure.Code, commonErr.ErrDatabaseOperationFailure.Message),
			applicationDBError: commonErr.BuildErrorResponse(http.StatusInternalServerError, commonErr.ErrDatabaseOperationFailure.Code, commonErr.ErrDatabaseOperationFailure.Message),
		},
		{
			desc:        "Failure application not found",
			applicantID: applicantID,
			applicantData: []*storage.Applicant{
				resources.ConstructLoanApplicant(101, applicantID, loanApplicationID),
			},
			applicationDBError: data.ErrNoData,
			expectedErr:        commonErr.BuildErrorResponse(http.StatusNotFound, commonErr.ErrApplicationNotFound.Code, commonErr.ErrApplicationNotFound.Message),
		},
		{
			desc:        "Offer DB Failure",
			applicantID: applicantID,
			applicantData: []*storage.Applicant{
				resources.ConstructLoanApplicant(101, applicantID, loanApplicationID),
			},
			applicationData: []*storage.Application{
				resources.ConstructApplication(loanApplicationID, onboardingApplicationID, "EXPIRED",
					"LOAN_APPLICATION_CREATED",
					"FLEXI_LOAN_LINE_OF_CREDIT",
					"DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"),
			},
			offerDBError: commonErr.BuildErrorResponse(http.StatusInternalServerError, commonErr.ErrDatabaseOperationFailure.Code, commonErr.ErrDatabaseOperationFailure.Message),
			expectedErr:  commonErr.BuildErrorResponse(http.StatusInternalServerError, commonErr.ErrDatabaseOperationFailure.Code, commonErr.ErrDatabaseOperationFailure.Message),
		},
		{
			desc:        "Failure Offer Not Found",
			applicantID: applicantID,
			applicantData: []*storage.Applicant{
				resources.ConstructLoanApplicant(101, applicantID, loanApplicationID),
			},
			applicationData: []*storage.Application{
				resources.ConstructApplication(loanApplicationID, onboardingApplicationID, "EXPIRED",
					"LOAN_APPLICATION_CREATED",
					"FLEXI_LOAN_LINE_OF_CREDIT",
					"DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"),
			},
			offerDBError: data.ErrNoData,
			expectedErr:  commonErr.BuildErrorResponse(http.StatusNotFound, commonErr.ErrOfferDetailsNotFound.Code, commonErr.ErrOfferDetailsNotFound.Message),
		},
		{
			desc:        "ProductVariantParameters DB Failure Product Not Found",
			applicantID: applicantID,
			applicantData: []*storage.Applicant{
				resources.ConstructLoanApplicant(101, applicantID, loanApplicationID),
			},
			applicationData: []*storage.Application{
				resources.ConstructApplication(loanApplicationID, onboardingApplicationID, "EXPIRED",
					"LOAN_APPLICATION_CREATED",
					"FLEXI_LOAN_LINE_OF_CREDIT",
					"DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"),
			},
			offerData: resources.SampleOfferData(string(api.LoanOfferStatus_ACCEPTED), timeNow),
			productDBError: &errorhandling.Error{
				Code: constants.ProductNotFoundErrorCode,
			},
			expectedErr: commonErr.BuildErrorResponse(http.StatusNotFound, commonErr.ErrProductVariantCodeNotFound.Code, commonErr.ErrProductVariantCodeNotFound.Message),
		},
		{
			desc:        "ProductVariantParameters DB Failure",
			applicantID: applicantID,
			applicantData: []*storage.Applicant{
				resources.ConstructLoanApplicant(101, applicantID, loanApplicationID),
			},
			applicationData: []*storage.Application{
				resources.ConstructApplication(loanApplicationID, onboardingApplicationID, "EXPIRED",
					"LOAN_APPLICATION_CREATED",
					"FLEXI_LOAN_LINE_OF_CREDIT",
					"DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"),
			},
			offerData: resources.SampleOfferData(string(api.LoanOfferStatus_ACCEPTED), timeNow),
			productDBError: &errorhandling.Error{
				Code: constants.Authorization,
			},
			expectedErr: commonErr.BuildErrorResponse(http.StatusInternalServerError, commonErr.ErrProductMasterConnectionFailure.Code, commonErr.ErrProductMasterConnectionFailure.Message),
		},
		{
			desc:        "Happy Case Single Application",
			applicantID: applicantID,
			applicantData: []*storage.Applicant{
				resources.ConstructLoanApplicant(101, applicantID, loanApplicationID),
			},
			applicationData: []*storage.Application{defaultLoanApplication},
			offerData:       []*storage.Offer{offerData},
			productData: &productMasterAPI.ListEffectiveProductVariantParametersResponse{
				ProductVariantParameters: []productMasterAPI.ProductVariantParameter{{
					ParameterKey:   constants.MinCreditLimitAmount,
					ParameterValue: "500",
				}},
			},
			expectedRes: &api.GetApplicationsByApplicantIDResponse{
				Applications: []api.ApplicationDetails{{
					OnboardingApplicationID: defaultLoanApplication.OnboardingApplicationID,
					DeviceID:                defaultLoanApplication.DeviceID,
					IpAddress:               defaultLoanApplication.IPAddress,
					ApplicationData: []map[string]interface{}{
						{
							"applicationID":         defaultLoanApplication.ApplicationID,
							"applicationType":       defaultLoanApplication.ApplicationType,
							"appCreationDate":       timeNowStringInRequiredFormat,
							"countryCode":           defaultLoanApplication.CountryCode,
							"channel":               defaultLoanApplication.Channel,
							"productType":           defaultLoanApplication.ProductCode,
							"subProductType":        defaultLoanApplication.ProductVariantCode,
							"requestedLoanAmount":   defaultLoanApplication.RequestedLoanCredit,
							"requestedCurrencyCode": defaultLoanApplication.CurrencyCode,
							"status":                defaultLoanApplication.Status,
							"statusReason":          defaultLoanApplication.StatusReason,
							"createdBy":             defaultLoanApplication.CreatedBy,
							"createdAt":             timeNowStringInRequiredFormat,
							"updatedBy":             defaultLoanApplication.UpdatedBy,
							"updatedAt":             timeNowStringInRequiredFormat,
							"offerDetails": map[string]interface{}{
								"offeredCredit": map[string]interface{}{
									"currencyCode": offerData.CurrencyCode,
									"val":          offeredCreditFloat,
								},
								"offeredMaxTenor":     float64(offerData.OfferedTenor),
								"offeredInterestRate": offerData.OfferedInterestRate.String(),
								"acceptedCredit": map[string]interface{}{
									"currencyCode": offerData.CurrencyCode,
									"val":          acceptedCreditFloat,
								},
							},
							"productParameters": map[string]interface{}{
								"minCreditLimitAmount": map[string]interface{}{
									"currencyCode": defaultLoanApplication.CurrencyCode,
									"val":          float64(50000),
								},
							},
						},
					},
				}},
			},
		},
		{
			desc:        "Happy Case Bundled Application",
			applicantID: applicantID,
			applicantData: []*storage.Applicant{
				resources.ConstructLoanApplicant(101, applicantID, loanApplicationID),
			},
			applicationData: []*storage.Application{defaultLoanApplication, defaultCardApplication},
			offerData:       []*storage.Offer{offerData, defaultCardOfferData[0]},
			productData: &productMasterAPI.ListEffectiveProductVariantParametersResponse{
				ProductVariantParameters: []productMasterAPI.ProductVariantParameter{{
					ParameterKey:   constants.MinCreditLimitAmount,
					ParameterValue: "500",
				}},
			},
			expectedRes: &api.GetApplicationsByApplicantIDResponse{
				Applications: []api.ApplicationDetails{{
					OnboardingApplicationID: defaultLoanApplication.OnboardingApplicationID,
					DeviceID:                defaultLoanApplication.DeviceID,
					IpAddress:               defaultLoanApplication.IPAddress,
					ApplicationData: []map[string]interface{}{
						{
							"applicationID":         defaultLoanApplication.ApplicationID,
							"applicationType":       defaultLoanApplication.ApplicationType,
							"appCreationDate":       timeNowStringInRequiredFormat,
							"countryCode":           defaultLoanApplication.CountryCode,
							"channel":               defaultLoanApplication.Channel,
							"productType":           defaultLoanApplication.ProductCode,
							"subProductType":        defaultLoanApplication.ProductVariantCode,
							"requestedLoanAmount":   defaultLoanApplication.RequestedLoanCredit,
							"requestedCurrencyCode": defaultLoanApplication.CurrencyCode,
							"status":                defaultLoanApplication.Status,
							"statusReason":          defaultLoanApplication.StatusReason,
							"createdBy":             defaultLoanApplication.CreatedBy,
							"createdAt":             timeNowStringInRequiredFormat,
							"updatedBy":             defaultLoanApplication.UpdatedBy,
							"updatedAt":             timeNowStringInRequiredFormat,
							"offerDetails": map[string]interface{}{
								"offeredCredit": map[string]interface{}{
									"currencyCode": offerData.CurrencyCode,
									"val":          offeredCreditFloat,
								},
								"offeredMaxTenor":     float64(offerData.OfferedTenor),
								"offeredInterestRate": offerData.OfferedInterestRate.String(),
								"acceptedCredit": map[string]interface{}{
									"currencyCode": offerData.CurrencyCode,
									"val":          acceptedCreditFloat,
								},
							},
							"productParameters": map[string]interface{}{
								"minCreditLimitAmount": map[string]interface{}{
									"currencyCode": defaultLoanApplication.CurrencyCode,
									"val":          float64(50000),
								},
							},
						},
						{
							"applicationID":         defaultCardApplication.ApplicationID,
							"applicationType":       defaultCardApplication.ApplicationType,
							"appCreationDate":       timeNowStringInRequiredFormat,
							"countryCode":           defaultCardApplication.CountryCode,
							"channel":               defaultCardApplication.Channel,
							"productType":           defaultCardApplication.ProductCode,
							"subProductType":        defaultCardApplication.ProductVariantCode,
							"requestedLoanAmount":   defaultCardApplication.RequestedLoanCredit,
							"requestedCurrencyCode": defaultCardApplication.CurrencyCode,
							"status":                defaultCardApplication.Status,
							"statusReason":          defaultCardApplication.StatusReason,
							"createdBy":             defaultCardApplication.CreatedBy,
							"createdAt":             timeNowStringInRequiredFormat,
							"updatedBy":             defaultCardApplication.UpdatedBy,
							"updatedAt":             timeNowStringInRequiredFormat,
							"offerDetails": map[string]interface{}{
								"offeredCredit": map[string]interface{}{
									"currencyCode": offerData.CurrencyCode,
									"val":          offeredCreditFloat,
								},
								"offeredMaxTenor":     float64(offerData.OfferedTenor),
								"offeredInterestRate": offerData.OfferedInterestRate.String(),
								"acceptedCredit": map[string]interface{}{
									"currencyCode": offerData.CurrencyCode,
									"val":          acceptedCreditFloat,
								},
							},
							"productParameters": map[string]interface{}{
								"minCreditLimitAmount": map[string]interface{}{
									"currencyCode": defaultCardApplication.CurrencyCode,
									"val":          float64(50000),
								},
							},
						}},
				}},
			},
		},
		{
			desc:        "Happy Case separate application for loan and credit-card",
			applicantID: applicantID,
			applicantData: []*storage.Applicant{
				resources.ConstructLoanApplicant(101, applicantID, loanApplicationID),
			},
			applicationData: []*storage.Application{defaultLoanApplication, defaultCardApplicationForSeparateApplication},
			offerData:       []*storage.Offer{offerData, defaultCardOfferData[0]},
			productData: &productMasterAPI.ListEffectiveProductVariantParametersResponse{
				ProductVariantParameters: []productMasterAPI.ProductVariantParameter{{
					ParameterKey:   constants.MinCreditLimitAmount,
					ParameterValue: "500",
				}},
			},
			expectedRes: &api.GetApplicationsByApplicantIDResponse{
				Applications: []api.ApplicationDetails{
					{
						OnboardingApplicationID: defaultLoanApplication.OnboardingApplicationID,
						DeviceID:                defaultLoanApplication.DeviceID,
						IpAddress:               defaultLoanApplication.IPAddress,
						ApplicationData: []map[string]interface{}{
							{
								"applicationID":         defaultLoanApplication.ApplicationID,
								"applicationType":       defaultLoanApplication.ApplicationType,
								"appCreationDate":       timeNowStringInRequiredFormat,
								"countryCode":           defaultLoanApplication.CountryCode,
								"channel":               defaultLoanApplication.Channel,
								"productType":           defaultLoanApplication.ProductCode,
								"subProductType":        defaultLoanApplication.ProductVariantCode,
								"requestedLoanAmount":   defaultLoanApplication.RequestedLoanCredit,
								"requestedCurrencyCode": defaultLoanApplication.CurrencyCode,
								"status":                defaultLoanApplication.Status,
								"statusReason":          defaultLoanApplication.StatusReason,
								"createdBy":             defaultLoanApplication.CreatedBy,
								"createdAt":             timeNowStringInRequiredFormat,
								"updatedBy":             defaultLoanApplication.UpdatedBy,
								"updatedAt":             timeNowStringInRequiredFormat,
								"offerDetails": map[string]interface{}{
									"offeredCredit": map[string]interface{}{
										"currencyCode": offerData.CurrencyCode,
										"val":          offeredCreditFloat,
									},
									"offeredMaxTenor":     float64(offerData.OfferedTenor),
									"offeredInterestRate": offerData.OfferedInterestRate.String(),
									"acceptedCredit": map[string]interface{}{
										"currencyCode": offerData.CurrencyCode,
										"val":          acceptedCreditFloat,
									},
								},
								"productParameters": map[string]interface{}{
									"minCreditLimitAmount": map[string]interface{}{
										"currencyCode": defaultLoanApplication.CurrencyCode,
										"val":          float64(50000),
									},
								},
							}},
					},
					{
						OnboardingApplicationID: defaultCardApplicationForSeparateApplication.OnboardingApplicationID,
						DeviceID:                defaultCardApplication.DeviceID,
						IpAddress:               defaultCardApplication.IPAddress,
						ApplicationData: []map[string]interface{}{
							{
								"applicationID":         defaultCardApplication.ApplicationID,
								"applicationType":       defaultCardApplication.ApplicationType,
								"appCreationDate":       timeNowStringInRequiredFormat,
								"countryCode":           defaultCardApplication.CountryCode,
								"channel":               defaultCardApplication.Channel,
								"productType":           defaultCardApplication.ProductCode,
								"subProductType":        defaultCardApplication.ProductVariantCode,
								"requestedLoanAmount":   defaultCardApplication.RequestedLoanCredit,
								"requestedCurrencyCode": defaultCardApplication.CurrencyCode,
								"status":                defaultCardApplication.Status,
								"statusReason":          defaultCardApplication.StatusReason,
								"createdBy":             defaultCardApplication.CreatedBy,
								"createdAt":             timeNowStringInRequiredFormat,
								"updatedBy":             defaultCardApplication.UpdatedBy,
								"updatedAt":             timeNowStringInRequiredFormat,
								"offerDetails": map[string]interface{}{
									"offeredCredit": map[string]interface{}{
										"currencyCode": offerData.CurrencyCode,
										"val":          offeredCreditFloat,
									},
									"offeredMaxTenor":     float64(offerData.OfferedTenor),
									"offeredInterestRate": offerData.OfferedInterestRate.String(),
									"acceptedCredit": map[string]interface{}{
										"currencyCode": offerData.CurrencyCode,
										"val":          acceptedCreditFloat,
									},
								},
								"productParameters": map[string]interface{}{
									"minCreditLimitAmount": map[string]interface{}{
										"currencyCode": defaultCardApplication.CurrencyCode,
										"val":          float64(50000),
									},
								},
							},
						},
					},
				},
			},
		},
	}
	for _, scenario := range scenarios {
		testcase := scenario
		t.Run(testcase.desc, func(t *testing.T) {
			req := requests.ConstructGetApplicationsByApplicantIDRequest(testcase.applicantID)
			mockApplicantDao := &storage.MockIApplicantDAO{}
			mockApplicantDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(testcase.applicantData, testcase.applicantDBError)
			storage.ApplicantDao = mockApplicantDao
			mockApplicationDao := &storage.MockIApplicationDAO{}
			mockApplicationDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(testcase.applicationData, testcase.applicationDBError)
			storage.ApplicationDao = mockApplicationDao
			mockOfferDAO := &storage.MockIOfferDAO{}
			mockOfferDAO.On("Find", mock.Anything, mock.Anything).Return(testcase.offerData, testcase.offerDBError)
			storage.OfferDao = mockOfferDAO
			productMasterMock := mocks.ProductMaster{}
			productMasterMock.On("ListEffectiveProductVariantParameters", mock.Anything, mock.Anything).Return(testcase.productData, testcase.productDBError)

			l := &LoanAppService{ProductMasterClient: &productMasterMock}
			res, err := l.GetApplicationsByApplicantID(context.Background(), req)
			if testcase.expectedErr != nil {
				assert.Equal(t, testcase.expectedErr, err, testcase.desc)
			} else {
				for _, application := range res.Applications {
					expectedRes := findApplication(testcase.expectedRes, application.OnboardingApplicationID)
					assert.Equal(t, *expectedRes, application, testcase.desc)
				}
			}
		})
	}
}

func findApplication(res *api.GetApplicationsByApplicantIDResponse, onboardingApplicationID string) *api.ApplicationDetails {
	for _, application := range res.Applications {
		if application.OnboardingApplicationID == onboardingApplicationID {
			return &application
		}
	}

	return nil
}
