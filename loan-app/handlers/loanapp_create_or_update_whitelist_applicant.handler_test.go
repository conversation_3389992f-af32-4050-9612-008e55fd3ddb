package handlers //nolint:dupl
import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	handlerMock "gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans/mocks"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.myteksi.net/dakota/lending/common/countries"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestLoanAppService_CreateOrUpdateWhitelistApplicant(t *testing.T) {
	tests := []struct {
		name        string
		req         *api.CreateOrUpdateWhitelistApplicantRequest
		err         error
		expectedRes *api.CreateOrUpdateWhitelistApplicantResponse
		expectedErr error
	}{
		{
			name: "should return success response when create or update whitelist applicant is a success",
			req: &api.CreateOrUpdateWhitelistApplicantRequest{
				WhitelistedUserDetails: []api.WhitelistedUserDetails{
					{Key: "CIFNumber",
						Value:       "123456",
						Whitelisted: true,
						Channel:     "appian",
					},
				},
				UpdatedBy: "appian",
			},
			expectedRes: &api.CreateOrUpdateWhitelistApplicantResponse{
				WhitelistedUserDetails: []api.WhitelistedUserDetailsResponse{
					{Key: "CIFNumber",
						Value:       "123456",
						Whitelisted: true,
						Channel:     "appian",
						Status:      "WHITELISTED",
					},
				},
			},
		},
		{
			name: "should return error response when create or update whitelist applicant is a error",
			req: &api.CreateOrUpdateWhitelistApplicantRequest{
				WhitelistedUserDetails: []api.WhitelistedUserDetails{},
				UpdatedBy:              "appian",
			},
			expectedRes: nil,
			expectedErr: servus.ServiceError{
				HTTPCode: 400,
				Code:     "1525",
				Message:  "Invalid request parameters",
				Errors: []servus.ErrorDetail{{
					ErrorCode: "FIELD_MISSING",
					Message:   "Missing WhitelistedUser field",
					Path:      "whitelistedUserDetails",
				}},
			},
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			mockFlexiTermLoanApplication := &handlerMock.FlexiTermLoanApplication{}
			l := &LoanAppService{
				FlexiTermLoanApplication: mockFlexiTermLoanApplication,
				AppConfig: &config.AppConfig{
					LocaleConfig: config.LocaleConfig{
						DefaultCountryCode: "ID",
					},
				},
			}
			constants.DefaultCountryCode = countries.ID
			mockFlexiTermLoanApplication.On("CreateOrUpdateWhitelistApplicantStatus", mock.Anything, mock.Anything).Return(test.expectedRes, test.expectedErr)
			resp, err := l.CreateOrUpdateWhitelistApplicant(context.Background(), test.req)
			if test.expectedErr != nil {
				assert.Error(t, err, test.name)
				assert.Equal(t, test.expectedErr, err, test.name)
			} else {
				assert.NoError(t, err, test.name)
				assert.NotNil(t, resp, test.name)
				assert.Equal(t, test.expectedRes, resp)
			}
		})
	}
}
