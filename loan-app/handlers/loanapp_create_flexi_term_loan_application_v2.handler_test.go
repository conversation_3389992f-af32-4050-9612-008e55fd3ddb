package handlers

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans/factory"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/superbank"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/requests"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/resources"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestLoanAppService_CreateFlexiTermLoanApplicationV2(t *testing.T) {
	randomID := uuid.NewString()
	tests := []struct {
		name               string
		req                *api.CreateFlexiTermLoanApplicationRequest
		mockWorkflowRes    *superbank.ExecutionData
		mockWorkflowErr    error
		expectedRes        *api.CreateFlexiTermLoanApplicationResponseV2
		expectedErr        error
		applicationDaoData []*storage.Application
		applicationDaoErr  error
		applicantDaoData   []*storage.Applicant
		applicantDaoErr    error
	}{
		{
			name:               "should return success response when create application is a success",
			req:                requests.SampleCreateFlexiTermLoanApplication(),
			mockWorkflowRes:    &superbank.ExecutionData{ApplicationID: randomID},
			expectedRes:        &api.CreateFlexiTermLoanApplicationResponseV2{ApplicationID: randomID},
			applicationDaoData: resources.SampleStorageApplicationData(),
		},
		{
			name: "should return validation error when fields are missing",
			req:  requests.SampleCreateFlexiTermLoanApplicationWithoutApplicantType(),
			expectedErr: servus.ServiceError{
				HTTPCode: 400,
				Code:     "FIELD_INVALID",
				Message:  "Invalid enquiry request",
				Errors: []servus.ErrorDetail{{
					ErrorCode: "FIELD_MISSING",
					Message:   "Missing applicationType field",
				}},
			},
		},
		{
			name: "should return db failure error when the db is down",
			req:  requests.SampleCreateFlexiTermLoanApplication(),
			mockWorkflowErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "2201",
				Message:  "Failed to save data in database",
				Errors:   nil,
			},
			expectedErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "2201",
				Message:  "Failed to save data in database",
				Errors:   nil,
			},
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			mockIApplicationDAO := storage.MockIApplicationDAO{}
			mockIApplicationDAO.On("Find", mock.Anything, mock.Anything).Return(tt.applicationDaoData, tt.applicationDaoErr)
			storage.ApplicationDao = &mockIApplicationDAO
			mockIApplicantDAO := storage.MockIApplicantDAO{}
			mockIApplicantDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(tt.applicantDaoData, tt.applicantDaoErr)
			storage.ApplicantDao = &mockIApplicantDAO

			mockFlexiTermLoanApplicationWorkflow := &flexitermloanapplication.MockWorkflow{}
			l := &LoanAppService{
				FlexiTermLoanApplicationWorkflow: mockFlexiTermLoanApplicationWorkflow,
				AppConfig: &config.AppConfig{
					FeatureFlags: config.FeatureFlags{
						BusinessValidationFlag: true,
					},
					LocaleConfig: config.LocaleConfig{
						DefaultCountryCode: "ID",
					},
				},
			}

			mockFlexiTermLoanApplicationWorkflow.On("ExecuteCreateFlexiTermLoanApplicationWorkflow", mock.Anything, mock.Anything).Return(test.mockWorkflowRes, test.mockWorkflowErr)
			mockFlexiTermLoanApplicationWorkflow.On("GetLogicAnalyser").Return(factory.NewLogicAnalyserFactory("ID")())
			mockFlexiTermLoanApplicationWorkflow.On("GetApplicationID", mock.Anything).Return(randomID)

			resp, err := l.CreateFlexiTermLoanApplicationV2(context.Background(), test.req)
			if test.expectedErr != nil {
				assert.Error(t, err, test.name)
				assert.Equal(t, test.expectedErr, err, test.name)
			} else {
				assert.NoError(t, err, test.name)
				assert.NotNil(t, resp, test.name)
				assert.Equal(t, test.expectedRes, resp)
			}
		})
	}
}
