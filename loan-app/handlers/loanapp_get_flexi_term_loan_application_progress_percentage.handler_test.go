package handlers //nolint:dupl
import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	handlerMock "gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans/mocks"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestLoanAppService_GetFlexiTermLoanApplicationProgressPercentage(t *testing.T) {
	tests := []struct {
		name        string
		req         *api.GetFlexiTermLoanApplicationProgressPercentageRequest
		err         error
		expectedRes *api.GetFlexiTermLoanApplicationProgressPercentageResponse
		expectedErr error
	}{
		{
			name: "should return success response for get fexi-term loan application progress percentage",
			req: &api.GetFlexiTermLoanApplicationProgressPercentageRequest{
				OnboardingApplicationID: "ID1234",
				ProductVariantCode:      string(api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
			},
			expectedRes: &api.GetFlexiTermLoanApplicationProgressPercentageResponse{
				Status:                     "PROCESSING",
				ApplicationProgressPercent: 20,
			},
		},
		{
			name: "should return error response for get fexi-term loan application progress percentage",
			req: &api.GetFlexiTermLoanApplicationProgressPercentageRequest{
				ProductVariantCode: string(api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
			},
			expectedRes: nil,
			expectedErr: servus.ServiceError{
				HTTPCode: 400,
				Code:     "1519",
				Message:  "Invalid request parameters",
				Errors: []servus.ErrorDetail{{
					ErrorCode: "FIELD_MISSING",
					Message:   "Missing onboardingApplicationID field",
					Path:      "onboardingApplicationID",
				}},
			},
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			mockFlexiTermLoanApplication := &handlerMock.FlexiTermLoanApplication{}
			l := &LoanAppService{
				FlexiTermLoanApplication: mockFlexiTermLoanApplication,
				AppConfig: &config.AppConfig{
					LocaleConfig: config.LocaleConfig{
						DefaultCountryCode: "ID",
					},
				},
			}
			ctx := commonCtx.WithUserID(context.Background(), "Dummy")
			ctx = commonCtx.WithServiceID(ctx, "Dummy")
			mockFlexiTermLoanApplication.On("GetApplicationProgressPercentage", mock.Anything, mock.Anything).Return(test.expectedRes, test.expectedErr)
			resp, err := l.GetFlexiTermLoanApplicationProgressPercentage(ctx, test.req)
			if test.expectedErr != nil {
				assert.Error(t, err, test.name)
				assert.Equal(t, test.expectedErr, err, test.name)
			} else {
				assert.NoError(t, err, test.name)
				assert.NotNil(t, resp, test.name)
				assert.Equal(t, test.expectedRes, resp)
			}
		})
	}
}
