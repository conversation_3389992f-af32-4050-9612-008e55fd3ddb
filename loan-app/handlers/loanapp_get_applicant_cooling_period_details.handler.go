// Package handlers ...
// nolint:dupl
package handlers

import (
	context "context"
	"fmt"
	"net/http"

	api "gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/validations"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetApplicantCoolingPeriodDetails : API to get the applicant cooling period details
// nolint:dupl
func (l *LoanAppService) GetApplicantCoolingPeriodDetails(ctx context.Context, req *api.GetApplicantCoolingPeriodDetailsRequest) (*api.GetApplicantCoolingPeriodDetailsResponse, error) {
	slog.FromContext(ctx).Info(constants.GetApplicantCoolingPeriodDetailsLogTag, fmt.Sprintf("Received Request: %v", utils.ToJSON(req)), utils.GetTraceID(ctx))

	if errs := ValidateGetApplicantCoolingPeriodDetails(req); len(errs) != 0 {
		return nil, commonErr.BuildErrorResponseWithErrorDetail(
			http.StatusBadRequest, commonErr.ErrInvalidGetApplicantCoolingPeriodDetailsRequest.Code,
			commonErr.ErrInvalidGetApplicantCoolingPeriodDetailsRequest.Message, errs)
	}

	res, err := l.FlexiTermLoanApplication.GetApplicantCoolingPeriodDetails(ctx, req)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetApplicantCoolingPeriodDetailsLogTag, fmt.Sprintf("Successful Response: %v", utils.ToJSON(res)), utils.GetTraceID(ctx))
	return res, nil
}

// ValidateGetApplicantCoolingPeriodDetails ...
// nolint:dupl
func ValidateGetApplicantCoolingPeriodDetails(req *api.GetApplicantCoolingPeriodDetailsRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	errs = append(errs, validations.ValidateApplicantID(req.ApplicantID)...)
	errs = append(errs, validations.ValidateProductVariantCode(req.ProductVariantCode)...)
	return errs
}
