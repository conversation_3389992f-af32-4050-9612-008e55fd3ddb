package handlers

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	handlerMock "gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans/mocks"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/requests"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/resources"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/responses"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func TestLoanAppService_UpdateFlexiTermLoanApplication(t *testing.T) {
	tests := []struct {
		name             string
		req              *api.UpdateFlexiTermLoanApplicationRequest
		applicationDBErr error
		expectedRes      *api.UpdateFlexiTermLoanApplicationResponse
		idempotencyErr   error
		expectedErr      error
	}{
		{
			name:        "should return success response when update application is a success",
			req:         requests.SampleUpdateFlexiTermLoanApplicationProcessing(),
			expectedRes: responses.SampleUpdateFlexiTermLoanApplication(),
		},
		{
			name: "should return validation error when update application request has missing CBS/CDE Response",
			req:  requests.SampleUpdateFlexiTermLoanApplicationWithoutCDEAndCBSResponse(),
			expectedErr: servus.ServiceError{
				HTTPCode: 400,
				Code:     "FIELD_INVALID",
				Message:  "Invalid enquiry request",
				Errors: []servus.ErrorDetail{{
					ErrorCode: apiErr.ErrMissingCreditDecisionResponse.Code,
					Message:   apiErr.ErrMissingCreditDecisionResponse.Message,
				}, {
					ErrorCode: apiErr.ErrMissingCreditBureauResponse.Code,
					Message:   apiErr.ErrMissingCreditBureauResponse.Message,
				}},
			},
		},
		{
			name: "should return validation error when update application request has missing LimitCreationResponse",
			req:  requests.SampleUpdateFlexiTermLoanApplicationWithoutLimitCreationResponse(),
			expectedErr: servus.ServiceError{
				HTTPCode: 400,
				Code:     "FIELD_INVALID",
				Message:  "Invalid enquiry request",
				Errors: []servus.ErrorDetail{{
					ErrorCode: apiErr.ErrMissingLimitCreationResponse.Code,
					Message:   apiErr.ErrMissingLimitCreationResponse.Message,
				}},
			},
		},
		{
			name: "should return db failure error when the idempotency query errors out",
			req:  requests.SampleUpdateFlexiTermLoanApplicationProcessing(),
			idempotencyErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "2201",
				Message:  "Failed to save data in database",
				Errors:   nil,
			},
			expectedErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "2201",
				Message:  "Failed to save data in database",
				Errors:   nil,
			},
		},
		{
			name:             "should return db failure error when the application DB query errors out",
			req:              requests.SampleUpdateFlexiTermLoanApplicationProcessing(),
			applicationDBErr: data.ErrTimedOut,
			expectedErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "1808",
				Message:  "Database error",
			},
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			mockApplicationDAO := &storage.MockIApplicationDAO{}
			storage.ApplicationDao = mockApplicationDAO
			mockApplicationDAO.On("Find", mock.Anything, mock.Anything).
				Return(resources.SampleApplicationData(), test.applicationDBErr)
			mockFlexiTermLoanApplication := &handlerMock.FlexiTermLoanApplication{}
			l := &LoanAppService{
				FlexiTermLoanApplication: mockFlexiTermLoanApplication,
				AppConfig: &config.AppConfig{
					LocaleConfig: config.LocaleConfig{
						DefaultCountryCode: "SG",
					},
				},
			}
			mockFlexiTermLoanApplication.On("HandleIdempotentRequest", mock.Anything, mock.Anything).Return(test.expectedRes, test.idempotencyErr)
			resp, err := l.UpdateFlexiTermLoanApplication(context.Background(), test.req)
			if test.expectedErr != nil {
				assert.Error(t, err, test.name)
				assert.Equal(t, test.expectedErr, err, test.name)
			} else {
				test.expectedRes.Message.Application.ApplicationID = resp.Message.Application.ApplicationID
				assert.NoError(t, err, test.name)
				assert.NotNil(t, resp, test.name)
				assert.Equal(t, test.expectedRes, resp)
			}
		})
	}
}
