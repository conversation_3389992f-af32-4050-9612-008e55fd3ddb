package handlers

import (
	"context"
	"fmt"
	"net/http"

	api "gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	"gitlab.com/gx-regional/dakota/lending/loan-app/validation/request"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// UpdateFlexiTermLoanApplicationStatus : API to update flexi term loan application status and status reason
func (l *LoanAppService) UpdateFlexiTermLoanApplicationStatus(ctx context.Context, req *api.UpdateFlexiTermLoanApplicationStatusRequest) (*api.UpdateFlexiTermLoanApplicationStatusResponse, error) {
	validationErr, dbErr := l.validateUpdateApplicationStatus(req)
	if dbErr != nil {
		return nil, dbErr
	}
	if len(validationErr) != 0 {
		return nil, commonErr.BuildErrorResponseWithErrorDetail(
			http.StatusBadRequest, commonErr.ErrInvalidEnquiryRequest.Code,
			commonErr.ErrInvalidEnquiryRequest.Message, validationErr)
	}
	slog.FromContext(ctx).Info(constants.UpdateApplicationStatusLogTag, fmt.Sprintf("Received Request, applicationID: %s, status: %s, statusReason: %s", req.ApplicationID, req.Status, req.StatusReason), utils.GetTraceID(ctx))
	ctx = slog.AddTagsToContext(ctx,
		slog.CustomTag(constants.ReferenceIDTag, req.ReferenceID),
		slog.CustomTag(constants.ApplicationIDTag, req.ApplicationID),
	)
	response, err := l.FlexiTermLoanApplication.HandleIdempotentRequest(ctx, flexitermloans.IdempotencyParameters{
		Ctx:           ctx,
		Handler:       storage.IdempotentRequestHandlerUpdateFlexiTermLoanApplication,
		RequestID:     req.ReferenceID,
		RequestParams: req,
		Response:      &api.UpdateFlexiTermLoanApplicationStatusResponse{},
		Fn:            l.FlexiTermLoanApplication.UpdateLoanApplicationStatus,
	})
	if err != nil {
		return nil, err
	}
	res := response.(*api.UpdateFlexiTermLoanApplicationStatusResponse)
	slog.FromContext(ctx).Info(constants.UpdateApplicationStatusLogTag, fmt.Sprintf("Successfully completed response, applicationID: %s", req.ApplicationID), utils.GetTraceID(ctx))
	return res, err
}

func (l *LoanAppService) validateUpdateApplicationStatus(req *api.UpdateFlexiTermLoanApplicationStatusRequest) ([]servus.ErrorDetail, error) {
	validator := request.GetRequestValidator(constants.DefaultCountryCode)
	return validator.ValidateUpdateStatusReq(req)
}
