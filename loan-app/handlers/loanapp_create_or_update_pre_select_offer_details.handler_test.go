package handlers

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/offers"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestService_CreateOrUpdatePreSelectOfferDetails(t *testing.T) {
	tests := []struct {
		name        string
		req         *api.CreateOrUpdatePreSelectOfferDetailsRequest
		err         error
		expectedRes *api.CreateOrUpdatePreSelectOfferDetailsResponse
		expectedErr error
	}{
		{
			name: "Should return success response when Put pre select offer detail is success",
			req: &api.CreateOrUpdatePreSelectOfferDetailsRequest{
				RefApplicationID: "123",
				ProductType:      api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
				SubProductType:   api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
				IDType:           "CIF_NUM",
				IDNumber:         "idNumber",
				Status:           "ACTIVE",
				OfferData:        nil,
				Method:           "CASA_USER",
				ExpiryTime:       time.Now().Add(time.Hour),
			},
			expectedRes: &api.CreateOrUpdatePreSelectOfferDetailsResponse{
				StatusCode: string(constants.PreSelectOfferStatusCodeSuccess),
			},
		},
		{
			name: "Should return error response when validation failed",
			req: &api.CreateOrUpdatePreSelectOfferDetailsRequest{
				RefApplicationID: "123",
				ProductType:      api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
				SubProductType:   api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
				IDType:           "SAFE_ID",
				IDNumber:         "idNumber",
				Status:           "ACTIVE",
				OfferData:        nil,
				Method:           "CASA_USER",
				ExpiryTime:       time.Now().Add(time.Hour),
			},
			expectedRes: nil,
			expectedErr: servus.ServiceError{
				HTTPCode: 400,
				Code:     "1505",
				Message:  "Invalid request parameters",
				Errors: []servus.ErrorDetail{{
					ErrorCode: "FIELD_INVALID",
					Message:   "Invalid identifierType field",
					Path:      "",
				}},
			},
		},
	}

	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			mockOfferDetailsClient := &offers.MockOfferDetails{}
			mockOfferDetailsClient.On("CreateOrUpdatePreSelectOfferDetails", mock.Anything, mock.Anything).Return(test.expectedRes, nil)
			l := &LoanAppService{
				OfferDetailsClient: mockOfferDetailsClient,
			}
			resp, err := l.CreateOrUpdatePreSelectOfferDetails(context.Background(), test.req)
			if test.expectedErr != nil {
				assert.Error(t, err, test.name)
				assert.Equal(t, test.expectedErr, err, test.name)
			} else {
				assert.NoError(t, err, test.name)
				assert.NotNil(t, resp, test.name)
			}
		})
	}
}
