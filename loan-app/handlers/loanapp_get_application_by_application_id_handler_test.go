package handlers

import (
	"context"
	"net/http"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/requests"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/resources"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/responses"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	customerMasterAPI "gitlab.myteksi.net/dakota/lending/external/onboarding/customermaster"
	customerMasterMock "gitlab.myteksi.net/dakota/lending/external/onboarding/customermaster/mocks"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetApplicationByApplicationID(t *testing.T) {
	applicationID := uuid.NewString()
	cifNumber := uuid.NewString()
	onboardingApplicationID := uuid.NewString()
	scenarios := []struct {
		name               string
		req                *api.GetApplicationByApplicationIDRequest
		expectedResp       *api.GetApplicationByApplicationIDResponse
		expectedErr        error
		applicationData    []*storage.Application
		applicationDBError error
		customerMasterResp *customerMasterAPI.LookupCIFNumberResponse
		customerMasterErr  error
		applicantDBError   error
		applicantData      []*storage.Applicant
	}{
		{
			name:         "missing fields validation error",
			req:          &api.GetApplicationByApplicationIDRequest{},
			expectedResp: nil,
			expectedErr:  responses.CreateGetApplicationByApplicationIDMissingFieldResponse(),
		},
		{
			name: "error from customer master",
			req:  requests.ConstructGetApplicationByApplicationIDRequest(applicationID),
			applicationData: []*storage.Application{
				resources.ConstructApplication(applicationID, onboardingApplicationID, "EXPIRED",
					"LOAN_APPLICATION_EXPIRED",
					"FLEXI_LOAN_LINE_OF_CREDIT",
					"DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"),
			},
			customerMasterErr: commonErr.BuildErrorResponse(http.StatusInternalServerError, commonErr.ErrCustomerMasterConnectionFailure.Code, commonErr.ErrCustomerMasterConnectionFailure.Message),
			expectedErr:       commonErr.BuildErrorResponse(http.StatusInternalServerError, commonErr.ErrCustomerMasterConnectionFailure.Code, commonErr.ErrCustomerMasterConnectionFailure.Message),
		},
		{
			name: "application does not belong to logged in user",
			req:  requests.ConstructGetApplicationByApplicationIDRequest(applicationID),
			applicationData: []*storage.Application{
				resources.ConstructApplication(applicationID, onboardingApplicationID, "EXPIRED",
					"LOAN_APPLICATION_EXPIRED",
					"FLEXI_LOAN_LINE_OF_CREDIT",
					"DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"),
			},
			customerMasterResp: &customerMasterAPI.LookupCIFNumberResponse{CifNumber: cifNumber},
			applicantData:      []*storage.Applicant{},
			applicantDBError:   data.ErrNoData,
			expectedErr:        commonErr.BuildErrorResponse(http.StatusNotFound, commonErr.ErrApplicantNotAssociated.Code, commonErr.ErrApplicantNotAssociated.Message),
		},
		{
			name: "get application success",
			req:  requests.ConstructGetApplicationByApplicationIDRequest(applicationID),
			applicationData: []*storage.Application{
				resources.ConstructApplication(applicationID, onboardingApplicationID, "PENDING_ACCEPTANCE",
					"CDE_POST_BUREAU_APPROVED",
					"FLEXI_LOAN_LINE_OF_CREDIT",
					"DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"),
			},
			customerMasterResp: &customerMasterAPI.LookupCIFNumberResponse{CifNumber: cifNumber},
			applicantData:      []*storage.Applicant{{ApplicationID: applicationID, ApplicantID: cifNumber}},
			expectedErr:        nil,
		},
		{
			name: "get application success response Flexi Loan",
			req:  requests.ConstructGetApplicationByApplicationIDRequest(applicationID),
			applicationData: []*storage.Application{
				resources.ConstructApplication(applicationID, onboardingApplicationID, "PENDING_ACCEPTANCE",
					"CDE_POST_BUREAU_APPROVED",
					"FLEXI_LOAN_LINE_OF_CREDIT",
					"DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"),
			},
			customerMasterResp: &customerMasterAPI.LookupCIFNumberResponse{CifNumber: cifNumber},
			applicantData:      []*storage.Applicant{{ApplicationID: applicationID, ApplicantID: cifNumber}},
			expectedResp:       responses.ConstructGetApplicationByApplicationIDResponse(applicationID, "EXPIRED", "LOAN_APPLICATION_EXPIRED"),
		},
		{
			name: "get application success response Flexi Card",
			req:  requests.ConstructGetApplicationByApplicationIDRequest(applicationID),
			applicationData: []*storage.Application{
				resources.ConstructApplication(applicationID, onboardingApplicationID, "PENDING_ACCEPTANCE",
					"CDE_POST_BUREAU_APPROVED",
					"FLEXI_LOAN_LINE_OF_CREDIT",
					"DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"),
			},
			customerMasterResp: &customerMasterAPI.LookupCIFNumberResponse{CifNumber: cifNumber},
			applicantData:      []*storage.Applicant{{ApplicationID: applicationID, ApplicantID: cifNumber}},
			expectedResp:       responses.ConstructGetApplicationByApplicationIDResponse(applicationID, "PENDING_ACCEPTANCE", "CDE_POST_BUREAU_APPROVED"),
		},
	}
	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			mockApplicationDao := &storage.MockIApplicationDAO{}
			mockApplicationDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(scenario.applicationData, scenario.applicationDBError)
			storage.ApplicationDao = mockApplicationDao

			mockApplicantDao := &storage.MockIApplicantDAO{}
			mockApplicantDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(scenario.applicantData, scenario.applicantDBError)
			storage.ApplicantDao = mockApplicantDao

			mockCustomerMaster := &customerMasterMock.CustomerMaster{}
			mockCustomerMaster.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(&customerMasterAPI.LookupCIFNumberResponse{}, scenario.customerMasterErr).Once()
			l := &LoanAppService{
				CustomerMasterClient: mockCustomerMaster,
				AppConfig: &config.AppConfig{
					FeatureFlags: config.FeatureFlags{
						EnableActiveProfile: false,
					},
				},
			}
			resp, err := l.GetApplicationByApplicationID(context.Background(), scenario.req)
			if scenario.expectedErr != nil {
				assert.Equal(t, scenario.expectedErr, err, scenario.name)
			} else {
				assert.NotNil(t, resp, scenario.name)
			}
		})
	}
}
