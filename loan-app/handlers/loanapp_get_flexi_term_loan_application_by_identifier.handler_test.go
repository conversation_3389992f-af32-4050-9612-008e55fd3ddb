package handlers

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	handlerMock "gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans/mocks"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/requests"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/responses"
)

func Test_GetFlexiTermLoanApplicationByIdentifier(t *testing.T) {
	dummyErr := errors.New("dummy error")
	scenarios := []struct {
		name         string
		req          *api.GetFlexiTermLoanApplicationByIdentifierRequest
		expectedResp *api.GetFlexiTermLoanApplicationByIdentifierResponse
		expectedErr  error
	}{
		{
			name:         "missing fields validation error",
			req:          &api.GetFlexiTermLoanApplicationByIdentifierRequest{},
			expectedResp: nil,
			expectedErr:  responses.CreateFlexiTermLoanApplicationByIdentifierMissingFieldResponse(),
		},
		{
			name:         "invalid field value validation error",
			req:          &api.GetFlexiTermLoanApplicationByIdentifierRequest{IdentifierType: "INVALID_TYPE", IdentifierValue: "some NRIC"},
			expectedResp: nil,
			expectedErr:  responses.CreateFlexiTermLoanApplicationByIdentifierInvalidFieldResponse(),
		},
		{
			name:        "get flexi term loan application success",
			req:         requests.CreateFlexiTermLoanApplicationByIdentifierSampleRequest(),
			expectedErr: dummyErr,
		},
		{
			name: "get flexi term loan application success",
			req:  requests.CreateFlexiTermLoanApplicationByIdentifierSampleRequest(),
			expectedResp: &api.GetFlexiTermLoanApplicationByIdentifierResponse{
				LoanApplications: []api.LoanApplication{{
					Status: "PROCESSING",
				}},
			},
		},
	}
	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			mockFlexiTermLoanApplication := &handlerMock.FlexiTermLoanApplication{}
			l := &LoanAppService{
				FlexiTermLoanApplication: mockFlexiTermLoanApplication,
			}
			mockFlexiTermLoanApplication.On("GetLoanApplicationDetailsByIdentifier", mock.Anything, mock.Anything).Return(scenario.expectedResp, scenario.expectedErr)
			resp, err := l.GetFlexiTermLoanApplicationByIdentifier(context.Background(), scenario.req)
			if scenario.expectedErr != nil {
				assert.Error(t, err, scenario.name)
				assert.Equal(t, scenario.expectedErr, err, scenario.name)
			} else {
				assert.NoError(t, err, scenario.name)
				assert.NotNil(t, resp, scenario.name)
			}
		})
	}
}
