package handlers

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	handlerMock "gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans/mocks"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/requests"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/responses"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestLoanAppService_CreateFlexiTermLoanApplication(t *testing.T) {
	tests := []struct {
		name        string
		req         *api.CreateFlexiTermLoanApplicationRequest
		err         error
		expectedRes *api.CreateFlexiTermLoanApplicationResponse
		expectedErr error
	}{
		{
			name:        "should return success response when create application is a success",
			req:         requests.SampleCreateFlexiTermLoanApplication(),
			expectedRes: responses.SampleCreateFlexiTermLoanApplication(),
		},
		{
			name: "should return validation error when fields are missing",
			req:  requests.SampleCreateFlexiTermLoanApplicationWithoutApplicantType(),
			expectedErr: servus.ServiceError{
				HTTPCode: 400,
				Code:     "FIELD_INVALID",
				Message:  "Invalid enquiry request",
				Errors: []servus.ErrorDetail{{
					ErrorCode: "FIELD_MISSING",
					Message:   "Missing applicationType field",
				}},
			},
		},
		{
			name: "should return validation error when create application request has missing fields",
			req: &api.CreateFlexiTermLoanApplicationRequest{Message: &api.Message{
				ReferenceID:             "83476698-f700-46bc-baaa-54282b3bd4eb",
				OnboardingApplicationID: "83476698-f700-36bc-1aaa-24282b3bd4e",
				Application: &api.FlexiTermLoanApplication{
					ApplicationID:   "G234324235",
					ApplicationType: "NEW",
					CountryCode:     "SG",
					Channel:         "GRAB",
					Applicants: []api.FlexiTermLoanApplicant{{
						ApplicantID:                  "",
						IDType:                       "NRIC",
						IDNumber:                     "S234324321S",
						ApplicantType:                "PRIMARY",
						FullName:                     "John Doe",
						AliasName:                    "John",
						Gender:                       "MALE",
						MaritalStatus:                "SINGLE",
						ResidentialStatus:            "C",
						ExistingCustomer:             false,
						CustomerRelationshipOpenDate: "",
						ContactNumber:                "+6736363636",
						EmailAddress:                 "<EMAIL>",
						ContactType:                  "RESIDENTIAL",
						AddressType:                  "WORK",
						HouseNumber:                  "#101",
						UnitNumber:                   "",
						BuildingName:                 "",
						StreetName:                   "Street1",
						PostalCode:                   "119954",
						State:                        "Singapore",
						WhitelistFlag:                "Y",
					}},
					Products: []api.Product{{
						ProductType:           "FLEXI_LOAN_LINE_OF_CREDIT",
						SubProductType:        "DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT",
						RequestedLoanAmount:   10000,
						RequestedCurrencyCode: "SGD",
						RequestedTenure:       45,
						InterestRate:          6.5,
					}},
				},
				Status:    "LOAN_APPLICATION_CREATED",
				DeviceID:  "83476698-f700-36bc-1aaa-24282b3bd4e",
				IpAddress: "************",
				CreatedBy: "Appian",
			}},
			err: errors.New("DB Error"),
			expectedErr: servus.ServiceError{
				HTTPCode: 400,
				Code:     "FIELD_INVALID",
				Message:  "Invalid enquiry request",
				Errors: []servus.ErrorDetail{
					{
						ErrorCode: "FIELD_MISSING",
						Message:   "Missing race field",
						Path:      "",
					},
				},
			},
		},
		{
			name: "should return db failure error when the db is down",
			req:  requests.SampleCreateFlexiTermLoanApplication(),
			err:  errors.New("DB Error"),
			expectedErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "2201",
				Message:  "Failed to save data in database",
				Errors:   nil,
			},
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			mockFlexiTermLoanApplication := &handlerMock.FlexiTermLoanApplication{}
			l := &LoanAppService{
				FlexiTermLoanApplication: mockFlexiTermLoanApplication,
				AppConfig: &config.AppConfig{
					LocaleConfig: config.LocaleConfig{
						DefaultCountryCode: "SG",
					},
				},
			}
			mockFlexiTermLoanApplication.On("HandleIdempotentRequest", mock.Anything, mock.Anything).Return(test.expectedRes, test.expectedErr)
			resp, err := l.CreateFlexiTermLoanApplication(context.Background(), test.req)
			if test.expectedErr != nil {
				assert.Error(t, err, test.name)
				assert.Equal(t, test.expectedErr, err, test.name)
			} else {
				test.expectedRes.Message.Application.ApplicationID = resp.Message.Application.ApplicationID
				assert.NoError(t, err, test.name)
				assert.NotNil(t, resp, test.name)
				assert.Equal(t, test.expectedRes, resp)
			}
		})
	}
}

func Test_validateCreateFlexiTermLoanApplicationDetailsRequest(t *testing.T) {
	type args struct {
		req *api.CreateFlexiTermLoanApplicationRequest
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "should throw error with the request has invalid data",
			args: args{
				req: &api.CreateFlexiTermLoanApplicationRequest{
					Message: &api.Message{
						ReferenceID:             "",
						OnboardingApplicationID: "",
						Application: &api.FlexiTermLoanApplication{
							ApplicationID:   "validApplicationId",
							ApplicationType: "",
							AppCreationDate: time.Time{},
							CountryCode:     "SG",
							Channel:         api.Channel_GRAB,
							Applicants:      nil,
							Products:        nil,
						},
						Status:    "status",
						DeviceID:  "deviceId",
						IpAddress: "ipaddress",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should not throw error when the request has valid data",
			args: args{
				req: requests.SampleCreateFlexiTermLoanApplication(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		data := tt
		l := &LoanAppService{
			AppConfig: &config.AppConfig{
				LocaleConfig: config.LocaleConfig{
					DefaultCountryCode: "SG",
				},
			},
		}
		t.Run(data.name, func(t *testing.T) {
			if err := l.validateCreateFlexiTermLoanApplicationDetailsRequest(data.args.req); (err != nil) != data.wantErr {
				t.Errorf("validateApplication() = %v, want %v", err, data.wantErr)
			}
		})
	}
}

func Test_LoanAppService_CreateFlexiTermLoanApplication_DBMY(t *testing.T) {
	tests := []struct {
		Name        string
		Req         *api.CreateFlexiTermLoanApplicationRequest
		ExpectedRes *api.CreateFlexiTermLoanApplicationResponse
		ExpectedErr error
		Mocker      func() *handlerMock.FlexiTermLoanApplication
	}{
		{
			Name:        "should return success response when create application is a success",
			Req:         requests.SampleCreateFlexiTermLoanApplicationDBMY(),
			ExpectedRes: responses.SampleCreateFlexiTermLoanApplicationDBMY(),
			ExpectedErr: nil,
		},
		{
			Name: "should return db failure error when the db is down",
			Req:  requests.SampleCreateFlexiTermLoanApplicationDBMY(),
			ExpectedErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "2201",
				Message:  "Failed to save data in database",
				Errors:   nil,
			},
		},
		{
			Name: "should return validation error when fields are missing",
			Req:  requests.SampleCreateFlexiTermLoanApplicationWithoutApplicantIDDBMY(),
			ExpectedErr: servus.ServiceError{
				HTTPCode: 400,
				Code:     "FIELD_INVALID",
				Message:  "Invalid enquiry request",
				Errors: []servus.ErrorDetail{{
					ErrorCode: apiErr.ErrMissingApplicantID.Code,
					Message:   apiErr.ErrMissingApplicantID.Message,
				}},
			},
		},
	}

	for _, tt := range tests {
		test := tt
		t.Run(test.Name, func(t *testing.T) {
			mockFlexiTermLoan := &handlerMock.FlexiTermLoanApplication{}
			service := &LoanAppService{
				FlexiTermLoanApplication: mockFlexiTermLoan,
				AppConfig: &config.AppConfig{
					LocaleConfig: config.LocaleConfig{
						DefaultCountryCode: "MY",
					},
				},
			}

			mockFlexiTermLoan.On("HandleIdempotentRequest", mock.Anything, mock.Anything).
				Return(test.ExpectedRes, test.ExpectedErr)

			resp, err := service.CreateFlexiTermLoanApplication(context.Background(), test.Req)
			if err != nil {
				assert.Equal(t, test.ExpectedErr, err, test.Name)
			} else {
				assert.Equal(t, test.ExpectedRes, resp, test.Name)
			}
		})
	}
}

func Test_ValidateCreateFlexiTermLoanApplicationRequest_DBMY(t *testing.T) {
	tests := []struct {
		Name    string
		Args    *api.CreateFlexiTermLoanApplicationRequest
		WantErr bool
	}{
		{
			Name:    "happy-path: return validation success",
			Args:    requests.SampleCreateFlexiTermLoanApplicationDBMY(),
			WantErr: false,
		},
		{
			Name:    "sad-path: return validation error, missing applicant ID",
			Args:    requests.SampleCreateFlexiTermLoanApplicationWithoutApplicantIDDBMY(),
			WantErr: true,
		},
	}

	for _, tt := range tests {
		test := tt
		l := &LoanAppService{
			AppConfig: &config.AppConfig{
				LocaleConfig: config.LocaleConfig{
					DefaultCountryCode: "MY",
				},
			},
		}
		t.Run(test.Name, func(t *testing.T) {
			err := l.validateCreateFlexiTermLoanApplicationDetailsRequest(test.Args)
			if (err != nil) != test.WantErr {
				t.Errorf("validateApplication() = %v, want %v", err, test.WantErr)
			}
		})
	}
}
