package handlers

import (
	"context"
	"fmt"
	"net/http"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateFlexiTermLoanApplicationV2 : V2 API to create flexi term loan application
// nolint:dupl
func (l *LoanAppService) CreateFlexiTermLoanApplicationV2(ctx context.Context, req *api.CreateFlexiTermLoanApplicationRequest) (*api.CreateFlexiTermLoanApplicationResponseV2, error) {
	slog.FromContext(ctx).Info(constants.CreateApplicationLogTagV2, fmt.Sprintf("Received Request, onboardingApplicationID: %s", req.Message.OnboardingApplicationID), utils.GetTraceID(ctx))
	if errs := l.validateCreateFlexiTermLoanApplicationDetailsRequest(req); len(errs) != 0 {
		return nil, commonErr.BuildErrorResponseWithErrorDetail(
			http.StatusBadRequest, commonErr.ErrInvalidEnquiryRequest.Code,
			commonErr.ErrInvalidEnquiryRequest.Message, errs)
	}

	ctx = slog.AddTagsToContext(ctx,
		slog.CustomTag(constants.ReferenceIDTag, req.Message.ReferenceID),
		slog.CustomTag(constants.OnboardingApplicationIDTag, req.Message.OnboardingApplicationID),
	)
	logicAnalyser := l.FlexiTermLoanApplicationWorkflow.GetLogicAnalyser()
	if l.AppConfig.FeatureFlags.BusinessValidationFlag {
		if err := logicAnalyser.ValidateApplicant(ctx, req.Message.Application.Applicants); err != nil {
			slog.FromContext(ctx).Error(constants.CreateApplicationLogTagV2, fmt.Sprintf("Applicant validation failed, err: %s", err.Error()), utils.GetTraceID(ctx))
			return nil, err
		}
	}
	executionData, err := l.FlexiTermLoanApplicationWorkflow.ExecuteCreateFlexiTermLoanApplicationWorkflow(ctx, &dto.CreateFlexiTermLoanApplicationWorkflowRequest{
		Message: req.Message,
	})
	if err != nil {
		slog.FromContext(ctx).Error(constants.CreateApplicationLogTagV2, fmt.Sprintf("Error in calling flexi-term loan application workflow, err: %v", err), utils.GetTraceID(ctx))
		return nil, err
	}

	applicationID := l.FlexiTermLoanApplicationWorkflow.GetApplicationID(executionData)

	slog.FromContext(ctx).Info(constants.CreateApplicationLogTagV2, fmt.Sprintf("Successfully completed response, applicationID: %s", applicationID), utils.GetTraceID(ctx))
	return &api.CreateFlexiTermLoanApplicationResponseV2{ApplicationID: applicationID}, nil
}
