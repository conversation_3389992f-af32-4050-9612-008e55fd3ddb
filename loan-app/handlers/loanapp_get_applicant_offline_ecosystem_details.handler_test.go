package handlers

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestLoanAppService_GetApplicantEcoSystemDetails(t *testing.T) {
	tests := []struct {
		name         string
		req          *api.GetApplicantOfflineEcoSystemDetailsRequest
		expectedRes  *api.GetApplicantOfflineEcoSystemDetailsResponse
		expectedData []*storage.ApplicantOfflineEcosystemDetails
		expectedErr  error
		dbErr        error
	}{
		{
			name: "should return success response when get ecosystem data is successful",
			req: &api.GetApplicantOfflineEcoSystemDetailsRequest{
				ApplicantID: "MY123",
			},
			expectedRes: &api.GetApplicantOfflineEcoSystemDetailsResponse{
				ApplicantID: "MY123",
				EcosystemData: []api.OfflineEcosystemData{
					{
						Partner: "GRAB",
						Data: map[string]interface{}{
							"rem_score":      "576",
							"applicant_type": "ntp",
							"exported_date":  "2024-11-19T08:59:00",
						},
					},
				},
			},
			expectedData: []*storage.ApplicantOfflineEcosystemDetails{
				{
					ApplicantID: "MY123",
					Partner:     "GRAB",
					Data:        json.RawMessage(`{"rem_score": "576", "applicant_type": "ntp", "exported_date": "2024-11-19T08:59:00"}`),
				},
			},
		},
		{
			name: "should return error response for get ecosystem details",
			req: &api.GetApplicantOfflineEcoSystemDetailsRequest{
				ApplicantID: "",
			},
			expectedRes: nil,
			expectedErr: servus.ServiceError{
				HTTPCode: 400,
				Code:     "1551",
				Message:  "Invalid request parameters",
				Errors: []servus.ErrorDetail{{
					ErrorCode: "FIELD_INVALID",
					Message:   "Missing Applicant ID field",
					Path:      "applicantID",
				}},
			},
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			l := &LoanAppService{
				AppConfig: &config.AppConfig{
					LocaleConfig: config.LocaleConfig{
						DefaultCountryCode: "MY",
					},
				},
			}
			applicantOfflineEcosystemMock := &storage.MockIApplicantOfflineEcosystemDetailsDAO{}
			storage.ApplicantOfflineEcosystemDetailsDao = applicantOfflineEcosystemMock
			applicantOfflineEcosystemMock.On("Find", mock.Anything, mock.Anything).Return(tt.expectedData, tt.dbErr)
			resp, err := l.GetApplicantEcoSystemDetails(context.Background(), test.req)
			assert.Equal(t, tt.expectedRes, resp)
			assert.Equal(t, tt.expectedErr, err)
		})
	}
}
