package handlers

import (
	"context"
	"fmt"
	"net/http"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/validations"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetFlexiTermLoanApplicationProgressPercentage : API to get the application percentage tracker
// nolint:dupl
func (l *LoanAppService) GetFlexiTermLoanApplicationProgressPercentage(ctx context.Context, req *api.GetFlexiTermLoanApplicationProgressPercentageRequest) (*api.GetFlexiTermLoanApplicationProgressPercentageResponse, error) {
	slog.FromContext(ctx).Info(constants.GetFlexiTermLoanApplicationProgressPercentageLogTag, fmt.Sprintf("Received Request: %v", utils.ToJSON(req)), utils.GetTraceID(ctx))

	if errs := ValidateGetFlexiTermLoanApplicationProgressPercentageRequest(ctx, req); len(errs) != 0 {
		return nil, commonErr.BuildErrorResponseWithErrorDetail(
			http.StatusBadRequest, commonErr.ErrInvalidGetFlexiLoanProgressPercentageRequest.Code,
			commonErr.ErrInvalidGetFlexiLoanProgressPercentageRequest.Message, errs)
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(constants.UserID, utils.GetUserID(ctx)))
	res, err := l.FlexiTermLoanApplication.GetApplicationProgressPercentage(ctx, req)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetFlexiTermLoanApplicationProgressPercentageLogTag, fmt.Sprintf("Successful Response: %v", utils.ToJSON(res)), utils.GetTraceID(ctx))
	return res, nil
}

// ValidateGetFlexiTermLoanApplicationProgressPercentageRequest ...
// nolint:dupl
func ValidateGetFlexiTermLoanApplicationProgressPercentageRequest(ctx context.Context, req *api.GetFlexiTermLoanApplicationProgressPercentageRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	userID := utils.GetUserID(ctx)
	serviceID := utils.GetServiceID(ctx)

	errs = append(errs, validations.ValidateOnboardingApplicationID(req.OnboardingApplicationID)...)
	errs = append(errs, validations.ValidateProductVariantCode(req.ProductVariantCode)...)
	errs = append(errs, validations.ValidateUserID(userID)...)
	errs = append(errs, validations.ValidateServiceID(serviceID)...)

	return errs
}
