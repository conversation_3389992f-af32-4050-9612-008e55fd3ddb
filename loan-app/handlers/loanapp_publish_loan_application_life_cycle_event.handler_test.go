package handlers

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	handlerMock "gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans/mocks"
)

func TestPublishLoanApplicationLifeCycleEvent(t *testing.T) {
	errDummy := errors.New("simulate error")
	var publishReq = &api.PublishLoanApplicationLifecycleEventRequest{
		OnboardingApplicationID: "123456",
		Applicants: []api.LoanAppApplicant{
			{
				ApplicantType: "",
				ApplicantID:   "",
			},
		},
		AccountID:          "",
		IpAddress:          "",
		DeviceID:           "",
		Status:             "",
		StatusReason:       "",
		ProductCode:        "",
		ProductVariantCode: "",
		ApplicationID:      "",
		Errors: []api.LoanAppError{
			{
				ErrorCode:        "",
				ErrorDescription: "",
			},
		},
		CreatedAt: time.Time{},
		UpdatedAt: time.Time{},
	}
	var response = &api.PublishLoanApplicationLifecycleEventResponse{
		Status: "PUBLISHED",
	}
	scenarios := []struct {
		desc             string
		publishReq       *api.PublishLoanApplicationLifecycleEventRequest
		handlerErr       error
		expectedErr      bool
		expectedResponse *api.PublishLoanApplicationLifecycleEventResponse
	}{

		{
			desc:        "error from handler",
			publishReq:  publishReq,
			handlerErr:  errDummy,
			expectedErr: true,
		},
		{
			desc:             "Happy path",
			publishReq:       publishReq,
			handlerErr:       nil,
			expectedErr:      false,
			expectedResponse: response,
		},
	}

	for _, scenario := range scenarios {
		testcase := scenario
		mockFlexiTermLoanApplication := &handlerMock.FlexiTermLoanApplication{}
		testService := &LoanAppService{FlexiTermLoanApplication: mockFlexiTermLoanApplication}
		mockFlexiTermLoanApplication.On("PublishLoanApplicationLifeCycleEvent", mock.Anything, mock.Anything).Return(testcase.expectedResponse, testcase.handlerErr)
		resp, err := testService.PublishLoanApplicationLifeCycleEvent(context.Background(), testcase.publishReq)
		if (err != nil) != testcase.expectedErr {
			t.Errorf("TestPublishUpdateLoanAppLifeCycle() error = %v, wantErr %v", err, testcase.expectedErr)
			return
		}
		if !testcase.expectedErr {
			assert.Equal(t, testcase.expectedResponse, resp)
		}
	}
}
