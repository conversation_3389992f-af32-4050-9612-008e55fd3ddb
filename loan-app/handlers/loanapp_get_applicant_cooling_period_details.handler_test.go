package handlers //nolint:dupl
import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	handlerMock "gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans/mocks"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestLoanAppService_GetApplicantCoolingPeriodDetails(t *testing.T) {
	tests := []struct {
		name        string
		req         *api.GetApplicantCoolingPeriodDetailsRequest
		err         error
		expectedRes *api.GetApplicantCoolingPeriodDetailsResponse
		expectedErr error
	}{
		{
			name: "should return success response when get cooling period details is a success",
			req: &api.GetApplicantCoolingPeriodDetailsRequest{
				ApplicantID:        "ID123",
				ProductVariantCode: string(api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
			},
			expectedRes: &api.GetApplicantCoolingPeriodDetailsResponse{
				ApplicantID:        "ID123",
				ProductVariantCode: string(api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
				ReasonCode:         "123",
				CoolingPeriod:      1,
				CreatedAt:          time.Now(),
				UpdatedAt:          time.Now(),
			},
		},
		{
			name: "should return error response for get cooling period details",
			req: &api.GetApplicantCoolingPeriodDetailsRequest{
				ApplicantID:        "",
				ProductVariantCode: string(api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT),
			},
			expectedRes: nil,
			expectedErr: servus.ServiceError{
				HTTPCode: 400,
				Code:     "1520",
				Message:  "Invalid request parameters",
				Errors: []servus.ErrorDetail{{
					ErrorCode: "FIELD_INVALID",
					Message:   "Missing Applicant ID field",
					Path:      "applicantID",
				}},
			},
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			mockFlexiTermLoanApplication := &handlerMock.FlexiTermLoanApplication{}
			l := &LoanAppService{
				FlexiTermLoanApplication: mockFlexiTermLoanApplication,
				AppConfig: &config.AppConfig{
					LocaleConfig: config.LocaleConfig{
						DefaultCountryCode: "ID",
					},
				},
			}
			mockFlexiTermLoanApplication.On("GetApplicantCoolingPeriodDetails", mock.Anything, mock.Anything).Return(test.expectedRes, test.expectedErr)
			resp, err := l.GetApplicantCoolingPeriodDetails(context.Background(), test.req)
			if test.expectedErr != nil {
				assert.Error(t, err, test.name)
				assert.Equal(t, test.expectedErr, err, test.name)
			} else {
				assert.NoError(t, err, test.name)
				assert.NotNil(t, resp, test.name)
				assert.Equal(t, test.expectedRes, resp)
			}
		})
	}
}
