package handlers

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	handlerMock "gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans/mocks"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func TestGetWhitelistHistoryApplicant(t *testing.T) {
	trueBool := true
	// Mock response
	response := &api.GetWhitelistHistoryApplicantResponse{
		Key:   "CIFID",
		Value: "ID123",
		HistoryDetails: []api.WhitelistHistoryDetail{
			{
				CreatedBy:   "external",
				Whitelisted: &(trueBool),
				CreatedAt:   time.Time{},
			},
		},
	}

	tests := []struct {
		name          string
		req           *api.GetWhitelistHistoryApplicantRequest
		responseError error
		want          *api.GetWhitelistHistoryApplicantResponse
		wantErr       error
	}{
		{
			name:    "success response",
			req:     &api.GetWhitelistHistoryApplicantRequest{ApplicantWhitelistID: 1},
			want:    response,
			wantErr: nil,
		},
		{
			name: "empty applicant whitelist id",
			req:  &api.GetWhitelistHistoryApplicantRequest{},
			want: nil,
			wantErr: commonErr.BuildErrorResponseWithErrorDetail(
				http.StatusBadRequest,
				commonErr.ErrInvalidApplicantWhitelistID.Code,
				commonErr.ErrInvalidApplicantWhitelistID.Message,
				[]servus.ErrorDetail{
					{
						ErrorCode: commonErr.ErrInvalidApplicantWhitelistID.Code,
						Message:   commonErr.ErrInvalidApplicantWhitelistID.Message,
						Path:      "applicantWhitelistID",
					},
				}),
		},
		{
			name:          "empty applicant whitelist id",
			req:           &api.GetWhitelistHistoryApplicantRequest{ApplicantWhitelistID: 1},
			responseError: data.ErrNoData,
			want:          nil,
			wantErr:       data.ErrNoData,
		},
	}

	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			// Mock the FlexiTermLoanApplication
			mockFlexiTermLoanApplication := &handlerMock.FlexiTermLoanApplication{}
			mockFlexiTermLoanApplication.On("GetWhitelistHistoryApplicant", mock.Anything, mock.Anything).
				Return(test.want, test.responseError)

			// Create the LoanAppService
			appConfig := &config.AppConfig{
				LocaleConfig: config.LocaleConfig{
					DefaultCountryCode: "ID",
				},
			}
			testService := &LoanAppService{FlexiTermLoanApplication: mockFlexiTermLoanApplication, AppConfig: appConfig}
			resp, err := testService.GetWhitelistHistoryApplicant(context.Background(), test.req)

			// Assert the results
			if test.wantErr != nil {
				assert.Error(t, err)
				assert.Equal(t, test.wantErr, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, resp, test.want)
			}
		})
	}
}
