package handlers //nolint:dupl
import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	handlerMock "gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans/mocks"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestLoanAppService_UpdateFlexiTermLoanApplicationStatus(t *testing.T) {
	tests := []struct {
		name           string
		req            *api.UpdateFlexiTermLoanApplicationStatusRequest
		err            error
		expectedRes    *api.UpdateFlexiTermLoanApplicationStatusResponse
		expectedErr    error
		idempotencyErr error
	}{
		{
			name: "should return success response for update flexi-term loan application status",
			req: &api.UpdateFlexiTermLoanApplicationStatusRequest{
				ApplicationID: "ID123",
				Status:        "PROCESSING",
				StatusReason:  "CDE_PRE_BUREAU_FAILED",
				CountryCode:   "ID",
				ReferenceID:   "123456",
				UpdatedBy:     "APPIAN",
			},
			expectedRes: &api.UpdateFlexiTermLoanApplicationStatusResponse{
				ApplicationID: "ID123",
				Status:        "PROCESSING",
				StatusReason:  "CDE_PRE_BUREAU_FAILED",
				ReferenceID:   "123456",
			},
			idempotencyErr: nil,
		},
		{
			name: "should return db failure error when the idempotency query errors out",
			req: &api.UpdateFlexiTermLoanApplicationStatusRequest{
				ApplicationID: "ID123",
				Status:        "PROCESSING",
				StatusReason:  "CDE_PRE_BUREAU_FAILED",
				CountryCode:   "ID",
				ReferenceID:   "123456",
				UpdatedBy:     "APPIAN",
			},
			idempotencyErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "2201",
				Message:  "Failed to save data in database",
				Errors:   nil,
			},
			expectedErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "2201",
				Message:  "Failed to save data in database",
				Errors:   nil,
			},
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			mockFlexiTermLoanApplication := &handlerMock.FlexiTermLoanApplication{}
			l := &LoanAppService{
				FlexiTermLoanApplication: mockFlexiTermLoanApplication,
				AppConfig: &config.AppConfig{
					LocaleConfig: config.LocaleConfig{
						DefaultCountryCode: "ID",
					},
				},
			}
			ctx := commonCtx.WithUserID(context.Background(), "Dummy")
			ctx = commonCtx.WithServiceID(ctx, "Dummy")
			mockFlexiTermLoanApplication.On("HandleIdempotentRequest", mock.Anything, mock.Anything).Return(test.expectedRes, test.idempotencyErr)
			resp, err := l.UpdateFlexiTermLoanApplicationStatus(ctx, test.req)
			if test.expectedErr != nil {
				assert.Error(t, err, test.name)
				assert.Equal(t, test.expectedErr, err, test.name)
			} else {
				assert.NoError(t, err, test.name)
				assert.NotNil(t, resp, test.name)
				assert.Equal(t, test.expectedRes, resp)
			}
		})
	}
}
