package handlers

import (
	"context"
	"errors"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	handlerMock "gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans/mocks"

	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestGetFlexiTermLoanApplicationV2(t *testing.T) {
	scenarios := []struct {
		name           string
		request        *api.GetFlexiTermLoanApplicationRequestV2
		mockResponse   *api.GetFlexiTermLoanApplicationResponseV2
		mockError      error
		expectedError  error
		expectedStatus int
	}{
		{
			name: "Invalid request - missing ApplicationID",
			request: &api.GetFlexiTermLoanApplicationRequestV2{
				OnboardingApplicationID: "",
			},
			expectedError: servus.ServiceError{
				HTTPCode: 400,
				Code:     "1510",
				Message:  "Invalid request parameters",
				Errors: []servus.ErrorDetail{{
					ErrorCode: "FIELD_MISSING",
					Message:   "Missing onboardingApplicationID field",
					Path:      "onboardingApplicationID",
				}},
			},
		},
		{
			name: "Successful response",
			request: &api.GetFlexiTermLoanApplicationRequestV2{
				OnboardingApplicationID: "app123",
			},
			mockResponse: &api.GetFlexiTermLoanApplicationResponseV2{
				Status:       "APPROVED",
				StatusReason: "Approved successfully",
			},
			expectedError:  nil,
			expectedStatus: http.StatusOK,
		},
		{
			name: "Error from GetLoanApplicationDetailsV2",
			request: &api.GetFlexiTermLoanApplicationRequestV2{
				OnboardingApplicationID: "app123",
			},
			mockError:     errors.New("internal error"),
			expectedError: errors.New("internal error"),
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.name, func(t *testing.T) {
			mockFlexiTermLoanApplication := &handlerMock.FlexiTermLoanApplication{}
			mockFlexiTermLoanApplication.On("GetLoanApplicationDetailsV2", mock.Anything, mock.Anything).Return(scenario.mockResponse, scenario.mockError)

			service := &LoanAppService{
				FlexiTermLoanApplication: mockFlexiTermLoanApplication,
			}

			response, err := service.GetFlexiTermLoanApplicationV2(context.Background(), scenario.request)

			if scenario.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, scenario.expectedError, err)
				assert.Nil(t, response)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, scenario.mockResponse, response)
			}
		})
	}
}
