// Package handlers ...
// nolint:dupl
package handlers

import (
	context "context"
	"fmt"
	"net/http"

	api "gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/validations"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetApplicantEcoSystemDetails : API to get the applicant ecoSystem details
func (l *LoanAppService) GetApplicantEcoSystemDetails(ctx context.Context, req *api.GetApplicantOfflineEcoSystemDetailsRequest) (*api.GetApplicantOfflineEcoSystemDetailsResponse, error) {
	ctx = slog.AddTagsToContext(ctx,
		slog.CustomTag(constants.ApplicantIDTag, req.ApplicantID),
	)
	slog.FromContext(ctx).Info(constants.GetApplicantEcoSystemDetailsLogTag, fmt.Sprintf("Received Request: %v", utils.ToJSON(req)), utils.GetTraceID(ctx))

	if errs := ValidateGetApplicantEcoSystemDetails(req); len(errs) != 0 {
		return nil, commonErr.BuildErrorResponseWithErrorDetail(
			http.StatusBadRequest, commonErr.ErrInvalidGetApplicantOfflineEcosystemDetailsRequest.Code,
			commonErr.ErrInvalidGetApplicantOfflineEcosystemDetailsRequest.Message, errs)
	}

	res, err := handlerlogic.GetApplicantEcoSystemDetails(ctx, req)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// ValidateGetApplicantEcoSystemDetails ...
func ValidateGetApplicantEcoSystemDetails(req *api.GetApplicantOfflineEcoSystemDetailsRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	errs = append(errs, validations.ValidateApplicantID(req.ApplicantID)...)
	return errs
}
