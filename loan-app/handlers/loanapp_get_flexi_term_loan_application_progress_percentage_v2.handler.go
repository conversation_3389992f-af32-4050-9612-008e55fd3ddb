// Package handlers ...
//
//nolint:dupl
package handlers

import (
	context "context"
	"fmt"
	"net/http"

	api "gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/validations"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetFlexiTermLoanApplicationProgressPercentageV2 : API to get the application percentage tracker
func (l *LoanAppService) GetFlexiTermLoanApplicationProgressPercentageV2(ctx context.Context, req *api.GetFlexiTermLoanApplicationProgressPercentageRequestV2) (*api.GetFlexiTermLoanApplicationProgressPercentageResponseV2, error) {
	slog.FromContext(ctx).Info(constants.GetFlexiTermLoanApplicationProgressPercentageLogTagV2, fmt.Sprintf("Received Request: %v", utils.ToJSON(req)), utils.GetTraceID(ctx))

	if errs := ValidateGetFlexiTermLoanApplicationProgressPercentageV2Request(ctx, req); len(errs) != 0 {
		return nil, commonErr.BuildErrorResponseWithErrorDetail(
			http.StatusBadRequest, commonErr.ErrInvalidGetFlexiLoanProgressPercentageRequest.Code,
			commonErr.ErrInvalidGetFlexiLoanProgressPercentageRequest.Message, errs)
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(constants.UserID, utils.GetUserID(ctx)))
	res, err := l.FlexiTermLoanApplication.GetApplicationProgressPercentageV2(ctx, req)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetFlexiTermLoanApplicationProgressPercentageLogTagV2, fmt.Sprintf("Successful Response: %v", utils.ToJSON(res)), utils.GetTraceID(ctx))
	return res, nil
}

// ValidateGetFlexiTermLoanApplicationProgressPercentageV2Request ...
func ValidateGetFlexiTermLoanApplicationProgressPercentageV2Request(ctx context.Context, req *api.GetFlexiTermLoanApplicationProgressPercentageRequestV2) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	userID := utils.GetUserID(ctx)
	serviceID := utils.GetServiceID(ctx)

	errs = append(errs, validations.ValidateProductVariantCode(req.ProductVariantCode)...)
	errs = append(errs, validations.ValidateUserID(userID)...)
	errs = append(errs, validations.ValidateServiceID(serviceID)...)

	return errs
}
