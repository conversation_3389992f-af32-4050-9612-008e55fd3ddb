package handlers

import (
	"context"
	"fmt"
	"net/http"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// PublishLoanApplicationLifeCycleEvent ...
func (l *LoanAppService) PublishLoanApplicationLifeCycleEvent(ctx context.Context, req *api.PublishLoanApplicationLifecycleEventRequest) (*api.PublishLoanApplicationLifecycleEventResponse, error) {
	slog.FromContext(ctx).Info(constants.PublishLoanApplicationLifeCycleEventLogTag, fmt.Sprintf("Received Request: %s", utils.ToJSON(req)), utils.GetTraceID(ctx))

	res, err := l.FlexiTermLoanApplication.PublishLoanApplicationLifeCycleEvent(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Info(constants.PublishLoanApplicationLifeCycleEventLogTag, fmt.Sprintf("Err: %s", utils.ToJSON(err)), utils.GetTraceID(ctx))
		return nil, commonErr.BuildErrorResponse(
			http.StatusInternalServerError, commonErr.ErrPublishFailed.Code,
			commonErr.ErrPublishFailed.Message)
	}
	slog.FromContext(ctx).Info(constants.PublishLoanApplicationLifeCycleEventLogTag, fmt.Sprintf("Successful Request: %s", utils.ToJSON(req)), utils.GetTraceID(ctx))

	return res, nil
}
