package handlers

import (
	"context"
	"fmt"
	"net/http"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetFlexiTermLoanApplicationByIdentifier : API to get flexi term loan application by identifier
func (l *LoanAppService) GetFlexiTermLoanApplicationByIdentifier(ctx context.Context, req *api.GetFlexiTermLoanApplicationByIdentifierRequest) (*api.GetFlexiTermLoanApplicationByIdentifierResponse, error) {
	if errs := flexitermloans.ValidateGetFlexiTermLoanApplicationDetailsRequest(req); len(errs) != 0 {
		slog.FromContext(ctx).Warn(constants.GetApplicationByIdentifierLogTag, fmt.Sprintf("Validation failed: %+v", errs), utils.GetTraceID(ctx))
		return nil, commonErr.BuildErrorResponseWithErrorDetail(
			http.StatusBadRequest, commonErr.ErrInvalidGetLoanApplicationByIdentifierRequest.Code, commonErr.ErrInvalidGetLoanApplicationByIdentifierRequest.Message, errs)
	}
	ctx = slog.AddTagsToContext(ctx,
		slog.CustomTag(constants.IdentifierTypeTag, req.IdentifierType),
	)

	res, err := l.FlexiTermLoanApplication.GetLoanApplicationDetailsByIdentifier(ctx, req)
	if err != nil {
		return nil, err
	}

	slog.FromContext(ctx).Info(constants.GetApplicationByIdentifierLogTag, fmt.Sprintf("Successful Response, identifierType: %s", req.IdentifierType), utils.GetTraceID(ctx))
	return res, nil
}
