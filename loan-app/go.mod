module gitlab.com/gx-regional/dakota/lending/loan-app

replace (
	github.com/aws/aws-sdk-go => github.com/aws/aws-sdk-go v1.44.177
	github.com/coreos/go-systemd => github.com/coreos/go-systemd/v22 v22.0.0
	github.com/gogo/protobuf => github.com/gogo/protobuf v1.3.2
	github.com/miekg/dns => github.com/miekg/dns v1.1.26
	github.com/prometheus/client_golang => github.com/prometheus/client_golang v1.12.2
	github.com/satori/go.uuid => github.com/satori/go.uuid v1.2.0
	gitlab.com/gx-regional/dakota/common/retryable-stream => gitlab.com/gx-regional/dakota/common.git/retryable-stream v1.19.0
	gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api => gitlab.com/gx-regional/dakota/lending.git/cr-decision-eng/api v1.47.1
	gitlab.com/gx-regional/dakota/lending/loan-app/api => ./api
	gitlab.com/gx-regional/dakota/schemas => gitlab.com/gx-regional/dakota/schemas.git v1.20.5
	gitlab.myteksi.net/bersama/core-banking/product-master/api => gitlab.super-id.net/bersama/core-banking/product-master/api v1.24.0
	gitlab.myteksi.net/bersama/customer-master/api/v2 => gitlab.super-id.net/bersama/customer-master/api/v2 v2.31.19
	gitlab.myteksi.net/bersama/transaction-statements/api => gitlab.super-id.net/bersama/transaction-statements/api v0.0.0-**************-eb28351525ce
	gitlab.myteksi.net/dakota/application-service/api => gitlab.com/gx-regional/dakota/application-service.git/api v0.99.1
	gitlab.myteksi.net/dakota/common/aws => gitlab.com/gx-regional/dakota/common.git/aws v1.4.4
	gitlab.myteksi.net/dakota/common/context => gitlab.com/gx-regional/dakota/common.git/context v0.17.0
	gitlab.myteksi.net/dakota/common/countries => gitlab.com/gx-regional/dakota/common.git/countries v1.0.0
	gitlab.myteksi.net/dakota/common/crypto => gitlab.com/gx-regional/dakota/common.git/crypto v1.0.2
	gitlab.myteksi.net/dakota/common/currency => gitlab.com/gx-regional/dakota/common.git/currency v1.0.0
	gitlab.myteksi.net/dakota/common/env-injection => gitlab.com/gx-regional/dakota/common.git/env-injection v1.0.0
	gitlab.myteksi.net/dakota/common/redis => gitlab.com/gx-regional/dakota/common.git/redis v0.20.0
	gitlab.myteksi.net/dakota/common/secrets-injection => gitlab.com/gx-regional/dakota/common.git/secrets-injection v1.0.3
	gitlab.myteksi.net/dakota/common/servicename => gitlab.com/gx-regional/dakota/common.git/servicename v1.55.0
	gitlab.myteksi.net/dakota/common/testauto => gitlab.com/gx-regional/dakota/common.git/testauto v1.18.0
	gitlab.myteksi.net/dakota/common/tracing => gitlab.com/gx-regional/dakota/common.git/tracing v1.10.0
	gitlab.myteksi.net/dakota/core-banking/account-service => gitlab.com/gx-regional/dakota/core-banking.git/account-service v0.0.0-**************-69bd438c2002
	gitlab.myteksi.net/dakota/core-banking/account-service/api => gitlab.com/gx-regional/dakota/core-banking.git/account-service/api v1.132.0
	gitlab.myteksi.net/dakota/core-banking/account-service/external => gitlab.com/gx-regional/dakota/core-banking.git/account-service/external v0.0.0-**************-3af0f02a47be
	gitlab.myteksi.net/dakota/core-banking/deposits-core/api => gitlab.com/gx-regional/dakota/core-banking.git/deposits-core/api v1.45.0
	gitlab.myteksi.net/dakota/core-banking/deposits-exp/api => gitlab.com/gx-regional/dakota/core-banking.git/deposits-exp/api v1.16.0
	gitlab.myteksi.net/dakota/core-banking/goal-core/api => gitlab.com/gx-regional/dakota/core-banking.git/goal-core/api v1.9.0
	gitlab.myteksi.net/dakota/core-banking/product-master/api => gitlab.com/gx-regional/dakota/core-banking.git/product-master/api v1.35.0
	gitlab.myteksi.net/dakota/customer-master/api/v2 => gitlab.com/gx-regional/dakota/customer-master.git/api/v2 v2.64.0
	gitlab.myteksi.net/dakota/digicard/digicard-core/api => gitlab.com/gx-regional/dakota/digicard.git/digicard-core/api v1.88.1
	gitlab.myteksi.net/dakota/experian-adapter/api => gitlab.com/gx-regional/dakota/experian-adapter.git/api v1.19.0
	gitlab.myteksi.net/dakota/flow => gitlab.com/gx-regional/dakota/flow.git v1.2.0
	gitlab.myteksi.net/dakota/gaia => gitlab.com/gx-regional/dakota/gaia.git v0.1.1
	gitlab.myteksi.net/dakota/hermes/api => gitlab.com/gx-regional/dakota/hermes.git/api v1.9.0
	gitlab.myteksi.net/dakota/klient => gitlab.com/gx-regional/dakota/klient.git v1.25.0
	gitlab.myteksi.net/dakota/lending/cb-my-adapter/api => gitlab.com/gx-regional/dakota/lending.git/cb-my-adapter/api v1.7.0
	gitlab.myteksi.net/dakota/lending/common => ../common
	gitlab.myteksi.net/dakota/lending/credit-bureau/api => gitlab.com/gx-regional/dakota/lending.git/credit-bureau/api v1.13.0
	gitlab.myteksi.net/dakota/lending/external => ../external
	gitlab.myteksi.net/dakota/lending/loan-core/api => gitlab.com/gx-regional/dakota/lending.git/loan-core/api v1.185.1
	gitlab.myteksi.net/dakota/lending/perfios-adapter/api => gitlab.com/gx-regional/dakota/lending.git/perfios-adapter/api v1.7.2
	gitlab.myteksi.net/dakota/lending/simulator => gitlab.com/gx-regional/dakota/lending.git/simulator v1.73.0
	gitlab.myteksi.net/dakota/pigeon/api => gitlab.com/gx-regional/dakota/pigeon.git/api v1.16.2
	gitlab.myteksi.net/dakota/servus => gitlab.com/gx-regional/dakota/servus.git v1.64.0
	gitlab.myteksi.net/dakota/servus/v2 => gitlab.com/gx-regional/dakota/servus.git/v2 v2.54.0
	gitlab.myteksi.net/dakota/state-machine => gitlab.com/gx-regional/dakota/state-machine.git v1.4.0
	gitlab.myteksi.net/dakota/transaction-statements/api => gitlab.com/gx-regional/dakota/transaction-statements.git/api v1.23.0
	gitlab.myteksi.net/dakota/verification-provider/api => gitlab.com/gx-regional/dakota/verification-provider.git/api v1.40.0
	gitlab.myteksi.net/dakota/whitelist-service/api => gitlab.com/gx-regional/dakota/whitelist-service.git/api v1.31.0
	gitlab.myteksi.net/dakota/workflowengine => gitlab.com/gx-regional/dakota/workflowengine.git v1.21.0
	gitlab.myteksi.net/dbmy/application-service/api => gitlab.com/gx-regional/dbmy/application-service.git/api v1.17.1-dbmy
	gitlab.myteksi.net/dbmy/common/active-profile => gitlab.com/gx-regional/dbmy/common.git/active-profile v1.5.0
	gitlab.myteksi.net/dbmy/common/active-profile/v2 => gitlab.com/gx-regional/dbmy/common.git/active-profile/v2 v2.0.0
	gitlab.myteksi.net/dbmy/core-banking/account-service/api => gitlab.com/gx-regional/dbmy/core-banking.git/account-service/api v1.66.0-dbmy
	gitlab.myteksi.net/dbmy/core-banking/product-master/api => gitlab.com/gx-regional/dbmy/core-banking.git/product-master/api v1.31.0-dbmy
	gitlab.myteksi.net/dbmy/customer-experience/api => gitlab.com/gx-regional/dbmy/customer-experience.git/api v1.57.4
	gitlab.myteksi.net/dbmy/customer-master/api/v2 => gitlab.com/gx-regional/dbmy/customer-master.git/api/v2 v2.58.0-dbmy
	gitlab.myteksi.net/dbmy/ekyb-service/api => gitlab.com/gx-regional/dbmy/ekyb-service.git/api v1.1.1-0.**************-9ab8550236ad
	gitlab.myteksi.net/dbmy/gd-proxy/api => gitlab.com/gx-regional/dbmy/gd-proxy.git/api v0.37.0
	gitlab.myteksi.net/dbmy/pigeon/api => gitlab.com/gx-regional/dbmy/pigeon.git/api v1.33.0-dbmy
	gitlab.myteksi.net/dbmy/risk-broker/api => gitlab.com/gx-regional/dbmy/risk-broker.git/api v1.30.0-dbmy
	gitlab.myteksi.net/dbmy/transaction-statements/api => gitlab.com/gx-regional/dbmy/transaction-statements.git/api v1.7.1-dbmy
	golang.org/x/crypto => golang.org/x/crypto v0.17.0
	golang.org/x/net => golang.org/x/net v0.33.0
	golang.org/x/text => golang.org/x/text v0.3.8
	google.golang.org/grpc => google.golang.org/grpc v1.58.3
	gopkg.in/yaml.v2 => gopkg.in/yaml.v2 v2.4.0
	gopkg.in/yaml.v3 => gopkg.in/yaml.v3 v3.0.1
)

go 1.24

require (
	github.com/Rhymond/go-money v1.0.10
	github.com/go-co-op/gocron v1.17.0
	github.com/go-openapi/errors v0.19.4
	github.com/google/uuid v1.6.0
	github.com/json-iterator/go v1.1.12
	github.com/myteksi/hystrix-go v1.1.3
	github.com/onsi/ginkgo v1.16.5
	github.com/onsi/gomega v1.27.10
	github.com/samber/lo v1.39.0
	github.com/shopspring/decimal v1.3.1
	github.com/stretchr/testify v1.10.0
	gitlab.com/gx-regional/dakota/common/retryable-stream v1.19.0
	gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api v1.47.1
	gitlab.com/gx-regional/dakota/lending/loan-app/api v0.0.0-**************-************
	gitlab.com/gx-regional/dakota/schemas v1.20.5
	gitlab.myteksi.net/dakota/common/context v0.17.0
	gitlab.myteksi.net/dakota/common/countries v1.0.0
	gitlab.myteksi.net/dakota/common/crypto v1.0.2
	gitlab.myteksi.net/dakota/common/currency v1.0.0
	gitlab.myteksi.net/dakota/common/redis v0.20.0
	gitlab.myteksi.net/dakota/common/servicename v1.55.0
	gitlab.myteksi.net/dakota/common/testauto v1.18.0
	gitlab.myteksi.net/dakota/common/tracing v1.10.0
	gitlab.myteksi.net/dakota/core-banking/account-service v0.0.0-**************-69bd438c2002
	gitlab.myteksi.net/dakota/digicard/digicard-core/api v1.88.1
	gitlab.myteksi.net/dakota/experian-adapter/api v1.19.0
	gitlab.myteksi.net/dakota/flow v1.2.0
	gitlab.myteksi.net/dakota/klient v1.25.0
	gitlab.myteksi.net/dakota/lending/cb-my-adapter/api v1.7.0
	gitlab.myteksi.net/dakota/lending/common v0.0.0-**************-************
	gitlab.myteksi.net/dakota/lending/credit-bureau/api v1.13.0
	gitlab.myteksi.net/dakota/lending/external v0.0.0-**************-************
	gitlab.myteksi.net/dakota/lending/perfios-adapter/api v1.7.2
	gitlab.myteksi.net/dakota/lending/simulator v1.73.0
	gitlab.myteksi.net/dakota/pigeon/api v1.16.2
	gitlab.myteksi.net/dakota/servus v1.64.0
	gitlab.myteksi.net/dakota/servus/v2 v2.54.0
	gitlab.myteksi.net/dakota/verification-provider/api v1.40.0
	gitlab.myteksi.net/dakota/workflowengine v1.21.0
	gitlab.myteksi.net/dbmy/common/active-profile/v2 v2.0.0
	gitlab.myteksi.net/dbmy/customer-experience/api v1.57.4
	gitlab.myteksi.net/dbmy/ekyb-service/api v1.1.1-0.**************-9ab8550236ad
	gitlab.myteksi.net/dbmy/gd-proxy/api v0.37.0
	gitlab.myteksi.net/dbmy/risk-broker/api v1.30.0-dbmy
	gitlab.myteksi.net/gophers/go/commons/data v1.0.11
	gitlab.myteksi.net/gophers/go/commons/util/log/logging v1.1.8
	gitlab.myteksi.net/gophers/go/commons/util/log/yall v1.0.23
	gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent v1.0.0
	gitlab.myteksi.net/gophers/go/commons/util/resilience/circuitbreaker v1.0.10
	gitlab.myteksi.net/gophers/go/commons/util/tags v1.1.11
	gitlab.myteksi.net/gophers/go/commons/util/time/grabtime v1.0.0
	gitlab.myteksi.net/snd/streamsdk v1.0.0
	golang.org/x/crypto v0.32.0
	golang.org/x/exp v0.0.0-20240904232852-e7e105dedf7e
	gopkg.in/stretchr/testify.v1 v1.2.2
)

require (
	github.com/alpeb/go-finance v0.0.0-20211202201625-e4f601ef4382 // indirect
	github.com/aws/aws-sdk-go-v2 v1.36.3 // indirect
	github.com/aws/aws-sdk-go-v2/config v1.29.11 // indirect
	github.com/aws/aws-sdk-go-v2/credentials v1.17.64 // indirect
	github.com/aws/aws-sdk-go-v2/feature/ec2/imds v1.16.30 // indirect
	github.com/aws/aws-sdk-go-v2/internal/configsources v1.3.34 // indirect
	github.com/aws/aws-sdk-go-v2/internal/endpoints/v2 v2.6.34 // indirect
	github.com/aws/aws-sdk-go-v2/internal/ini v1.8.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/accept-encoding v1.12.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/presigned-url v1.12.15 // indirect
	github.com/aws/aws-sdk-go-v2/service/kms v1.38.1 // indirect
	github.com/aws/aws-sdk-go-v2/service/sso v1.25.2 // indirect
	github.com/aws/aws-sdk-go-v2/service/ssooidc v1.29.2 // indirect
	github.com/aws/aws-sdk-go-v2/service/sts v1.33.17 // indirect
	github.com/aws/smithy-go v1.22.2 // indirect
	github.com/cactus/go-statsd-client/v4 v4.0.0 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/grpc-ecosystem/grpc-opentracing v0.0.0-20170512040955-6c130eed1e29 // indirect
	github.com/iancoleman/strcase v0.3.0 // indirect
	github.com/kr/pretty v0.3.1 // indirect
	github.com/lightstep/lightstep-tracer-common/golang/gogo v0.0.0-20210210170715-a8dfcb80d3a7 // indirect
	github.com/lightstep/lightstep-tracer-go v0.25.0 // indirect
	github.com/lufia/plan9stats v0.0.0-20211012122336-39d0f177ccd0 // indirect
	github.com/mattn/go-colorable v0.1.14 // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c // indirect
	github.com/redis/go-redis/v9 v9.7.0 // indirect
	github.com/rogpeppe/go-internal v1.10.0 // indirect
	github.com/shirou/gopsutil/v3 v3.23.7 // indirect
	github.com/shoenig/go-m1cpu v0.1.6 // indirect
	github.com/tklauser/go-sysconf v0.3.11 // indirect
	github.com/tklauser/numcpus v0.6.0 // indirect
	github.com/yusufpapurcu/wmi v1.2.3 // indirect
	gitlab.myteksi.net/bersama/customer-master/api/v2 v2.31.19 // indirect
	gitlab.myteksi.net/bersama/transaction-statements/api v0.0.0-**************-eb28351525ce // indirect
	gitlab.myteksi.net/dakota/application-service/api v0.99.1 // indirect
	gitlab.myteksi.net/dakota/core-banking/account-service/api v1.132.0 // indirect
	gitlab.myteksi.net/dakota/core-banking/product-master/api v1.35.0 // indirect
	gitlab.myteksi.net/dakota/customer-master/api/v2 v2.64.0 // indirect
	gitlab.myteksi.net/dakota/transaction-statements/api v1.23.0 // indirect
	gitlab.myteksi.net/dbmy/application-service/api v1.17.1-dbmy // indirect
	gitlab.myteksi.net/dbmy/transaction-statements/api v1.7.1-dbmy // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/logdefaults v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/network/iputil v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/tracer v1.0.5 // indirect
	gitlab.myteksi.net/spartan/hystrix-go/v2 v2.0.1 // indirect
	gitlab.super-id.net/bersama/core-banking/account-service/api v1.70.0 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-**************-e6fa225c2576 // indirect
)

require (
	github.com/DataDog/datadog-agent/pkg/obfuscate v0.36.1 // indirect
	github.com/DataDog/datadog-go v4.8.3+incompatible
	github.com/DataDog/datadog-go/v5 v5.1.1 // indirect
	github.com/DataDog/sketches-go v1.4.1 // indirect
	github.com/Microsoft/go-winio v0.5.2 // indirect
	github.com/alecholmes/xfccparser v0.1.0 // indirect
	github.com/alecthomas/participle v0.7.1 // indirect
	github.com/aws/aws-sdk-go v1.55.4 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.**************-d8f796af33cc // indirect
	github.com/dgraph-io/ristretto v0.1.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-**************-9f7001d12a5f // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/eapache/go-resiliency v1.2.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-**************-776d5712da21 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/flosch/pongo2 v0.0.0-20200913210552-0d938eb266f3 // indirect
	github.com/fsnotify/fsnotify v1.8.0 // indirect
	github.com/garyburd/redigo v1.6.3 // indirect
	github.com/go-redis/redis/v8 v8.11.5 // indirect
	github.com/go-sql-driver/mysql v1.7.0 // indirect
	github.com/gofrs/uuid v4.2.0+incompatible // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/glog v1.2.4 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/gorilla/mux v1.8.0 // indirect
	github.com/gorilla/schema v1.4.1 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-cleanhttp v0.5.2 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/go-retryablehttp v0.7.7 // indirect
	github.com/hashicorp/go-uuid v1.0.3 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.0.0 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.2 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/jinzhu/copier v0.4.0 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/julienschmidt/httprouter v1.3.0 // indirect
	github.com/klauspost/compress v1.16.7 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/myteksi/schema v0.0.0-20180214071320-149151f79a92 // indirect
	github.com/nxadm/tail v1.4.8 // indirect
	github.com/philhofer/fwd v1.1.1 // indirect
	github.com/pierrec/lz4/v4 v4.1.18 // indirect
	github.com/pkg/errors v0.9.1
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/pquerna/ffjson v0.0.0-20190930134022-aa0246cd15f7 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/reterVision/go-kinesis v0.0.0-**************-c0f0783318c3 // indirect
	github.com/robfig/cron/v3 v3.0.1 // indirect
	github.com/rs/cors v1.11.0 // indirect
	github.com/showa-93/go-mask v0.6.1 // indirect
	github.com/spiffe/go-spiffe/v2 v2.1.1 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/tinylib/msgp v1.1.6 // indirect
	github.com/xdg/scram v0.0.0-**************-7eeb5667e42c // indirect
	github.com/xdg/stringprep v1.0.3 // indirect
	gitlab.myteksi.net/bersama/core-banking/product-master/api v1.24.0 // indirect
	gitlab.myteksi.net/dakota/common/aws v1.4.4 // indirect
	gitlab.myteksi.net/dakota/common/env-injection v1.0.0 // indirect
	gitlab.myteksi.net/dakota/common/secrets-injection v1.0.3 // indirect
	gitlab.myteksi.net/dakota/core-banking/account-service/external v0.0.0-**************-3af0f02a47be // indirect
	gitlab.myteksi.net/dakota/core-banking/deposits-core/api v1.45.0 // indirect
	gitlab.myteksi.net/dakota/core-banking/deposits-exp/api v1.16.0 // indirect
	gitlab.myteksi.net/dakota/core-banking/goal-core/api v1.9.0 // indirect
	gitlab.myteksi.net/dakota/gaia v0.1.1 // indirect
	gitlab.myteksi.net/dakota/hermes/api v1.9.0 // indirect
	gitlab.myteksi.net/dakota/lending/loan-core/api v1.185.1
	gitlab.myteksi.net/dakota/state-machine v1.4.0 // indirect
	gitlab.myteksi.net/dakota/whitelist-service/api v1.31.0
	gitlab.myteksi.net/dbmy/core-banking/account-service/api v1.66.0-dbmy // indirect
	gitlab.myteksi.net/dbmy/core-banking/product-master/api v1.31.0-dbmy // indirect
	gitlab.myteksi.net/dbmy/customer-master/api/v2 v2.58.0-dbmy // indirect
	gitlab.myteksi.net/dbmy/pigeon/api v1.33.0-dbmy // indirect
	gitlab.myteksi.net/gophers/go/commons/algo/cmap v1.0.1 // indirect
	gitlab.myteksi.net/gophers/go/commons/deprecation v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/public/misc/systems v1.0.23 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/aws/grabkinesis v1.0.1 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/config/conf v1.0.2 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/config/mode v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/encoding/grabjson v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/ldflags v1.0.1 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/timerlog v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/monitor/statsd v1.0.10 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/redis/gredis v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/redis/gredismigrate v1.0.1 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/resilience/hystrix v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/server/swaggergen v1.0.4 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/validate v1.1.4 // indirect
	gitlab.myteksi.net/gophers/go/spartan/lechuck v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/staples/gredis3 v1.0.10 // indirect
	gitlab.myteksi.net/gophers/go/staples/logging v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/staples/statsd v1.0.14
	gitlab.myteksi.net/snd/sarama v1.34.0 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.24.0 // indirect
	golang.org/x/net v0.36.0 // indirect
	golang.org/x/sync v0.11.0 // indirect
	golang.org/x/sys v0.30.0 // indirect
	golang.org/x/text v0.22.0 // indirect
	golang.org/x/time v0.8.0 // indirect
	golang.org/x/xerrors v0.0.0-20220907171357-04be3eba64a2 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20241223144023-3abc09e42ca8 // indirect
	google.golang.org/grpc v1.67.3 // indirect
	google.golang.org/protobuf v1.36.1 // indirect
	gopkg.in/DataDog/dd-trace-go.v1 v1.36.2
	gopkg.in/redsync.v1 v1.0.1 // indirect
	gopkg.in/tomb.v1 v1.0.0-20141024135613-dd632973f1e7 // indirect
	gopkg.in/tylerb/graceful.v1 v1.2.15 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
