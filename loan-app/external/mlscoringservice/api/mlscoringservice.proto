syntax = "proto3";

package api;

option go_package = "gitlab.myteksi.net/dakota/lending/loan-app/external/mlscoringservice/api";

import "google/api/annotations.proto";
import "gxs/api/annotations.proto";
import "google/protobuf/struct.proto";

message GetApplicationScoreRequest{
  string applicationID = 1;
  ApplicationType applicationType = 2[(gxs.api.validate) = "required"];
  string  countryCode = 3[(gxs.api.validate) = "string,max=3,required"];
  Channel channel = 4[(gxs.api.validate) = "required"];
  string promoCode = 5;
  string acquisitionChannelID = 6;
  repeated LoanApplicant applicants = 7;
  repeated Product products = 8;
}

message GetApplicationScoreResponse {
  Application application = 1;
}

message LoanApplicant {
  string applicantID = 1[(gxs.api.validate) = "string,required"];
  ApplicantType applicantType = 2[(gxs.api.validate) = "required"];
  IDType IDType = 3;
  string IDNumber = 4[(gxs.api.validate) = "string,max=36"];
  string fullName = 5[(gxs.api.validate) = "string,required"];
  string aliasName = 6[(gxs.api.validate) = "string"];
  string hanyuPinyinName = 7[(gxs.api.validate) = "string"];
  string race = 8[(gxs.api.validate) = "string"];
  bool   existingCustomer = 9[(gxs.api.validate) = "required"];
  string customerRelationshipOpenDate = 10;
  string contactType = 11;
  string contactNumber = 12[(gxs.api.validate) = "string,required"];
  string emailAddress = 13;
  string addressType = 14;
  string houseNumber = 15;
  string unitNumber = 16;
  string buildingName = 17;
  string streetName = 18[(gxs.api.validate) = "string"];
  bool   isStaff = 19;
  string staffGrade = 20;
  Gender gender = 21[(gxs.api.validate) = "required"];
  string dateOfBirth = 22[(gxs.api.validate) = "string,required"];
  string postalCode = 23[(gxs.api.validate) = "string"];
  string state = 24[(gxs.api.validate) = "string"];
  ResidentialStatus residentialStatus = 25;
  string nationality = 26[(gxs.api.validate) = "string,required"];
  string birthCountry = 27;
  string passportExpiryDate = 28;
  string passType = 29;
  string passStatus = 30;
  string passExpiryDate = 31;
  bool isValidEmploymentPass = 32;
  int64 education = 33;
  MaritalStatus maritalStatus = 34[(gxs.api.validate) = "required"];
  EmploymentDetail employmentDetail = 35;
  string residentialCountry = 36;
  bool  isMyInfo = 37;
  int64 housingType = 38;
  int64 hdbType = 39;
  HDBOwnership hdbOwnership = 40;
  CPFAccountBalances cpfAccountBalances = 41;
  bool isLifeInsuranceAvailable = 42;
  Income income = 43;
  repeated CPFHousingWithdrawal cpfHousingWithdrawals = 44;
  bool ownershipOfPrivateResidentialProperty = 45;
  google.protobuf.Struct bureau = 46;
  EcosystemDetail grabDetails = 47;
  EcosystemDetail singtelDetails = 48;
  Grabuserprofiledetails grabUserProfileDetails = 49 [(gxs.api.noomit) = true];
  google.protobuf.Struct experianSMENSReport = 50;
  google.protobuf.Struct experianECSRReport = 51;
  google.protobuf.Struct experianEISReport = 52;
  string entityType = 53;
  RelationToPrimaryType relationToPrimary = 54;
  repeated google.protobuf.Struct perfiosReports = 55;
  google.protobuf.Struct experianCBSCReport = 56;
  google.protobuf.Struct experianIBSReport = 57;
}

message Product {
  ProductType productType = 1[(gxs.api.validate) = "required"];
  SubProductType subProductType = 2;
  double requestedLoanAmount = 3;
  string requestedCurrencyCode = 4[(gxs.api.validate) = "string,max=3"];
  int64 requestedTenure = 5;
  double interestRate = 6;
}

message EmploymentDetail {
  EmploymentType employmentType = 1;
  string occupationCode = 2[(gxs.api.validate) = "string,required"];
  string industryCode = 3;
  int64 lengthOfEmployment = 4;
  int64 totalWorkingExperience = 5;
  string employmentSector = 6[(gxs.api.validate) = "string"];
  string companyCategory = 7;
  string negativeCompanyList = 8;
  string employerName = 9;
  string natureOfBusiness = 10;
}

message HDBOwnership {
  double monthlyLoanInstalmentAmount = 1;
  double outstandingInstalmentAmount = 2;
  string currencyCode = 3;
}

message CPFAccountBalances {
  double ordinaryAccountBalance = 1;
  double specialAccountBalance = 2;
  double medisaveAccountBalance = 3;
  double retirementAccountBalance = 4;
  string currencyCode = 5;
}

message Income {
  repeated IncomeDocument incomeDocuments = 1;
}

message IncomeDocument {
  IncomeDocumentType incomeDocumentType = 1[(gxs.api.validate) = "required"];
  string documentDate = 2;
  int64 yearOfAssessment = 3;
  repeated IncomeComponent incomeComponents = 4;
}

message IncomeComponent {
  string incomeComponentType = 1[(gxs.api.validate) = "regex,pattern=^cpfContribution$|^employment$|^trade$|^rent$|^interest$|^assessableIncome$,required"];
  string incomeCurrency = 2;
  repeated IncomeOccurrence incomeOccurrences = 3;
}

message IncomeOccurrence {
  double incomeAmount = 1 [(gxs.api.noomit) = true];
  string paidForMonth = 2;
  string employerNameFromCPF = 3;
}


message CPFHousingWithdrawal {
  double principalWithdrawalAmount = 1;
  double monthlyInstalmentAmount = 2;
  double accruedInterestAmount = 3;
  double totalAmountOfCPFAllowedForProperty = 4;
  string currencyCode = 5;
}

message EcosystemDetail {
  string hashedCustomerIDLendingOffer = 1;
  bool target = 2;
  string campaignID = 3;
  string placementID = 4;
  double interestRate = 5;
  string miniSegment = 6;
  string clickID = 7;
  google.protobuf.Struct merchantData = 8;
  repeated google.protobuf.Struct storesData = 9;
}

message Grabuserprofiledetails {
  string ecosystemId = 1 [(gxs.api.noomit) = true];
  string grabAddress = 2 [(gxs.api.noomit) = true];
  string grabDateOfBirth = 3 [(gxs.api.noomit) = true];
  string grabEmail = 4 [(gxs.api.noomit) = true];
  string grabGender = 5 [(gxs.api.noomit) = true];
  string grabIdNumber = 6 [(gxs.api.noomit) = true];
  string grabIdType = 7 [(gxs.api.noomit) = true];
  string grabName = 8 [(gxs.api.noomit) = true];
  string grabNationality = 9 [(gxs.api.noomit) = true];
  int64  grabPhoneNumber = 10 [(gxs.api.noomit) = true];
  string grabCustomerRewardTier = 11 [(gxs.api.noomit) = true];
  double grabCreditScore = 12 [(gxs.api.noomit) = true];
  bool grabCustomerCurrentIsBanned = 13 [(gxs.api.noomit) = true];
  double grabAvgDailyBalanceAmountOfGrabWallet30d = 14 [(gxs.api.noomit) = true];
  bool grabCustomerHasGrabpayCard = 15 [(gxs.api.noomit) = true];
  string grabDistCountCardType = 16 [(gxs.api.noomit) = true];
  bool grabPaylaterTransactionIndicator90d = 17 [(gxs.api.noomit) = true];
  double grabPctAmountSpendPassthroughCard30d = 18 [(gxs.api.noomit) = true];
  double grabPctTxnFoodDeliveryCostMoreThan30pctGmv30d = 19 [(gxs.api.noomit) = true];
  double grabPctTxnFoodDeliveryDistLessThan2km90d = 20 [(gxs.api.noomit) = true];
  double grabPctTxnFoodOrdersToCommercialBuilding90d = 21 [(gxs.api.noomit) = true];
  double grabPctTxnFoodOrdersToGovernmentBuilding90d = 22 [(gxs.api.noomit) = true];
  double grabPctTxnFoodOrdersToHome90d = 23 [(gxs.api.noomit) = true];
  double grabPctTxnFoodOrdersToHotel90d = 24 [(gxs.api.noomit) = true];
  double grabPctTxnFoodOrdersToOffice90d = 25 [(gxs.api.noomit) = true];
  double grabPctTxnFoodOrdersToResidential90d = 26 [(gxs.api.noomit) = true];
  double grabPctTxnFoodOrdersToStreet90d = 27 [(gxs.api.noomit) = true];
  double grabPctTxnFoodWeekdayOrders90d = 28 [(gxs.api.noomit) = true];
  double grabPctTxnFoodWeekendOrders90d = 29 [(gxs.api.noomit) = true];
  string grabCustomerAffluenceSegment = 30 [(gxs.api.noomit) = true];
  int64 grabPercentileOfSumGmvSgd90d = 31 [(gxs.api.noomit) = true];
  int64 grabPercentileOfSumGmvSgd180d = 32 [(gxs.api.noomit) = true];
  int64 grabPercentileTotalCountOfPurchase90d = 33 [(gxs.api.noomit) = true];
  bool grabPaylaterActivatonEver = 34 [(gxs.api.noomit) = true];
  string grabRegisterDate = 35 [(gxs.api.noomit) = true];
  string grabSegmentClmGfWs = 36 [(gxs.api.noomit) = true];
  string grabSegmentClmGpAll = 37 [(gxs.api.noomit) = true];
  string grabSegmentClmGpP2mGmv = 38 [(gxs.api.noomit) = true];
  string grabSegmentClmGpP2mRecency = 39 [(gxs.api.noomit) = true];
  string grabSegmentClmGpP2mTxn = 40 [(gxs.api.noomit) = true];
  string grabSegmentClmWalletBalance = 41 [(gxs.api.noomit) = true];
  double grabTotalAmountP2mTpvGrabpay90d = 42 [(gxs.api.noomit) = true];
  double grabTotalAmountSpendCash90d = 43 [(gxs.api.noomit) = true];
  double grabTotalAmountSpendCreditCard90d = 44 [(gxs.api.noomit) = true];
  double grabPredictedCurrentAllverticalsProbabilityChurn12m = 45 [(gxs.api.noomit) = true];
  int64 grabTotalCountDailyGrabpayTransaction90d = 46 [(gxs.api.noomit) = true];
  int64 grabTotalCountGdistinctCompletedChallenges90d = 47 [(gxs.api.noomit) = true];
  int64 grabTotalCountGrabMart90d = 48 [(gxs.api.noomit) = true];
  int64 grabTotalCountOfGrabWalletTopUp90d = 49 [(gxs.api.noomit) = true];
  int64 grabTotalCountP2mTransactionGrabpay90d = 50 [(gxs.api.noomit) = true];
  int64 grabTotalCountSgpPrimaryCreditCardUsages365d = 51 [(gxs.api.noomit) = true];
  int64 grabTotalCountSgpPrimaryCreditCardUsages90d = 52 [(gxs.api.noomit) = true];
  int64 grabTotalCountTransportRides90d = 53 [(gxs.api.noomit) = true];
  int64 grabTotalCountTransportRidesFromBarPubClub30d = 54 [(gxs.api.noomit) = true];
  int64 grabTotalCountTransportRidesFromCasino30d = 55 [(gxs.api.noomit) = true];
  int64 grabTotalCountTransportRidesToBarPubClub30d = 56 [(gxs.api.noomit) = true];
  int64 grabTotalCountTransportRidesToCasino30d = 57 [(gxs.api.noomit) = true];
  int64 grabTotalCountTxnCash30d = 58 [(gxs.api.noomit) = true];
  int64 grabTotalCountTxnCreditCard7d = 59 [(gxs.api.noomit) = true];
  double grabTotalFoodGmvUsd90d = 60 [(gxs.api.noomit) = true];
  double grabTotalTransportGmvUsd90d = 61 [(gxs.api.noomit) = true];
  double grabTotalGrabexpressGmvUsd90d = 62 [(gxs.api.noomit) = true];
  double grabTotalGrabmartGmvUsd90d = 63 [(gxs.api.noomit) = true];
  int64 grabTotalCountOfCcBins = 64 [(gxs.api.noomit) = true];
  double grabTotalAmountPaylaterPurchase90d = 65 [(gxs.api.noomit) = true];
  double grabAvgDailyBalanceActualAmountOfGrabWallet90d = 66 [(gxs.api.noomit) = true];
  bool grabPaylaterPurchasedEver = 67 [(gxs.api.noomit) = true];
  double grabPctBnplPurchaseOfGmv90d = 68 [(gxs.api.noomit) = true];
  string grabPredictedCurrentMostPreferredTaxiType = 69 [(gxs.api.noomit) = true];
  string grabTelcoProvider = 70 [(gxs.api.noomit) = true];
  double grabTotalAmountSpendOnGrabmart90d = 71 [(gxs.api.noomit) = true];
  bool grabTotalCountClickTileGrabPaylater90d = 72 [(gxs.api.noomit) = true];
  int64 grabTotalCountGrabpayTransaction30d = 73 [(gxs.api.noomit) = true];
  int64 grabTotalCountGrabpayTransaction90d = 74 [(gxs.api.noomit) = true];
  int64 grabTotalCountDistinctCompletedChallenges90d = 75 [(gxs.api.noomit) = true];
  int64 grabTotalCountNumberOfAmexcard = 76 [(gxs.api.noomit) = true];
  int64 grabTotalCountNumberOfChinaunionpaycard = 77 [(gxs.api.noomit) = true];
  int64 grabTotalCountNumberOfMastercard = 78 [(gxs.api.noomit) = true];
  int64 grabTotalCountNumberOfVisacard = 79 [(gxs.api.noomit) = true];
  int64 grabTotalCountTransportRidesAfternoon90d = 80 [(gxs.api.noomit) = true];
  int64 grabTotalCountTransportRidesBusiness90d = 81 [(gxs.api.noomit) = true];
  int64 grabTotalCountTransportRidesFromAirport90d = 82 [(gxs.api.noomit) = true];
  int64 grabTotalCountTransportRidesFromEducation90d = 83 [(gxs.api.noomit) = true];
  int64 grabTotalCountTransportRidesLateNight90d = 84 [(gxs.api.noomit) = true];
  int64 grabTotalCountTransportRidesToAirport90d = 85 [(gxs.api.noomit) = true];
  int64 grabTotalCountTransportRidesToEducation90d = 86 [(gxs.api.noomit) = true];
  int64 grabTotalCountTransportRidesToLibrary90d = 87 [(gxs.api.noomit) = true];
  int64 grabTotalCountTransportRidesToPolice90d = 88 [(gxs.api.noomit) = true];
  int64 grabTotalCountTransportRidesToSportsRecreationCenter90d = 89 [(gxs.api.noomit) = true];
  bool grabIsDax = 90 [(gxs.api.noomit) = true];
  string grabHashNricId = 91 [(gxs.api.noomit) = true];
  string grabPs11HashId = 92 [(gxs.api.noomit) = true];
  bool grabDaxIsDax = 93 [(gxs.api.noomit) = true];
  string grabDaxGroup = 94 [(gxs.api.noomit) = true];
  bool grabDaxIsPilot = 95 [(gxs.api.noomit) = true];
  string grabDaxIdNumber = 96 [(gxs.api.noomit) = true];
  int64 grabDaxIdType = 97 [(gxs.api.noomit) = true];
  string grabDaxRiskSegment = 98 [(gxs.api.noomit) = true];
  double grabDaxPcaOutstandingBalance = 99 [(gxs.api.noomit) = true];
  double grabDaxPcaLimitAssigned = 100 [(gxs.api.noomit) = true];
  double grabDaxTotalAmountTransportFareEarnings30d = 101 [(gxs.api.noomit) = true];
  double grabDaxTotalAmountTransportFareEarnings60d = 102 [(gxs.api.noomit) = true];
  double grabDaxTotalAmountTransportFareEarnings90d = 103 [(gxs.api.noomit) = true];
  double grabDaxTotalAmountTransportFareEarnings120d = 104 [(gxs.api.noomit) = true];
  double grabDaxTotalAmountTransportFareEarnings150d = 105 [(gxs.api.noomit) = true];
  double grabDaxTotalAmountTransportFareEarnings180d = 106 [(gxs.api.noomit) = true];
  string grabDaxOnboardedDate = 107 [(gxs.api.noomit) = true];
  string grabDaxLatestRideDate = 108 [(gxs.api.noomit) = true];
  string grabDaxPcaDateOfPcaLimitGranted = 109 [(gxs.api.noomit) = true];
  double grabDaxPcaLoanMaxWeeklyInstallmentAmount = 110 [(gxs.api.noomit) = true];
  string grabDaxPcaLoanMaxLoanMaturityDate = 111 [(gxs.api.noomit) = true];
  int64 grabDaxPcaLoanMaxTenureInWeeks = 112 [(gxs.api.noomit) = true];
  int64 grabDaxPcaLoanMaxDpd = 113 [(gxs.api.noomit) = true];
  double grabDaxPcaOverdueAmount = 114 [(gxs.api.noomit) = true];
  string grabDaxPcaLatestDrawdownDate = 115 [(gxs.api.noomit) = true];
  string grabDaxPcaFirstDrawdownDate = 116 [(gxs.api.noomit) = true];
  double onaDpd = 117 [(gxs.api.noomit) = true];
  double paylaterMaxDpdL3MCnt = 118 [(gxs.api.noomit) = true];
  double paylaterCreditLimit = 119 [(gxs.api.noomit) = true];
  string grabDeviceModel = 120 [(gxs.api.noomit) = true];
  double paylaterAvailableLimit = 121 [(gxs.api.noomit) = true];
  double grabTotalCountOfCcBins180D = 122  [(gxs.api.noomit) = true];
  double onaBillPayL1MCnt = 123 [(gxs.api.noomit) = true];
  double onaAvailableLimit = 124 [(gxs.api.noomit) = true];
  double paylaterBillDueLcy = 125 [(gxs.api.noomit) = true];
  double paylaterAvgOutstandingPrincipleLcyL3M = 126 [(gxs.api.noomit) = true];
  double grabTotalCountTransportRidesToBarPubClub90D = 127 [(gxs.api.noomit) = true];
  double paylaterMaxOutstandingUtilizeL3M = 128 [(gxs.api.noomit) = true];
}

message Error {
  string errorCode = 1[(gxs.api.validate) = "string,required"];
  string errorDescription = 2;
}

message Application {
  string applicationID = 1;
  repeated Applicant applicants = 2;

}

message Applicant{
  string applicantID = 1;
  repeated ModelDetail modelDetails = 2;
}

message ModelDetail{
  string modelId = 1;
  string modelName = 2;
  double pdValue = 3;
  double score = 4;
  string scoreGrade = 5;
  string modelSegment = 6;
}

// ApplicationType : application type of flexicard and flexiloan
enum ApplicationType {
  NEW = 0;
  REVIEW = 1;
}

// Channel : channel of request
enum Channel {
  GXS = 0;
  MSTA = 1;
  GRAB = 2;
  SUPERBANK = 3;
  SINGTEL = 4;
}

// ProductType : product type
enum ProductType {
  FLEXI_LOAN_LINE_OF_CREDIT = 0;
  FLEXI_CREDIT_CARD = 1;
}

// SubProductType : sub product type
enum SubProductType {
  DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT = 0;
  DEFAULT_FLEXI_CREDIT_CARD = 1;
}

enum ApplicantType {
  PRIMARY = 0;
  JOINT = 1;
  GUARANTOR = 2;
}

enum Gender {
  MALE = 0;
  FEMALE = 1;
  UNKNOWN = 2;
}

enum IDType {
  EMPL = 0;
  NRIC = 1;
  PASS = 2;
  UNKN = 3;
  WORK = 4;
  KTP = 5;
  NPWP = 6;
}
enum ResidentialStatus {
  A = 0;
  C = 1;
  P = 2;
  U = 3;
  N = 4;
}
enum MaritalStatus {
  SINGLE = 0;
  MARRIED = 1;
  WIDOWED = 2;
  DIVORCED = 3;
}

enum EmploymentType {
  SA = 0;
  SE = 1;
  GIG = 2;
}


enum IncomeDocumentType{
  cpf = 0;
  noa = 1;
  salary = 2;
  ecosystem = 3;
}

enum RelationToPrimaryType{
  SELF = 0;
  OWNER = 1;
}

service MlScoringService {
  rpc GetApplicationScore(GetApplicationScoreRequest) returns (GetApplicationScoreResponse) {
    option (google.api.http) = {
      post: "/",
      body: "*",
    };
  }
}