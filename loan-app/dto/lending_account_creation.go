package dto

import (
	"time"

	"gitlab.com/gx-regional/dakota/schemas/streams/apis/streaminfo"
)

type LendingAccountCreationDTO struct {
	StreamInfo              streaminfo.StreamInfo
	ReferenceID             string
	ApplicationID           string
	ParentAccountID         string
	AccountID               string
	ProductID               string
	ProductVariantCode      string
	ProductVersionID        string
	PermittedCurrencies     []string
	CifNumber               string
	Status                  string
	StatusReasonDescription string
	OpeningTimestamp        *time.Time
	InstanceParams          map[string]string
	CreatedBy               string
}
