package dto

import incomeDerivationEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/income_derivation_event"

// IncomeDerivationStreamMessage ...
type IncomeDerivationStreamMessage struct {
	ReferenceID          string                             `json:"refID,omitempty"`
	ApplicationID        string                             `json:"applicationID,omitempty"`
	DocumentType         incomeDerivationEvent.DocumentType `json:"docType,omitempty"`
	Status               incomeDerivationEvent.Status       `json:"status,omitempty"`
	StatusReason         incomeDerivationEvent.StatusReason `json:"statusReason,omitempty"`
	UserDetails          *UserDetails                       `json:"userDetails,omitempty"`
	Metadata             *IncomeDerivationMetadata          `json:"metadata,omitempty"`
	IncomeAmount         float64                            `json:"incomeAmount,omitempty"`
	IncomeDocumentSource string                             `json:"DocSource,omitempty"`
	BankStatementReport  []byte                             `json:"bankStatementReport,omitempty"`
}

// UserDetails ...
type UserDetails struct {
	Name string `json:"name,omitempty"`
}

// IncomeDerivationMetadata ...
type IncomeDerivationMetadata struct {
	IncomeAlert                           bool    `json:"incomeAlert"`
	SixMonthsEPF                          bool    `json:"sixMonthsEPF"`
	PerfiosStatementStatus                string  `json:"perfiosStatementStatus"`
	TotalEPFContributionAmount            float64 `json:"totalEPFContributionAmount"`
	EmployerNameMatch                     *bool   `json:"employerNameMatch"`
	PositiveKeywordMatch                  *bool   `json:"positiveKeywordMatch"`
	NegativeKeywordMatch                  *bool   `json:"negativeKeywordMatch"`
	DepositDescriptionConsistency         *bool   `json:"depositDescriptionConsistency"`
	CreditAmountConsistency               *bool   `json:"creditAmountConsistency"`
	CreditDateConsistency                 *bool   `json:"creditDateConsistency"`
	StatementAccountHolderNameConsistency *bool   `json:"statementAccountHolderNameConsistency"`
	StatementAccountNoConsistency         *bool   `json:"statementAccountNoConsistency"`
	SecondaryStatementStatus              *string `json:"secondaryStatementStatus"`
}
