// Package mapper ...
// nolint:gosec
package mapper

import (
	"context"
	"encoding/json"
	"fmt"
	"math"

	"github.com/shopspring/decimal"

	creditDecisionEngineAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	mlScoringAPI "gitlab.com/gx-regional/dakota/lending/loan-app/external/mlscoringservice/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils/kms"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils/mapper"
	"gitlab.myteksi.net/dakota/common/servicename"
	experianAPI "gitlab.myteksi.net/dakota/experian-adapter/api"
	"gitlab.myteksi.net/dakota/lending/common/countries"
	grabAPI "gitlab.myteksi.net/dakota/lending/external/grab/api"
	"gitlab.myteksi.net/dakota/servus/slog"
	whitelistAPI "gitlab.myteksi.net/dakota/whitelist-service/api"
)

// MapPreBureauCreditDecisionRequest preBureauCDE request mapper function
func MapPreBureauCreditDecisionRequest(dto dto.CreditDecisionRequestMapperDTO) *creditDecisionEngineAPI.FICOCreditDecisionRequest {
	callType := creditDecisionEngineAPI.CallType(constants.PreBureau)
	applicationType := creditDecisionEngineAPI.ApplicationType(dto.ApplicationType)
	channel := creditDecisionEngineAPI.Channel(dto.Channel)
	ficoCreditDecisionRequest := &creditDecisionEngineAPI.FICOCreditDecisionRequest{
		Message: &creditDecisionEngineAPI.Message{
			CallType: &callType,
			Application: &creditDecisionEngineAPI.Application{
				ApplicationID:        &dto.ApplicationID,
				ApplicationType:      &applicationType,
				AppCreationDate:      &dto.AppCreationDate,
				CountryCode:          &dto.CountryCode,
				Channel:              &channel,
				Applicants:           MapApplicants(dto),
				Products:             MapProducts(dto),
				PromoCode:            &dto.PromoCode,
				AcquisitionChannelID: &dto.AcquisitionChannelID,
				PreviousApplications: MapPreviousApplications(dto.PreviousApplications),
			},
			ReferenceID: &dto.ReferenceID,
		},
	}
	return ficoCreditDecisionRequest
}

// MapPostBureauCreditDecisionRequest postBureauCDE request mapper function
func MapPostBureauCreditDecisionRequest(dto dto.CreditDecisionRequestMapperDTO) *creditDecisionEngineAPI.FICOCreditDecisionRequest {
	postBureauCreditDecisionRequest := MapPreBureauCreditDecisionRequest(dto)

	//Set call type, transition id
	callType := creditDecisionEngineAPI.CallType("POST_BUREAU")
	postBureauCreditDecisionRequest.Message.CallType = &callType
	postBureauCreditDecisionRequest.Message.ReferenceID = &dto.ReferenceID

	//Set bureau, model details only for non biz applicant
	for i := range postBureauCreditDecisionRequest.Message.Application.Applicants {
		entityType := postBureauCreditDecisionRequest.Message.Application.Applicants[i].EntityType
		if entityType != nil && *entityType == creditDecisionEngineAPI.EntityType_BUSINESS {
			continue
		}
		marshalledReport, err := common.DecryptIDNumberInCBSData(context.Background(), dto.Report["RSP_REPORT"], dto.EnableEncryptionFlags)
		if err != nil {
			continue
		}

		s := []byte(marshalledReport)
		m := make(map[string]interface{})
		_ = json.Unmarshal(s, &m)
		postBureauCreditDecisionRequest.Message.Application.Applicants[i].Bureau = map[string]interface{}{
			"RSP_REPORT":        m,
			"bureauEnquiryDate": dto.BureauEnquiryDate,
		}
		if dto.ApplicationScoreResponse != nil {
			for _, applicant := range dto.ApplicationScoreResponse.Application.Applicants {
				if applicant.ApplicantID == *postBureauCreditDecisionRequest.Message.Application.Applicants[i].ApplicantID {
					var modelDetails []creditDecisionEngineAPI.ModelDetail
					for _, modelDetail := range applicant.ModelDetails {
						modelDetails = append(modelDetails, mapModelDetail(modelDetail))
					}
					postBureauCreditDecisionRequest.Message.Application.Applicants[i].ModelDetails = modelDetails
					break
				}
			}
		}
	}
	return postBureauCreditDecisionRequest
}

func mapModelDetail(detail mlScoringAPI.ModelDetail) creditDecisionEngineAPI.ModelDetail {
	modelDetail := creditDecisionEngineAPI.ModelDetail{}
	marshal, _ := json.Marshal(detail)
	_ = json.Unmarshal(marshal, &modelDetail)
	return modelDetail
}

// MapFinalDecisionCreditDecisionRequest update cde with final decision mapper
func MapFinalDecisionCreditDecisionRequest(dto dto.CreditDecisionRequestMapperDTO) *creditDecisionEngineAPI.FICOCreditDecisionRequest {
	finalDecisionCreditDecisionRequest := MapPostBureauCreditDecisionRequest(dto)

	//Set call type, transition id
	callType := creditDecisionEngineAPI.CallType("FINAL_DECISION")
	finalDecisionCreditDecisionRequest.Message.CallType = &callType
	finalDecisionCreditDecisionRequest.Message.ReferenceID = &dto.ReferenceID
	products := finalDecisionCreditDecisionRequest.Message.Application.Products
	for index := range products {
		products[index].ProductDecision = &creditDecisionEngineAPI.ProductDecision{
			FinalDecision: &creditDecisionEngineAPI.FinalDecision{
				FinalDecision:      &dto.FinalDecision,
				FinalAcceptedLimit: &dto.FinalAcceptedLimit,
			},
		}
	}
	return finalDecisionCreditDecisionRequest
}

// MapPostIncomeDerivationCreditDecisionRequest income derivation cde mapper
func MapPostIncomeDerivationCreditDecisionRequest(dto dto.CreditDecisionRequestMapperDTO, enableBizFlexiCreditCdePostIncomeApplicantIncome bool, isBizFlexiCreditApplication bool) *creditDecisionEngineAPI.FICOCreditDecisionRequest {
	postIncomeDerivationCreditDecisionRequest := MapPostBureauCreditDecisionRequest(dto)

	//Set call type, transition id
	callType := creditDecisionEngineAPI.CallType_POST_INCOME_DERIVATION
	postIncomeDerivationCreditDecisionRequest.Message.CallType = &callType
	postIncomeDerivationCreditDecisionRequest.Message.ReferenceID = &dto.ReferenceID

	//Set perfios report for only biz applicant
	for i := range postIncomeDerivationCreditDecisionRequest.Message.Application.Applicants {
		entityType := postIncomeDerivationCreditDecisionRequest.Message.Application.Applicants[i].EntityType
		if entityType != nil && *entityType == creditDecisionEngineAPI.EntityType_BUSINESS {
			if enableBizFlexiCreditCdePostIncomeApplicantIncome {
				postIncomeDerivationCreditDecisionRequest.Message.Application.Applicants[i].Income = getIncomeDetails(dto.IncomeDerivationStreamMessage, isBizFlexiCreditApplication)
			}
			if len(dto.IncomeDerivationStreamMessage.BankStatementReport) > 0 {
				var perfiosReport map[string]interface{}
				if err := json.Unmarshal(dto.IncomeDerivationStreamMessage.BankStatementReport, &perfiosReport); err == nil {
					postIncomeDerivationCreditDecisionRequest.Message.Application.Applicants[i].PerfiosReports = []map[string]interface{}{perfiosReport}
				}
			}
		}
	}
	return postIncomeDerivationCreditDecisionRequest
}

// MapApplicants applicant mapper function
// nolint:funlen
func MapApplicants(dto dto.CreditDecisionRequestMapperDTO) []creditDecisionEngineAPI.Applicant {
	var applicants []creditDecisionEngineAPI.Applicant
	loanApplicants := dto.LoanApplicants
	for index := range loanApplicants {
		applicantType := creditDecisionEngineAPI.ApplicantType(loanApplicants[index].ApplicantType)
		gender := creditDecisionEngineAPI.Gender(loanApplicants[index].Gender)
		status := creditDecisionEngineAPI.ResidentialStatus(loanApplicants[index].ResidentialStatus)
		maritalStatus := creditDecisionEngineAPI.MaritalStatus(loanApplicants[index].MaritalStatus)
		creditDecisionEngineAPIApplicant := creditDecisionEngineAPI.Applicant{
			ApplicantID:                  &loanApplicants[index].ApplicantID,
			ApplicantType:                &applicantType,
			ExistingCustomer:             &loanApplicants[index].ExistingCustomer,
			CustomerRelationshipOpenDate: &loanApplicants[index].CustomerRelationshipOpenDate,
			IsStaff:                      &loanApplicants[index].IsStaff,
			StaffGrade:                   &loanApplicants[index].StaffGrade,
			DateOfBirth:                  &loanApplicants[index].DateOfBirth,
			Gender:                       &gender,
			Nationality:                  &loanApplicants[index].Nationality,
			ResidentialStatus:            &status,
			BirthCountry:                 &loanApplicants[index].BirthCountry,
			PassportExpiryDate:           &loanApplicants[index].PassportExpiryDate,
			PassType:                     &loanApplicants[index].PassType,
			PassStatus:                   &loanApplicants[index].PassStatus,
			PassExpiryDate:               &loanApplicants[index].PassExpiryDate,
			IsValidEmploymentPass:        &loanApplicants[index].IsValidEmploymentPass,
			Education:                    &loanApplicants[index].Education,
			MaritalStatus:                &maritalStatus,
			ResidentialCountry:           &loanApplicants[index].ResidentialCountry,
			PostalCode:                   &loanApplicants[index].PostalCode,
			IsMyInfo:                     &loanApplicants[index].IsMyInfo,
			HousingType:                  &loanApplicants[index].HousingType,
			HdbType:                      &loanApplicants[index].HdbType,
			IsLifeInsuranceAvailable:     &loanApplicants[index].IsLifeInsuranceAvailable,
			EmploymentDetail:             mapEmploymentDetail(loanApplicants[index]),
			Income:                       mapIncome(loanApplicants[index]),
			GrabDetails:                  mapEcosystemData(loanApplicants[index].ApplicantID, loanApplicants[index].GrabDetails, dto.EcosystemDetails, string(api.Channel_GRAB)),
			SingtelDetails:               mapEcosystemData(loanApplicants[index].ApplicantID, loanApplicants[index].SingtelDetails, dto.EcosystemDetails, string(api.Channel_SINGTEL)),
		}

		switch dto.Products[0].ProductType {
		case api.ProductType_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT:
			creditDecisionEngineAPIApplicant = appendBizFlexiCreditData(creditDecisionEngineAPIApplicant, loanApplicants[index], dto)
		}
		applicants = append(applicants, creditDecisionEngineAPIApplicant)
	}
	return applicants
}

// nolint:funlen
func appendBizFlexiCreditData(creditDecisionEngineAPIApplicant creditDecisionEngineAPI.Applicant, applicant api.FlexiTermLoanApplicant, dto dto.CreditDecisionRequestMapperDTO) creditDecisionEngineAPI.Applicant {
	var entityType creditDecisionEngineAPI.EntityType
	if applicant.EntityType != "" {
		entityType = creditDecisionEngineAPI.EntityType(applicant.EntityType)
	}
	var relationToPrimary creditDecisionEngineAPI.RelationToPrimary
	if applicant.RelationToPrimary != "" {
		relationToPrimary = creditDecisionEngineAPI.RelationToPrimary(applicant.RelationToPrimary)
	}
	var applicantType creditDecisionEngineAPI.ApplicantType
	if applicant.ApplicantType != "" {
		applicantType = creditDecisionEngineAPI.ApplicantType(applicant.ApplicantType)
	}

	idNumber := applicant.IDNumber
	var err error
	if dto.EnableEncryptionFlags.EnableIDNumberEncryption && dto.EnableEncryptionFlags.EnableSwitchToHashedIDNumber {
		ctx := context.Background()
		idNumber, err = kms.KMSClient.Decrypt(ctx, applicant.EncryptedIDNumber)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.KMSClientLogTag, fmt.Sprintf("Decrypting failed for EncryptedIDNumber %s with err %s", applicant.EncryptedIDNumber, err))
		}
	}

	switch entityType {
	case creditDecisionEngineAPI.EntityType_INDIVIDUAL:
		creditDecisionEngineAPIApplicant.ApplicantID = &applicant.ApplicantID
		creditDecisionEngineAPIApplicant.EntityType = &entityType
		creditDecisionEngineAPIApplicant.RelationToPrimary = &relationToPrimary
		creditDecisionEngineAPIApplicant.IDType = utils.GetPointer(string(applicant.IDType))
		creditDecisionEngineAPIApplicant.IDNumber = &idNumber
		creditDecisionEngineAPIApplicant.ApplicantType = &applicantType
		creditDecisionEngineAPIApplicant.FullName = &applicant.FullName

		switch dto.CountryCode {
		case countries.SG:
			handleIndividualSG(&creditDecisionEngineAPIApplicant, applicant, dto)

		case countries.MY:
			handleIndividualMY(&creditDecisionEngineAPIApplicant, applicant)
		}

	case creditDecisionEngineAPI.EntityType_BUSINESS:
		creditDecisionEngineAPIApplicant.ApplicantID = &applicant.ApplicantID
		creditDecisionEngineAPIApplicant.EntityType = &entityType
		creditDecisionEngineAPIApplicant.RelationToPrimary = &relationToPrimary
		creditDecisionEngineAPIApplicant.IDType = utils.GetPointer(string(applicant.IDType))
		creditDecisionEngineAPIApplicant.IDNumber = &idNumber
		creditDecisionEngineAPIApplicant.ApplicantType = &applicantType
		creditDecisionEngineAPIApplicant.GrabDetails = fetchEcosystemDataForBizFlexiCredit(applicant.ApplicantID, dto.EcosystemDetails, string(api.Channel_GRAB), entityType)

		switch dto.CountryCode {
		case countries.SG:
			handleBusinessSG(&creditDecisionEngineAPIApplicant, applicant, dto)

		case countries.MY:
			handleBusinessMY(&creditDecisionEngineAPIApplicant, applicant)
		}
	}
	return creditDecisionEngineAPIApplicant
}

func handleIndividualSG(creditDecisionEngineAPIApplicant *creditDecisionEngineAPI.Applicant, applicant api.FlexiTermLoanApplicant, dto dto.CreditDecisionRequestMapperDTO) {
	var hdbOwnership creditDecisionEngineAPI.HDBOwnership
	if applicant.HdbOwnership != nil {
		hdbOwnership = creditDecisionEngineAPI.HDBOwnership(*applicant.HdbOwnership)
	}
	var cpfAccountBalances creditDecisionEngineAPI.CPFAccountBalances
	if applicant.CpfAccountBalances != nil {
		cpfAccountBalances = creditDecisionEngineAPI.CPFAccountBalances(*applicant.CpfAccountBalances)
	}
	creditDecisionEngineAPIApplicant.AliasName = &applicant.AliasName
	creditDecisionEngineAPIApplicant.HanyuPinyinName = &applicant.HanyuPinyinName
	creditDecisionEngineAPIApplicant.Race = &applicant.Race
	creditDecisionEngineAPIApplicant.ContactType = &applicant.ContactType
	creditDecisionEngineAPIApplicant.ContactNumber = &applicant.ContactNumber
	creditDecisionEngineAPIApplicant.EmailAddress = &applicant.EmailAddress
	creditDecisionEngineAPIApplicant.AddressType = &applicant.AddressType
	creditDecisionEngineAPIApplicant.HouseNumber = &applicant.HouseNumber
	creditDecisionEngineAPIApplicant.UnitNumber = &applicant.UnitNumber
	creditDecisionEngineAPIApplicant.BuildingName = &applicant.BuildingName
	creditDecisionEngineAPIApplicant.StreetName = &applicant.StreetName
	creditDecisionEngineAPIApplicant.State = &applicant.State
	creditDecisionEngineAPIApplicant.HdbOwnership = &hdbOwnership
	creditDecisionEngineAPIApplicant.CpfAccountBalances = &cpfAccountBalances
	creditDecisionEngineAPIApplicant.CpfHousingWithdrawals = mapCpfWithdrawals(applicant.CpfHousingWithdrawals)
	creditDecisionEngineAPIApplicant.OwnershipOfPrivateResidentialProperty = &applicant.OwnershipOfPrivateResidentialProperty
	creditDecisionEngineAPIApplicant.ExperianEISReport = processExperianReport(dto.ExperianEISReport)
	creditDecisionEngineAPIApplicant.ExperianIBSReport = processExperianReport(dto.ExperianIBSReport)
	creditDecisionEngineAPIApplicant.GrabDetails = fetchEcosystemDataForBizFlexiCredit(applicant.ApplicantID, dto.EcosystemDetails, string(api.Channel_GRAB), creditDecisionEngineAPI.EntityType_INDIVIDUAL)
}

func handleIndividualMY(creditDecisionEngineAPIApplicant *creditDecisionEngineAPI.Applicant, applicant api.FlexiTermLoanApplicant) {
	creditDecisionEngineAPIApplicant.DateOfBirth = &applicant.DateOfBirth
	creditDecisionEngineAPIApplicant.IsResident = &applicant.IsResident
	creditDecisionEngineAPIApplicant.DepositInsuranceScheme = &applicant.DepositInsuranceScheme
	creditDecisionEngineAPIApplicant.AllowMarketingConsent = &applicant.AllowMarketingConsent
	creditDecisionEngineAPIApplicant.AllowDataSharing = &applicant.AllowDataSharing
	creditDecisionEngineAPIApplicant.AllowCreditBureauDataSharing = &applicant.AllowCreditBureauDataSharing
	creditDecisionEngineAPIApplicant.DeclareNotUSTaxResident = &applicant.DeclareNotUSTaxResident
	creditDecisionEngineAPIApplicant.ResidentialCountry = utils.GetPointer(countries.MY)
}

func handleBusinessSG(creditDecisionEngineAPIApplicant *creditDecisionEngineAPI.Applicant, applicant api.FlexiTermLoanApplicant, dto dto.CreditDecisionRequestMapperDTO) {
	var businessAnnualIncome creditDecisionEngineAPI.BusinessAnnualIncome
	if applicant.BusinessAnnualIncome != "" {
		businessAnnualIncome = creditDecisionEngineAPI.BusinessAnnualIncome(applicant.BusinessAnnualIncome)
	}
	var anticipatedAccountActivity creditDecisionEngineAPI.AnticipatedAccountActivity
	if applicant.AnticipatedAccountActivity != "" {
		anticipatedAccountActivity = creditDecisionEngineAPI.AnticipatedAccountActivity(applicant.AnticipatedAccountActivity)
	}
	var anticipatedAmountPerTransaction creditDecisionEngineAPI.AnticipatedAmountPerTransaction
	if applicant.AnticipatedAmountPerTransaction != "" {
		anticipatedAmountPerTransaction = creditDecisionEngineAPI.AnticipatedAmountPerTransaction(applicant.AnticipatedAmountPerTransaction)
	}
	var businessPurpose creditDecisionEngineAPI.BusinessPurpose
	if applicant.BusinessPurpose != "" {
		businessPurpose = creditDecisionEngineAPI.BusinessPurpose(applicant.BusinessPurpose)
	}
	creditDecisionEngineAPIApplicant.AnticipatedAccountActivity = &anticipatedAccountActivity
	creditDecisionEngineAPIApplicant.AnticipatedAmountPerTransaction = &anticipatedAmountPerTransaction
	creditDecisionEngineAPIApplicant.BusinessSof = &applicant.BusinessSof
	creditDecisionEngineAPIApplicant.BusinessMobile = &applicant.BusinessMobile
	creditDecisionEngineAPIApplicant.BusinessEmail = &applicant.BusinessEmail
	creditDecisionEngineAPIApplicant.BusinessPurpose = &businessPurpose
	creditDecisionEngineAPIApplicant.BusinessAnnualIncome = &businessAnnualIncome
	creditDecisionEngineAPIApplicant.ExperianECSRReport = processExperianReport(dto.ExperianECSRReport)
	creditDecisionEngineAPIApplicant.ExperianSMENSReport = processExperianReport(dto.ExperianSMENSReport)
	creditDecisionEngineAPIApplicant.ExperianCBSCReport = processExperianReport(dto.ExperianCBSCReport)
	creditDecisionEngineAPIApplicant.SingtelDetails = fetchEcosystemDataForBizFlexiCredit(applicant.ApplicantID, dto.EcosystemDetails, string(api.Channel_SINGTEL), creditDecisionEngineAPI.EntityType_BUSINESS)
}

func handleBusinessMY(creditDecisionEngineAPIApplicant *creditDecisionEngineAPI.Applicant, applicant api.FlexiTermLoanApplicant) {
	creditDecisionEngineAPIApplicant.BizName = &applicant.BizName
	creditDecisionEngineAPIApplicant.BizNature = &applicant.BizNature
	creditDecisionEngineAPIApplicant.BizOwnerName = &applicant.BizOwnerName
	creditDecisionEngineAPIApplicant.BizOwnerNric = &applicant.BizOwnerNric
	creditDecisionEngineAPIApplicant.BizAddressLine1 = &applicant.BizAddressLine1
	creditDecisionEngineAPIApplicant.BizAddressLine2 = &applicant.BizAddressLine2
	creditDecisionEngineAPIApplicant.BizAddressPostcode = &applicant.BizAddressPostcode
	creditDecisionEngineAPIApplicant.BizBrandName = &applicant.BizBrandName
	creditDecisionEngineAPIApplicant.BizEmail = &applicant.BizEmail
	creditDecisionEngineAPIApplicant.BizAddressCity = &applicant.BizAddressCity
	creditDecisionEngineAPIApplicant.BizAddressState = &applicant.BizAddressState
	creditDecisionEngineAPIApplicant.BizAnnualSales = &applicant.BizAnnualSales
	creditDecisionEngineAPIApplicant.BizNoOfEmployee = &applicant.BizNoOfEmployee
	creditDecisionEngineAPIApplicant.BizAccountCreatePurpose = &applicant.BizAccountCreatePurpose
	creditDecisionEngineAPIApplicant.BizCountryOfIncome = &applicant.BizCountryOfIncome
	creditDecisionEngineAPIApplicant.BizEstimatedMonthlyTrans = &applicant.BizEstimatedMonthlyTrans
	creditDecisionEngineAPIApplicant.BizEstimatedMonthlyTransAmt = &applicant.BizEstimatedMonthlyTransAmt
	creditDecisionEngineAPIApplicant.IsRegisteredSSM = &applicant.IsRegisteredSSM
}

func processExperianReport(inputReport *experianAPI.GetOrderReportResponse) *map[string]interface{} {
	var processedReport map[string]interface{}
	if inputReport != nil {
		reportBytes, _ := json.Marshal(inputReport.Report)
		err := json.Unmarshal(reportBytes, &processedReport)
		if err != nil {
			return nil
		}
		ver, ok := processedReport[constants.ExperianReportVerField].(string)
		if !ok {
			return nil
		}
		dats, ok := processedReport[constants.ExperianReportDatsField].([]interface{})
		if !ok {
			return nil
		}
		processedReport = map[string]interface{}{
			constants.ExperianReportVerField:  ver,
			constants.ExperianReportDatsField: dats,
		}
	}
	return &processedReport
}

func fetchPS11Data(crEcosystemDetail creditDecisionEngineAPI.EcosystemDetail, applicantEcoSystemDetail *storage.ApplicantEcoSystemDetails) creditDecisionEngineAPI.EcosystemDetail {
	var ps11Data whitelistAPI.PersonalRateMetadata
	_ = json.Unmarshal(applicantEcoSystemDetail.Data, &ps11Data)
	crEcosystemDetail.CampaignID = &ps11Data.CampaignID
	crEcosystemDetail.HashedCustomerIDLendingOffer = &ps11Data.HashedCustomerIDLendingOffer
	crEcosystemDetail.Target = &ps11Data.Target
	crEcosystemDetail.CampaignID = &ps11Data.CampaignID
	crEcosystemDetail.PlacementID = &ps11Data.PlacementID
	interestRate := math.Round(float64(ps11Data.InterestRate)*100) / 100
	crEcosystemDetail.InterestRate = &interestRate
	crEcosystemDetail.MiniSegment = &ps11Data.MiniSegment
	crEcosystemDetail.ClickID = &ps11Data.ClickID

	return crEcosystemDetail
}

func appendForIndividualApplicant(applicantEcoSystemDetail *storage.ApplicantEcoSystemDetails, crEcosystemDetail creditDecisionEngineAPI.EcosystemDetail) creditDecisionEngineAPI.EcosystemDetail {
	// grab individual data pulled with nric
	if applicantEcoSystemDetail.CallType == string(constants.CallTypePS2) && applicantEcoSystemDetail.Partner == string(api.Channel_GRAB) {
		profileData := map[string]interface{}{}
		_ = json.Unmarshal(applicantEcoSystemDetail.Data, &profileData)
		if crEcosystemDetail.ProfileData == nil {
			crEcosystemDetail.ProfileData = make(map[string]interface{})
		}
		for key, value := range profileData {
			crEcosystemDetail.ProfileData[key] = value
		}
	}

	// grab individual data pulled with phone number
	if applicantEcoSystemDetail.CallType == string(constants.CallTypePS11) && applicantEcoSystemDetail.Partner == string(api.Channel_GRAB) {
		crEcosystemDetail = fetchPS11Data(crEcosystemDetail, applicantEcoSystemDetail)
	}

	return crEcosystemDetail
}

func appendForBizApplicant(applicantEcoSystemDetail *storage.ApplicantEcoSystemDetails, crEcosystemDetail creditDecisionEngineAPI.EcosystemDetail) creditDecisionEngineAPI.EcosystemDetail {
	mexData := grabAPI.FetchLendingMexDetailsResponse{}

	// grab merchant data pulled from mex uen
	if applicantEcoSystemDetail.CallType == string(constants.CallTypePS) {
		mexDataBytes, _ := json.Marshal(applicantEcoSystemDetail.Data)
		_ = json.Unmarshal(mexDataBytes, &mexData)
		crEcosystemDetail.MerchantData = mexData.Merchant.(map[string]interface{})
		var storesData []map[string]interface{}
		for _, store := range mexData.Stores {
			if storeMap, ok := store.(map[string]interface{}); ok {
				storesData = append(storesData, storeMap)
			}
		}
		crEcosystemDetail.StoresData = storesData
	}

	// singtel data pulled from uen
	if applicantEcoSystemDetail.CallType == string(constants.CallTypePS11) && applicantEcoSystemDetail.Partner == string(api.Channel_SINGTEL) {
		crEcosystemDetail = fetchPS11Data(crEcosystemDetail, applicantEcoSystemDetail)
	}

	return crEcosystemDetail
}

func fetchEcosystemDataForBizFlexiCredit(applicantID string, applicantEcoSystemDetails []*storage.ApplicantEcoSystemDetails, partner string, entityType creditDecisionEngineAPI.EntityType) *creditDecisionEngineAPI.EcosystemDetail {
	crEcosystemDetail := creditDecisionEngineAPI.EcosystemDetail{}

	for _, applicantEcoSystemDetail := range applicantEcoSystemDetails {
		if applicantEcoSystemDetail.ApplicantID == applicantID && applicantEcoSystemDetail.Partner == partner && applicantEcoSystemDetail.Status != string(constants.GrabResponseFailed) {
			switch entityType {
			case creditDecisionEngineAPI.EntityType_INDIVIDUAL:
				crEcosystemDetail = appendForIndividualApplicant(applicantEcoSystemDetail, crEcosystemDetail)

			case creditDecisionEngineAPI.EntityType_BUSINESS:
				crEcosystemDetail = appendForBizApplicant(applicantEcoSystemDetail, crEcosystemDetail)
			}
		}
	}
	return &crEcosystemDetail
}

func mapEcosystemData(applicantID string, ecosystemDetail *api.EcosystemDetail, applicantEcoSystemDetails []*storage.ApplicantEcoSystemDetails, partner string) *creditDecisionEngineAPI.EcosystemDetail {
	marshal, _ := json.Marshal(ecosystemDetail)
	crEcosystemDetail := creditDecisionEngineAPI.EcosystemDetail{}
	_ = json.Unmarshal(marshal, &crEcosystemDetail)

	// Set ecosystem data
	for _, applicantEcoSystemDetail := range applicantEcoSystemDetails {
		if applicantEcoSystemDetail.ApplicantID == applicantID && applicantEcoSystemDetail.Partner == partner {
			profileData := map[string]interface{}{}
			_ = json.Unmarshal(applicantEcoSystemDetail.Data, &profileData)
			crEcosystemDetail.ProfileData = profileData
			break
		}
	}
	return &crEcosystemDetail
}

// MapProducts Product mapper function
func MapProducts(dto dto.CreditDecisionRequestMapperDTO) []creditDecisionEngineAPI.Product {
	var products []creditDecisionEngineAPI.Product
	productDto := dto.Products
	for index := range productDto {
		productType := creditDecisionEngineAPI.ProductType(productDto[index].ProductType)
		subProductType := string(productDto[index].SubProductType)
		cdeProduct := creditDecisionEngineAPI.Product{
			ProductType:           &productType,
			SubProductType:        &subProductType,
			RequestedLoanAmount:   &productDto[index].RequestedLoanAmount,
			RequestedCurrencyCode: &productDto[index].RequestedCurrencyCode,
			RequestedTenure:       &productDto[index].RequestedTenure,
			InterestRate:          &productDto[index].InterestRate,
		}
		products = append(products, cdeProduct)
	}
	return products
}

func mapIncome(applicant api.FlexiTermLoanApplicant) *creditDecisionEngineAPI.Income {
	if applicant.Income == nil {
		return nil
	}
	documents := applicant.Income.IncomeDocuments
	var incomeDocuments []creditDecisionEngineAPI.IncomeDocument

	for index := range documents {
		documentType := creditDecisionEngineAPI.IncomeDocumentType(documents[index].IncomeDocumentType)
		incomeDocument := creditDecisionEngineAPI.IncomeDocument{
			IncomeDocumentType: &documentType,
			DocumentDate:       &documents[index].DocumentDate,
			YearOfAssessment:   &documents[index].YearOfAssessment,
			IncomeComponents:   mapIncomeComponents(documents[index].IncomeComponents),
		}
		incomeDocuments = append(incomeDocuments, incomeDocument)
	}

	return &creditDecisionEngineAPI.Income{
		IncomeDocuments: incomeDocuments,
	}
}

func mapIncomeComponents(components []api.IncomeComponent) []creditDecisionEngineAPI.IncomeComponent {
	var incomeComponents []creditDecisionEngineAPI.IncomeComponent
	if len(components) == 0 {
		return nil
	}

	for index := range components {
		incomeComponent := creditDecisionEngineAPI.IncomeComponent{
			IncomeComponentType: &components[index].IncomeComponentType,
			IncomeCurrency:      &components[index].IncomeCurrency,
			IncomeOccurrences:   mapIncomeOccurrences(components[index].IncomeOccurrences),
		}
		incomeComponents = append(incomeComponents, incomeComponent)
	}
	return incomeComponents
}

func mapIncomeOccurrences(occurrences []api.IncomeOccurrence) []creditDecisionEngineAPI.IncomeOccurrence {
	if len(occurrences) == 0 {
		return nil
	}
	var incomeOccurrences []creditDecisionEngineAPI.IncomeOccurrence
	for index := range occurrences {
		incomeOccurrence := creditDecisionEngineAPI.IncomeOccurrence{
			IncomeAmount:        &occurrences[index].IncomeAmount,
			PaidForMonth:        &occurrences[index].PaidForMonth,
			EmployerNameFromCPF: &occurrences[index].EmployerNameFromCPF,
		}
		incomeOccurrences = append(incomeOccurrences, incomeOccurrence)
	}
	return incomeOccurrences
}

func mapEmploymentDetail(applicant api.FlexiTermLoanApplicant) *creditDecisionEngineAPI.EmploymentDetail {
	employmentDetail := applicant.EmploymentDetail
	if employmentDetail == nil {
		return nil
	}
	employmentType := creditDecisionEngineAPI.EmploymentType(employmentDetail.EmploymentType)
	return &creditDecisionEngineAPI.EmploymentDetail{
		EmploymentType:         &employmentType,
		OccupationCode:         &employmentDetail.OccupationCode,
		IndustryCode:           &employmentDetail.IndustryCode,
		LengthOfEmployment:     &employmentDetail.LengthOfEmployment,
		TotalWorkingExperience: &employmentDetail.TotalWorkingExperience,
		EmploymentSector:       &employmentDetail.EmploymentSector,
		CompanyCategory:        &employmentDetail.CompanyCategory,
		NegativeCompanyList:    &employmentDetail.NegativeCompanyList,
		EmployerName:           &employmentDetail.EmployerName,
		NatureOfBusiness:       &employmentDetail.NatureOfBusiness,
		Position:               &employmentDetail.Position,
		IncomePerMonth:         &employmentDetail.IncomePerMonth,
		IndustrySector:         &employmentDetail.IndustrySector,
	}
}

func mapCpfWithdrawals(withdrawals []api.CPFHousingWithdrawal) []creditDecisionEngineAPI.CPFHousingWithdrawal {
	if len(withdrawals) == 0 {
		return nil
	}
	var cpfWithdrawals []creditDecisionEngineAPI.CPFHousingWithdrawal
	for index := range withdrawals {
		cpfWithdrawal := creditDecisionEngineAPI.CPFHousingWithdrawal{
			PrincipalWithdrawalAmount:          withdrawals[index].PrincipalWithdrawalAmount,
			MonthlyInstalmentAmount:            withdrawals[index].MonthlyInstalmentAmount,
			AccruedInterestAmount:              withdrawals[index].AccruedInterestAmount,
			TotalAmountOfCPFAllowedForProperty: withdrawals[index].TotalAmountOfCPFAllowedForProperty,
			CurrencyCode:                       withdrawals[index].CurrencyCode,
		}
		cpfWithdrawals = append(cpfWithdrawals, cpfWithdrawal)
	}
	return cpfWithdrawals
}

// MapToApplicationDecisionStorage map cde responses to application decision
func MapToApplicationDecisionStorage(response *creditDecisionEngineAPI.FICOCreditDecisionResponse, applicationID string) *storage.ApplicationDecision {
	return &storage.ApplicationDecision{
		ApplicationID: applicationID,
		CallType:      api.CallType(*response.Message.CallType),
		ReferenceID:   *response.Message.ReferenceID,
		Status:        *response.Message.Status,
		CreatedAt:     utils.CurrentDateAndTimeInUTC(),
		CreatedBy:     string(servicename.LoanApp),
		UpdatedAt:     utils.CurrentDateAndTimeInUTC(),
		UpdatedBy:     "",
	}
}

// MapToApplicantDecisionStorage map cde responses to applicant decision
func MapToApplicantDecisionStorage(creditBureauResponse *dto.CreditBureauEnquiryStreamMessage, applicationID string) *storage.ApplicantDecision {
	return &storage.ApplicantDecision{
		ApplicantID:   creditBureauResponse.ApplicantID,
		ApplicationID: applicationID,
		CallType:      "credit_bureau",
		ReferenceID:   creditBureauResponse.ReferenceID,
		Status:        creditBureauResponse.Status,
		CreatedAt:     utils.CurrentDateAndTimeInUTC(),
		CreatedBy:     string(servicename.LoanApp),
		UpdatedAt:     utils.CurrentDateAndTimeInUTC(),
		UpdatedBy:     "",
	}
}

// MapToOfferStorage map cde responses to offer
func MapToOfferStorage(offer creditDecisionEngineAPI.Offer, applicationID string, status string, currencyCode string) *storage.Offer {
	storageOffer := &storage.Offer{
		ApplicationID:       applicationID,
		OfferedCredit:       decimal.NewFromFloat(*offer.ApprovedCreditLimit),
		OfferedTenor:        *offer.ApprovedTenor,
		TenorUnit:           constants.MONTH,
		OfferedInterestRate: decimal.NewFromFloat(*offer.InterestRate),
		AcceptedCredit:      decimal.NewFromInt(0),
		CurrencyCode:        currencyCode,
		Status:              status,
		CreatedAt:           utils.CurrentDateAndTimeInUTC(),
		CreatedBy:           string(servicename.LoanApp),
		UpdatedAt:           utils.CurrentDateAndTimeInUTC(),
		UpdatedBy:           string(servicename.LoanApp),
	}
	if constants.EnableBalanceTransferFeatureFlag {
		if len(offer.BalanceTransferParameters) > 0 {
			balanceTransferDTO := mapper.MapToBalanceTransferDTO(offer.BalanceTransferParameters[0], decimal.NewFromFloat(*offer.ApprovedCreditLimit))
			balanceTransferParametersDTOJSON, _ := json.Marshal(dto.MetadataDTO{
				BalanceTransferParameters: &balanceTransferDTO})
			rawMessage := json.RawMessage(balanceTransferParametersDTOJSON)
			storageOffer.MetaData = &rawMessage
		} else {
			emptyJSON := utils.MarshalEmptyJSON()
			storageOffer.MetaData = &emptyJSON
		}
	}
	return storageOffer
}

// MapCardOffer map cde response to card offer
func MapCardOffer(product creditDecisionEngineAPI.Product, applicationID string, defaultCurrencyCode string, status string) storage.Offer {
	offerStorage := storage.Offer{
		ApplicationID: applicationID,
		OfferedCredit: decimal.NewFromFloat(*product.ProductDecision.ApprovedCreditLimit),
		// Constant because not applicable for flexi_card
		OfferedTenor: 0,
		TenorUnit:    constants.MONTH,
		// Constant because not applicable for flexi_card
		OfferedInterestRate: decimal.NewFromInt(0),
		AcceptedCredit:      decimal.NewFromInt(0),
		CurrencyCode:        defaultCurrencyCode,
		Status:              status,
		CreatedAt:           utils.CurrentDateAndTimeInUTC(),
		CreatedBy:           string(servicename.LoanApp),
		UpdatedAt:           utils.CurrentDateAndTimeInUTC(),
		UpdatedBy:           string(servicename.LoanApp),
	}
	return offerStorage
}

func MapPreviousApplications(applications []api.PreviousApplication) []creditDecisionEngineAPI.PreviousApplication {
	var mappedPreviousApplications []creditDecisionEngineAPI.PreviousApplication
	for _, application := range applications {
		mappedApplication := &creditDecisionEngineAPI.PreviousApplication{
			PreviousApplicationID: &application.PreviousApplicationID,
			PreviousProduct:       &application.PreviousProduct,
			PreviousFinalDecision: &application.PreviousFinalDecision,
			PreviousDecisionDate:  &application.PreviousDecisionDate,
			PreviousReasons:       application.PreviousReasons,
		}
		mappedPreviousApplications = append(mappedPreviousApplications, *mappedApplication)
	}
	return mappedPreviousApplications
}

func getIncomeDetails(incomeDerivationEvent *dto.IncomeDerivationStreamMessage, isBizFlexiCreditApplication bool) *creditDecisionEngineAPI.Income {
	if incomeDerivationEvent != nil {
		var documentName *string
		var perfiosStatementStatus *string
		var perfiosIncomeAlert *bool
		var secondaryStatementStatus *string
		var incomeAmount *float64
		if incomeDerivationEvent.Metadata != nil {
			perfiosStatementStatus = utils.GetPointer(incomeDerivationEvent.Metadata.PerfiosStatementStatus)
			perfiosIncomeAlert = utils.GetPointer(incomeDerivationEvent.Metadata.IncomeAlert)
			secondaryStatementStatus = incomeDerivationEvent.Metadata.SecondaryStatementStatus
		}
		if incomeDerivationEvent.UserDetails != nil {
			documentName = utils.GetPointer(incomeDerivationEvent.UserDetails.Name)
		}
		if !isBizFlexiCreditApplication {
			incomeAmount = utils.GetPointer(incomeDerivationEvent.IncomeAmount)
		}
		income := &creditDecisionEngineAPI.Income{
			IncomeDocuments: []creditDecisionEngineAPI.IncomeDocument{
				{
					IncomeDocumentName:   documentName,
					IncomeAmount:         incomeAmount,
					IncomeDocumentSource: utils.GetPointer(incomeDerivationEvent.IncomeDocumentSource),
					IncomeDocumentType:   utils.GetPointer(creditDecisionEngineAPI.IncomeDocumentType(incomeDerivationEvent.DocumentType)),
				},
			},
			Perfios: &creditDecisionEngineAPI.Perfios{
				PerfiosStatementStatus:   perfiosStatementStatus,
				PerfiosIncomeAlert:       perfiosIncomeAlert,
				SecondaryStatementStatus: secondaryStatementStatus,
			},
		}
		return income
	}
	return &creditDecisionEngineAPI.Income{}
}
