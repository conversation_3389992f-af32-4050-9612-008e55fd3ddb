package mapper

import (
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/credit_card_account_creation_event"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/loc_account_creation_event"

	"gitlab.myteksi.net/dakota/common/servicename"
	accountServiceAPI "gitlab.myteksi.net/dakota/lending/external/corebanking/accountservice"
)

// MapCreditCardAccountCreationStreamToDTO maps flexicard account creation event to dto
func MapCreditCardAccountCreationStreamToDTO(data *credit_card_account_creation_event.CreditCardAccountCreationEvent) *dto.CreditCardAccountCreationStreamMessage {
	return &dto.CreditCardAccountCreationStreamMessage{
		ReferenceID:             data.ReferenceID,
		ApplicationID:           data.ApplicationID,
		AccountID:               data.AccountID,
		ProductID:               data.ProductID,
		ProductVariantCode:      data.ProductVariantCode,
		ProductVersionID:        data.ProductVersionID,
		PermittedCurrencies:     data.PermittedCurrencies,
		CifNumber:               data.CifNumber,
		Status:                  data.Status,
		StatusReasonDescription: data.StatusReasonDescription,
		OpeningTimestamp:        data.OpeningTimestamp,
		InstanceParams:          data.InstanceParams,
		CreatedBy:               data.CreatedBy,
	}
}

// MapLOCAccountCreationStreamToDTO : method to map LOC account creation stream to DTO
func MapLOCAccountCreationStreamToDTO(data *loc_account_creation_event.LOCAccountCreationEvent) *dto.LOCAccountCreationStreamMessage {
	return &dto.LOCAccountCreationStreamMessage{
		ReferenceID:             data.ReferenceID,
		ApplicationID:           data.ApplicationID,
		AccountID:               data.AccountID,
		ProductID:               data.ProductID,
		ProductVariantCode:      data.ProductVariantCode,
		ProductVersionID:        data.ProductVersionID,
		PermittedCurrencies:     data.PermittedCurrencies,
		CifNumber:               data.CifNumber,
		Status:                  data.Status,
		StatusReasonDescription: data.StatusReasonDescription,
		OpeningTimestamp:        data.OpeningTimestamp,
		InstanceParams:          data.InstanceParams,
		CreatedBy:               data.CreatedBy,
	}
}

// MapCreateLOCAccountRequest ...
func MapCreateLOCAccountRequest(req dto.CreateLOCAccountRequest, productVariantCode api.SubProductType) (*accountServiceAPI.CreateLOCAccountRequest, error) {
	request := accountServiceAPI.CreateLOCAccountRequest{
		IdempotencyKey:     req.OnboardingApplicationID,
		ProductVariantCode: string(productVariantCode),
		CifNumber:          req.ApplicantID,
		InstanceParameters: &accountServiceAPI.LOCInstanceParameters{
			OfferedLOC: &accountServiceAPI.Money{
				CurrencyCode: constants.DefaultCurrencyCode,
				Val:          int64(*req.ApprovedCreditLimit) * 100,
			},
			OfferedInterestRate: float32(*req.InterestRate),
			OfferedMaxTenor:     *req.ApprovedTenor,
		},
		CreatedBy:     string(servicename.LoanApp),
		ApplicationID: req.ApplicationID,
	}

	return &request, nil
}
