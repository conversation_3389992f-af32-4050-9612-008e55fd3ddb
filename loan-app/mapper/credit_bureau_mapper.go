package mapper

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils/kms"
	creditBureauEvent "gitlab.com/gx-regional/dakota/schemas/streams/apis/credit_bureau_event"
	"golang.org/x/exp/slices"

	myAdapterAPI "gitlab.myteksi.net/dakota/lending/cb-my-adapter/api"
	ctosReport "gitlab.myteksi.net/dakota/lending/common/bureaureport"
	"gitlab.myteksi.net/dakota/lending/common/constant"
	"gitlab.myteksi.net/dakota/lending/common/countries"
	creditBureauServiceAPI "gitlab.myteksi.net/dakota/lending/credit-bureau/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// MapCreditBureauEnquiryStreamToDTO maps credit bureau event stream object to dto
func MapCreditBureauEnquiryStreamToDTO(data *creditBureauEvent.CreditBureauEvent) (*dto.CreditBureauEnquiryStreamMessage, error) {
	var errors []*dto.Error
	for _, err := range data.Errors {
		errors = append(errors, &dto.Error{
			ErrorCode:        err.ErrorCode,
			ErrorDescription: err.ErrorDescription,
		})
	}

	return &dto.CreditBureauEnquiryStreamMessage{
		ReferenceID:       data.ReferenceID,
		ApplicationID:     data.ApplicationID,
		ApplicantID:       data.ApplicantID,
		Service:           data.Service,
		Status:            data.Status,
		CountryCode:       data.CountryCode,
		Report:            data.Report,
		Errors:            errors,
		BureauEnquiryDate: data.BureauEnquiryDate,
	}, nil
}

// CreateCreditBureauEnquiryRequest request object for credit bureau enquiry
func CreateCreditBureauEnquiryRequest(dto dto.CreditBureauRequestMapperDTO) *creditBureauServiceAPI.ApplicantEnquiryRequest {
	enquiry := make(map[string]interface{})
	enquiry["accountType"] = string(constant.AccountTypeEnumSingle)
	enquiry["applicant"] = CreateEnquiryApplicant(dto.LoanApplicant, dto.DynamicConstants.DefaultCountryCode, dto)
	enquiry["productType"] = string(dto.ProductType)
	if dto.Amount != nil {
		enquiry["requestedCurrencyCode"] = dto.Amount.CurrencyCode
		enquiry["requestedLoanAmount"] = dto.Amount.Val
	}
	switch dto.ProductType {
	case api.ProductType_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT:
		enquiry["enquiryType"] = string(constant.EnquiryTypeEnumGT)
		if dto.DynamicConstants.DefaultCountryCode == countries.MY {
			enquiry["applicant"].(map[string]interface{})["bizName"] = dto.BizLoanApplicant.BizName
			enquiry["applicant"].(map[string]interface{})["primaryID"] = api.IDDetails{
				Type:   dto.LoanApplicant.IDType,
				Number: dto.LoanApplicant.IDNumber,
			}
			enquiry["applicant"].(map[string]interface{})["additionalIDs"] = []api.IDDetails{{
				Type:   dto.BizLoanApplicant.IDType,
				Number: dto.BizLoanApplicant.IDNumber,
			}}
			if dto.LoanApplicant.Consent != nil {
				enquiry["applicant"].(map[string]interface{})["consent"] = dto.LoanApplicant.Consent
			}
		}
	default:
		enquiry["enquiryType"] = string(constant.EnquiryTypeEnumNA)
	}
	if dto.DynamicConstants.DefaultCountryCode == countries.MY {
		enquiry["dataSource"] = constant.CtosCreditReport
	}
	request := &creditBureauServiceAPI.ApplicantEnquiryRequest{
		Service:       string(constant.EnquiryServiceEnqlitsc),
		ApplicationID: dto.ApplicationID,
		ApplicantID:   dto.LoanApplicant.ApplicantID,
		Enquiry:       enquiry,
		ReferenceID:   dto.TransitionID,
		CountryCode:   dto.DynamicConstants.DefaultCountryCode,
	}
	return request
}

// CreateEnquiryApplicant create enquiry applicant of credit bureau enquiry request
func CreateEnquiryApplicant(loanApplicant api.FlexiTermLoanApplicant, countryCode string, dto dto.CreditBureauRequestMapperDTO) map[string]interface{} {
	applicant := make(map[string]interface{})
	idNumber := loanApplicant.IDNumber
	var err error
	if dto.EnableEncryptionFlags.EnableIDNumberEncryption && dto.EnableEncryptionFlags.EnableSwitchToHashedIDNumber {
		ctx := context.Background()
		idNumber, err = kms.KMSClient.Decrypt(ctx, loanApplicant.EncryptedIDNumber)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.KMSClientLogTag, fmt.Sprintf("Error while decrypting loanApplicant.EncryptedIDNumber %s with error %s", loanApplicant.EncryptedIDNumber, err.Error()))
			// Fallback
			idNumber = loanApplicant.IDNumber
		}
	}
	applicant["IDNumber"] = idNumber
	applicant["IDType"] = loanApplicant.IDType

	if strings.TrimSpace(loanApplicant.State) == countryCode {
		applicant["addressFormat"] = string(constant.AddressFormatEnumSL)
	} else {
		applicant["addressFormat"] = string(constant.AddressFormatEnumSF)
	}
	if loanApplicant.PrimaryID != nil {
		applicant["primaryID"] = loanApplicant.PrimaryID
	}
	if loanApplicant.Consent != nil {
		applicant["consent"] = loanApplicant.Consent
	}
	applicant["addressType"] = loanApplicant.AddressType
	applicant["applicantType"] = loanApplicant.ApplicantType
	applicant["contactNumber"] = loanApplicant.ContactNumber
	applicant["contactType"] = loanApplicant.ContactType
	applicant["countryCode"] = countryCode
	applicant["dateOfBirth"] = loanApplicant.DateOfBirth
	applicant["fullName"] = loanApplicant.FullName
	applicant["gender"] = loanApplicant.Gender
	applicant["maritalStatus"] = loanApplicant.MaritalStatus
	applicant["nationality"] = loanApplicant.Nationality
	applicant["occupationCode"] = loanApplicant.EmploymentDetail.OccupationCode
	applicant["postalCode"] = loanApplicant.PostalCode
	applicant["state"] = loanApplicant.State
	applicant["streetName"] = loanApplicant.StreetName
	return applicant
}

// CreateNotifyPayload request object for credit bureau enquiry
func CreateNotifyPayload(dto dto.CreditBureauNotifyRequestMapperDTO, finexusDTO *dto.FinexusDTO) *creditBureauServiceAPI.ApplicationNotifyRequest {
	notifyPayload := make(map[string]interface{})
	notifyPayload["notifyType"] = dto.NotifyType
	notifyPayload["dataSource"] = constants.FinexusApplication

	ctosData := dataFromAuditTrailEvent(dto.AuditData)

	notifyPayload["application"] = CreateApplicationNotifyRequest(dto, finexusDTO, ctosData)
	if dto.NotifyType == string(constants.NotifyTypeUpdate) {
		notifyPayload["previousReferenceID"] = finexusDTO.PreviousReferenceID
	}
	request := &creditBureauServiceAPI.ApplicationNotifyRequest{
		ApplicationID: dto.ApplicationID,
		ApplicantID:   dto.LoanApplicant[0].ApplicantID,
		ReferenceID:   dto.TransitionID,
		NotifyPayload: notifyPayload,
	}
	return request
}

// CreateApplicationNotifyRequest ...
func CreateApplicationNotifyRequest(dto dto.CreditBureauNotifyRequestMapperDTO, finexusDTO *dto.FinexusDTO,
	ctosData *ctosReport.BizEnqReport) *myAdapterAPI.Application {
	offer := finexusDTO.Offer
	loanApplicant := dto.LoanApplicant
	loanApplication := dto.LoanApplication
	acceptedCredit := offer.OfferedCredit.InexactFloat64()
	if offer.Status == string(api.LoanOfferStatus_ACCEPTED) {
		acceptedCredit = offer.AcceptedCredit.InexactFloat64()
	}
	application := &myAdapterAPI.Application{
		ApplicationID:     dto.ApplicationID,
		ApplicationStatus: getApplicationStatusMapping(dto.ApplicationStatus),
		ApplicationType:   myAdapterAPI.ApplicationType(loanApplication.ApplicationType),
		Products: []myAdapterAPI.Product{
			{
				ProductType:           myAdapterAPI.ProductType(api.ProductType_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT),
				SubProductType:        myAdapterAPI.SubProductType(api.SubProductType_DEFAULT_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT),
				RequestedLoanAmount:   offer.OfferedCredit.InexactFloat64(),
				RequestedTenure:       offer.OfferedTenor,
				RequestedCurrencyCode: loanApplication.CountryCode,
			},
		},
		Applicants:  CreateApplicantNotifyRequest(loanApplicant, finexusDTO, ctosData),
		CountryCode: loanApplication.CountryCode,
		LimitCreationResponse: &myAdapterAPI.LimitCreationResponse{
			ApprovedCreditLimit:     acceptedCredit,
			ContractualInterestRate: offer.OfferedInterestRate.InexactFloat64(),
			EffectiveInterestRate:   finexusDTO.Eir,
		},
	}
	return application
}

// CreateApplicantNotifyRequest ...
func CreateApplicantNotifyRequest(applicants []*storage.Applicant, finexusDTO *dto.FinexusDTO, ctosData *ctosReport.BizEnqReport) []myAdapterAPI.ApplicantInfo {
	bizApplicants := []myAdapterAPI.ApplicantInfo{}
	var businessCommenced, msicCode string
	for _, applicant := range applicants {
		var lendingApplicant api.FlexiTermLoanApplicant
		_ = json.Unmarshal(applicant.Data, &lendingApplicant)
		if ctosData != nil {
			businessCommenced, msicCode = getDataFromCtos(ctosData, api.IDType(applicant.IDType))
		}
		//msic from ctos
		data := myAdapterAPI.ApplicantInfo{
			ApplicantID:       applicant.ApplicantID,
			IDType:            myAdapterAPI.IDType(applicant.IDType),
			IDNumber:          applicant.IDNumber,
			ApplicantType:     myAdapterAPI.ApplicantType(applicant.ApplicantType),
			FundsPurposeOfUse: strings.ToLower(lendingApplicant.FundsPurposeOfUse),
		}
		if applicant.IDType == string(api.IDType_BRN) {
			data.FullName = lendingApplicant.BizName
			data.DateOfBirth = businessCommenced
			data.BizAnnualSales = lendingApplicant.BizAnnualSales
			data.BizNoOfEmployee = lendingApplicant.BizNoOfEmployee
			data.EmploymentDetail = &myAdapterAPI.EmploymentDetail{
				IndustryCode: msicCode,
			}
			data.AddressDetails = convertBizAddressesToAddressDetails(lendingApplicant)
		} else {
			data.FullName = lendingApplicant.FullName
			if lendingApplicant.EmploymentDetail != nil {
				data.EmploymentDetail = &myAdapterAPI.EmploymentDetail{
					OccupationCode:   lendingApplicant.EmploymentDetail.OccupationCode,
					NatureOfBusiness: lendingApplicant.EmploymentDetail.NatureOfBusiness,
					EmployerName:     lendingApplicant.EmploymentDetail.EmployerName,
					EmploymentType:   myAdapterAPI.EmploymentType(lendingApplicant.EmploymentDetail.EmploymentType),
					IncomePerMonth:   lendingApplicant.EmploymentDetail.IncomePerMonth,
				}
			}
			if len(lendingApplicant.AddressDetails) > 0 {
				data.AddressDetails = []myAdapterAPI.AddressDetails{
					{
						AddressLine: lendingApplicant.AddressDetails[0].AddressLine,
						PostalCode:  lendingApplicant.AddressDetails[0].PostalCode,
						State:       lendingApplicant.AddressDetails[0].District,
						City:        lendingApplicant.AddressDetails[0].City,
						AddressType: myAdapterAPI.AddressType(lendingApplicant.AddressDetails[0].AddressType),
					},
				}
			}
		}
		bizApplicants = append(bizApplicants, data)
	}

	return bizApplicants
}

func getDataFromCtos(ctosData *ctosReport.BizEnqReport, idType api.IDType) (string, string) {
	var businessCommenced, msicCode string
	indexOfCtos := slices.IndexFunc(ctosData.Summary.EnqSum, func(data ctosReport.EnqSum) bool {
		return idType == api.IDType_BRN && data.Ptype == "B" || idType == api.IDType_MYKAD && data.Ptype == "I"
	})
	ctosDataForApplicant := (*ctosData.Enquiry)[indexOfCtos]
	if len(ctosDataForApplicant.SectionA.Record) != 0 {
		businessCommenced = ctosDataForApplicant.SectionA.Record[0].BusinessCommenced
		if len(ctosDataForApplicant.SectionA.Record[0].MsicSsms.MsicSsm) != 0 {
			msicCode = ctosDataForApplicant.SectionA.Record[0].MsicSsms.MsicSsm[0].Code
		}
	}

	return businessCommenced, msicCode
}

func convertBizAddressesToAddressDetails(lendingApplicant api.FlexiTermLoanApplicant) []myAdapterAPI.AddressDetails {
	var addressDetails []myAdapterAPI.AddressDetails
	addressDetails = append(addressDetails, myAdapterAPI.AddressDetails{
		AddressLine: lendingApplicant.BizAddressLine1 + " " + lendingApplicant.BizAddressLine2,
		PostalCode:  lendingApplicant.BizAddressPostcode,
		State:       lendingApplicant.BizAddressState,
		City:        lendingApplicant.BizAddressCity,
		AddressType: myAdapterAPI.AddressType_REGISTERED,
	})
	return addressDetails
}

func dataFromAuditTrailEvent(auditData []*storage.AuditTrailEvent) (ctosEnqData *ctosReport.BizEnqReport) {
	var ctosEnquiryStreamMsg *dto.CreditBureauEnquiryStreamMessage
	for _, data := range auditData {
		if data.EventName == constants.CTOSEvent {
			ctosDataBytes, _ := json.Marshal(data.AuditData)
			_ = json.Unmarshal(ctosDataBytes, &ctosEnquiryStreamMsg)
			s := []byte(ctosEnquiryStreamMsg.Report["RSP_REPORT"])
			_ = json.Unmarshal(s, &ctosEnqData)
		}
	}
	return ctosEnqData
}

func getApplicationStatusMapping(status constants.OnboardingStatusType) myAdapterAPI.ApplicationStatus {
	switch status {
	case constants.OnboardingStatusNameScreeningApproved:
		return myAdapterAPI.ApplicationStatus_PENDING_ACCEPTANCE
	case constants.OnboardingStatusNameScreeningRejected:
		return myAdapterAPI.ApplicationStatus_REJECTED
	case constants.OnboardingStatusUserFundExpired:
		return myAdapterAPI.ApplicationStatus_EXPIRED
	case constants.OnboardingStatusUserFundSuccessful:
		return myAdapterAPI.ApplicationStatus_ACCEPTED
	default:
		return myAdapterAPI.ApplicationStatus_PROCESSING
	}
}
