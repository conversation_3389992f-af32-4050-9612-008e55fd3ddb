package mapper

import (
	"testing"
	"time"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/dto"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/credit_card_account_creation_event"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/loc_account_creation_event"
)

const (
	APP123 = "APP123"
)

func TestMapCreditCardAccountCreationStreamToDTO(t *testing.T) {
	randomTime := time.Date(2021, 10, 1, 0, 0, 0, 0, time.UTC) // Example date for OpeningTimestamp
	data := &credit_card_account_creation_event.CreditCardAccountCreationEvent{
		ReferenceID:             "REF123",
		ApplicationID:           APP123,
		AccountID:               "ACC123",
		ProductID:               "PROD123",
		ProductVariantCode:      "VAR123",
		ProductVersionID:        "VER123",
		PermittedCurrencies:     []string{"USD", "SGD"},
		CifNumber:               "CIF123",
		Status:                  "ACTIVE",
		StatusReasonDescription: "Approved",
		OpeningTimestamp:        &randomTime,
		InstanceParams:          map[string]string{"param1": "value1"},
		CreatedBy:               "user123",
	}

	result := MapCreditCardAccountCreationStreamToDTO(data)

	if result.ReferenceID != "REF123" {
		t.Errorf("Expected ReferenceID REF123, got %s", result.ReferenceID)
	}
	if result.ApplicationID != APP123 {
		t.Errorf("Expected ApplicationID APP123, got %s", result.ApplicationID)
	}
	if result.AccountID != "ACC123" {
		t.Errorf("Expected AccountID ACC123, got %s", result.AccountID)
	}
	if result.ProductID != "PROD123" {
		t.Errorf("Expected ProductID PROD123, got %s", result.ProductID)
	}
	if result.ProductVariantCode != "VAR123" {
		t.Errorf("Expected ProductVariantCode VAR123, got %s", result.ProductVariantCode)
	}
	if result.ProductVersionID != "VER123" {
		t.Errorf("Expected ProductVersionID VER123, got %s", result.ProductVersionID)
	}
	if len(result.PermittedCurrencies) != 2 || result.PermittedCurrencies[0] != "USD" || result.PermittedCurrencies[1] != "SGD" {
		t.Errorf("Expected PermittedCurrencies [USD, SGD], got %v", result.PermittedCurrencies)
	}
	if result.CifNumber != "CIF123" {
		t.Errorf("Expected CifNumber CIF123, got %s", result.CifNumber)
	}
	if result.Status != "ACTIVE" {
		t.Errorf("Expected Status ACTIVE, got %s", result.Status)
	}
	if result.StatusReasonDescription != "Approved" {
		t.Errorf("Expected StatusReasonDescription Approved, got %s", result.StatusReasonDescription)
	}
	if *result.OpeningTimestamp != randomTime {
		t.Errorf("Expected OpeningTimestamp **********, got %v", result.OpeningTimestamp)
	}
	if len(result.InstanceParams) != 1 || result.InstanceParams["param1"] != "value1" {
		t.Errorf("Expected InstanceParams {param1: value1}, got %v", result.InstanceParams)
	}
	if result.CreatedBy != "user123" {
		t.Errorf("Expected CreatedBy user123, got %s", result.CreatedBy)
	}
}

func TestMapLOCAccountCreationStreamToDTO(t *testing.T) {
	randomTime := time.Date(2021, 10, 1, 0, 0, 0, 0, time.UTC) // Example date for OpeningTimestamp
	data := &loc_account_creation_event.LOCAccountCreationEvent{
		ReferenceID:             "LOC_REF123",
		ApplicationID:           "LOC_APP123",
		AccountID:               "LOC_ACC123",
		ProductID:               "LOC_PROD123",
		ProductVariantCode:      "LOC_VAR123",
		ProductVersionID:        "LOC_VER123",
		PermittedCurrencies:     []string{"USD", "EUR"},
		CifNumber:               "LOC_CIF123",
		Status:                  "PENDING",
		StatusReasonDescription: "Under Review",
		OpeningTimestamp:        &randomTime,
		InstanceParams:          map[string]string{"param2": "value2"},
		CreatedBy:               "loc_user123",
	}

	result := MapLOCAccountCreationStreamToDTO(data)

	if result.ReferenceID != "LOC_REF123" {
		t.Errorf("Expected ReferenceID LOC_REF123, got %s", result.ReferenceID)
	}
	if result.ApplicationID != "LOC_APP123" {
		t.Errorf("Expected ApplicationID LOC_APP123, got %s", result.ApplicationID)
	}
	if result.AccountID != "LOC_ACC123" {
		t.Errorf("Expected AccountID LOC_ACC123, got %s", result.AccountID)
	}
	if result.ProductID != "LOC_PROD123" {
		t.Errorf("Expected ProductID LOC_PROD123, got %s", result.ProductID)
	}
	if result.ProductVariantCode != "LOC_VAR123" {
		t.Errorf("Expected ProductVariantCode LOC_VAR123, got %s", result.ProductVariantCode)
	}
	if result.ProductVersionID != "LOC_VER123" {
		t.Errorf("Expected ProductVersionID LOC_VER123, got %s", result.ProductVersionID)
	}
	if len(result.PermittedCurrencies) != 2 || result.PermittedCurrencies[0] != "USD" || result.PermittedCurrencies[1] != "EUR" {
		t.Errorf("Expected PermittedCurrencies [USD, EUR], got %v", result.PermittedCurrencies)
	}
	if result.CifNumber != "LOC_CIF123" {
		t.Errorf("Expected CifNumber LOC_CIF123, got %s", result.CifNumber)
	}
	if result.Status != "PENDING" {
		t.Errorf("Expected Status PENDING, got %s", result.Status)
	}
	if result.StatusReasonDescription != "Under Review" {
		t.Errorf("Expected StatusReasonDescription Under Review, got %s", result.StatusReasonDescription)
	}
	if *result.OpeningTimestamp != randomTime {
		t.Errorf("Expected OpeningTimestamp **********, got %v", result.OpeningTimestamp)
	}
	if len(result.InstanceParams) != 1 || result.InstanceParams["param2"] != "value2" {
		t.Errorf("Expected InstanceParams {param2: value2}, got %v", result.InstanceParams)
	}
	if result.CreatedBy != "loc_user123" {
		t.Errorf("Expected CreatedBy loc_user123, got %s", result.CreatedBy)
	}
}

func TestMapCreateLOCAccountRequest(t *testing.T) {
	approvedCreditLimit := float64(5000)
	interestRate := float64(3.5)
	approvedTenor := int64(12)

	req := dto.CreateLOCAccountRequest{
		OnboardingApplicationID: "ONBOARD123",
		ApplicationID:           APP123,
		ApplicantID:             "APPLICANT123",
		ApprovedCreditLimit:     &approvedCreditLimit,
		InterestRate:            &interestRate,
		ApprovedTenor:           &approvedTenor,
	}

	productVariantCode := api.SubProductType("LOC_VARIANT")

	result, err := MapCreateLOCAccountRequest(req, productVariantCode)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if result.IdempotencyKey != "ONBOARD123" {
		t.Errorf("Expected IdempotencyKey ONBOARD123, got %s", result.IdempotencyKey)
	}
	if result.ProductVariantCode != "LOC_VARIANT" {
		t.Errorf("Expected ProductVariantCode LOC_VARIANT, got %s", result.ProductVariantCode)
	}
	if result.CifNumber != "APPLICANT123" {
		t.Errorf("Expected CifNumber APPLICANT123, got %s", result.CifNumber)
	}
	if result.InstanceParameters.OfferedLOC.CurrencyCode != constants.DefaultCurrencyCode {
		t.Errorf("Expected CurrencyCode %s, got %s", constants.DefaultCurrencyCode, result.InstanceParameters.OfferedLOC.CurrencyCode)
	}
	if result.InstanceParameters.OfferedLOC.Val != int64(5000*100) {
		t.Errorf("Expected OfferedLOC Val 500000, got %d", result.InstanceParameters.OfferedLOC.Val)
	}
	if result.InstanceParameters.OfferedInterestRate != float32(3.5) {
		t.Errorf("Expected OfferedInterestRate 3.5, got %f", result.InstanceParameters.OfferedInterestRate)
	}
	if result.InstanceParameters.OfferedMaxTenor != 12 {
		t.Errorf("Expected OfferedMaxTenor 12, got %d", result.InstanceParameters.OfferedMaxTenor)
	}
	if result.CreatedBy != "loan-app" {
		t.Errorf("Expected CreatedBy loan-app, got %s", result.CreatedBy)
	}
	if result.ApplicationID != APP123 {
		t.Errorf("Expected ApplicationID APP123, got %s", result.ApplicationID)
	}
}
