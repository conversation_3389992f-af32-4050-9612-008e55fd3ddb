// Package config ...
package config

import (
	"time"

	sqsconfig "gitlab.com/gx-regional/dakota/common/retryable-stream/sqs/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/external/hedwig"

	"github.com/myteksi/hystrix-go/hystrix"
	"gitlab.myteksi.net/dakota/common/redis"
	grabAPI "gitlab.myteksi.net/dakota/lending/external/grab/api"
	"gitlab.myteksi.net/dakota/servus/v2"
	sndconfig "gitlab.myteksi.net/snd/streamsdk/kafka/config"
)

// AppConfig ...
type AppConfig struct {
	servus.DefaultAppConfig
	CustomerMaster                         *ServiceConfig                `json:"customerMasterConfig"`
	ProductMaster                          *ServiceConfig                `json:"productMasterConfig"`
	PerfiosAdapter                         *ServiceConfig                `json:"perfiosAdapterConfig"`
	GrabConfig                             GrabConfig                    `json:"grabConfig"`
	AccountService                         *ServiceConfig                `json:"accountServiceConfig"`
	EkybService                            *ServiceConfig                `json:"ekybServiceConfig"`
	CreditBureau                           *ServiceConfig                `json:"creditBureauServiceConfig"`
	LoanCore                               *ServiceConfig                `json:"loanCoreConfig"`
	LoanAppConfig                          LoanAppConfig                 `json:"loanAppConfig"`
	LoanAppKafkaConfig                     LoanAppKafkaConfig            `json:"loanAppKafkaConfig"`
	AppianConfig                           AppianConfig                  `json:"appianConfig"`
	CronJobConfig                          CronJobConfig                 `json:"cronJobConfig"`
	FeatureFlags                           FeatureFlags                  `json:"featureFlags"`
	DynamicConstants                       DynamicConstants              `json:"dynamicConstants"`
	Hedwig                                 Hedwig                        `json:"hedwig"`
	DatalakeConfig                         DatalakeConfig                `json:"datalakeConfig"`
	PigeonConfig                           PigeonConfig                  `json:"pigeonConfig"`
	LocaleConfig                           LocaleConfig                  `json:"localeConfig"`
	RedisConfig                            *redis.ConfigV2               `json:"redisConfig"`
	ApplicationStatusTransitionKafkaConfig *KafkaConfig                  `json:"applicationStatusTransitionKafkaConfig"`
	CreditBureauEnquiryKafkaConfig         *KafkaConfig                  `json:"creditBureauEnquiryKafkaConfig"`
	CoolingPeriod                          map[string]int                `json:"coolingPeriod"`
	StatusReasonToPercentageMapping        map[string]int                `json:"statusReasonToPercentageMapping"`
	WorkflowRetryConfig                    WorkflowRetryConfig           `json:"workflowRetryConfig"`
	MlScoringConfig                        MlScoringConfig               `json:"mlScoringConfig"`
	BizMlScoringConfig                     *ServiceConfig                `json:"bizMlScoringConfig"`
	CrDecisionEng                          *ServiceConfig                `json:"creditDecisionEngineConfig"`
	DigicardCore                           *ServiceConfig                `json:"digicardCoreConfig"`
	CreditCardAccountCreationKafkaConfig   *KafkaConfig                  `json:"creditCardAccountCreationKafkaConfig"`
	LOCAccountCreationEventKafkaConfig     *KafkaConfig                  `json:"locAccountCreationEventKafkaConfig"`
	FileTypeConfig                         map[string]FileTypeConfig     `json:"fileTypeConfig"`
	WhitelistService                       *ServiceConfig                `json:"whitelistServiceConfig"`
	ApplicationService                     *ServiceConfig                `json:"applicationServiceConfig"`
	CreateApplicationConfig                CreateApplicationConfig       `json:"createApplicationConfig"`
	ExperianAdapter                        *ServiceConfig                `json:"experianAdapterConfig"`
	MLOPSConfig                            MLOpsConfig                   `json:"mlopsConfig"`
	RiskBrokerServiceConfig                *ServiceConfig                `json:"riskBrokerServiceConfig"`
	GdProxyServiceConfig                   *ServiceConfig                `json:"gdProxyServiceConfig"`
	IncomeDerivationEventKafkaConfig       *KafkaConfig                  `json:"incomeDerivationEventKafkaConfig"`
	LendingNameScreeningEventKafkaConfig   *KafkaConfig                  `json:"lendingNameScreeningEventKafkaConfig"`
	EcddVerdictEventKafkaConfig            *KafkaConfig                  `json:"ecddVerdictEventKafkaConfig"`
	FinexusUpdateKafkaConfig               *KafkaConfig                  `json:"finexusUpdateKafkaConfig"`
	SQSConfig                              sqsconfig.SQSConfig           `json:"sqsConfig"`
	EventTrackerConfig                     *TrackerConfig                `json:"eventTrackerConfig"`
	TransactionStatement                   *ServiceConfig                `json:"transactionStatementConfig"`
	BizCampaignConfig                      BizCampaignConfig             `json:"bizCampaignConfig"`
	CustomerExperience                     *ServiceConfig                `json:"customerExperienceConfig"`
	CacheTimeoutConfig                     *CacheTimeoutConfig           `json:"cacheTimeoutConfig"`
	KMSEncryptionConfig                    KMSEncryptionConfig           `json:"kmsEncryptionConfig"`
	BackfillEncryptedAndHashedID           *BackfillEncryptedAndHashedID `json:"backfillEncryptedAndHashedID"`
	LendingAccountCreationKafkaConfig      *KafkaConfig                  `json:"lendingAccountCreationKafkaConfig"`
}

// WorkerConfig ...
type WorkerConfig struct {
	DelayInSecs           int    `json:"delayInSecs"`
	StartImmediately      bool   `json:"startImmediately"`
	LogTag                string `json:"logTag"`
	Name                  string `json:"name"`
	LockKey               string `json:"lockKey"`
	LockDurationInSecs    int    `json:"lockDurationInSecs"`
	Enabled               bool   `json:"enabled"`
	BatchSize             int    `json:"batchSize"`
	CheckpointExpiryHours int    `json:"checkpointExpiryHours"`
}

// BackfillEncryptedAndHashedID ...
type BackfillEncryptedAndHashedID struct {
	WorkerConfig
	StartID                       uint64 `json:"startID"`
	EndID                         uint64 `json:"endID"`
	CheckpointKeyPostfix          string `json:"checkpointKeyPostfix"`
	BatchProcessingIntervalInSecs int    `json:"batchProcessingIntervalInSecs"`
}

// WorkflowRetryConfig defines workflow retry config
type WorkflowRetryConfig struct {
	UpdateLoanOfferDetails          *RetryOptions `json:"updateLoanOfferDetails"`
	CreateFlexiCardApplication      *RetryOptions `json:"createFlexiCardApplication"`
	CreateBizFlexiCreditApplication *RetryOptions `json:"createBizFlexiCreditApplication"`
	BundledApplication              *RetryOptions `json:"bundledApplication"`
	OAAccountCreationCheck          *RetryOptions `json:"oaAccountCreationCheck"`
	MlDecisionScore                 *RetryOptions `json:"mlDecisionScore"`
	CreateFlexiTermLoanApplication  *RetryOptions `json:"createFlexiTermLoanApplication"`
	UpdateFinexus                   *RetryOptions `json:"updateFinexus"`
	CreateReviseLimitApplication    *RetryOptions `json:"reviseLimitApplication"`
}

// RetryOptions defines different types of retry policies
type RetryOptions struct {
	TransactionalRetryOption     *RetryPolicy `json:"transactional"`
	AuxiliaryRetryOption         *RetryPolicy `json:"auxiliary"`
	PostTransactionalRetryOption *RetryPolicy `json:"postTransactional"`
	UsageTrackerRetryOption      *RetryPolicy `json:"usageTracker"`
}

// RetryPolicy defines the configs for each retry policy
type RetryPolicy struct {
	IntervalInSeconds int `json:"intervalInSeconds"`
	MaxAttempt        int `json:"maxAttempt"`
	MaxRecordsPerRun  int `json:"maxRecordsPerRun"`
}

// Hedwig ...
type Hedwig struct {
	ClientConfig                      hedwig.Config     `json:"clientConfig"`
	PushNotificationTemplateMappings  map[string]string `json:"pushNotificationTemplateMappings"`
	EmailNotificationTemplateMappings map[string]string `json:"emailNotificationTemplateMappings"`
	SmsNotificationTemplateMappings   map[string]string `json:"smsNotificationTemplateMappings"`
}

// DatalakeConfig ...
type DatalakeConfig struct {
	HostAddress                   string                           `json:"hostAddress"`
	CircuitConfig                 map[string]hystrix.CommandConfig `json:"circuitConfig"`
	GetApplicantLoanPortfolioPath string                           `json:"getApplicantLoanPortfolioPath"`
}

// CustomerMasterConfig ...
type CustomerMasterConfig struct {
	ServiceName    string     `json:"serviceName"`
	BaseURL        string     `json:"baseURL"`
	CircuitBreaker *CBSetting `json:"circuitBreaker"`
}

// PigeonConfig ...
type PigeonConfig struct {
	BaseURL                           string                           `json:"baseURL"`
	SmsNotificationTemplateMappings   map[string]string                `json:"smsNotificationTemplateMappings"`
	CircuitConfig                     map[string]hystrix.CommandConfig `json:"circuitConfig"`
	EmailNotificationTemplateMappings map[string]string                `json:"emailNotificationTemplateMappings"`
	PushNotificationTemplateMappings  map[string]string                `json:"pushNotificationTemplateMappings"`
}

// GrabConfig ...
type GrabConfig struct {
	BaseURL          string                           `json:"baseURL"`
	ServiceName      string                           `json:"serviceName"`
	CircuitConfig    map[string]hystrix.CommandConfig `json:"circuitConfig"`
	HashConfig       HashConfig                       `json:"hashConfig"`
	OAuthCredentials grabAPI.GetOAuthTokenRequest     `json:"oAuthCredentials"`
	HashingConfig    map[string]HashAlgoConfig        `json:"hashingConfig"`
	GrabOAuthConfig  map[string]GrabOAuthConfig       `json:"grabOAuthConfig"`
}

// HashConfig ...
type HashConfig struct {
	SaltHex    string `json:"saltHex"`
	Iterations int    `json:"iterations"`
	HashLength int    `json:"hashLength"`
}

// HashAlgoConfig ...
type HashAlgoConfig struct {
	Salt             string `json:"salt"`
	Iterations       int    `json:"iterations"`
	DerivedKeyLength int    `json:"derivedKeyLength"`
}

// MlScoringConfig defines ml scoring api related config
type MlScoringConfig struct {
	BaseURL string `json:"baseURL"`
}

// LoanAppConfig ...
type LoanAppConfig struct {
	ClientConfig ClientConfig `json:"clientConfig"`
}

// ClientConfig ...
type ClientConfig struct {
	MaxIdleConnsPerHost           int `json:"maxIdleConnsPerHost"`
	IdleConnTimeoutInMillis       int `json:"idleConnTimeoutInSMillis"`
	TimeoutInMillis               int `json:"timeoutInMillis"`
	RequestLogLockTimeoutInMillis int `json:"requestLogLockTimeoutInMillis"`
}

// GrabOAuthConfig grab configs for auth-token
type GrabOAuthConfig struct {
	ClientID                      string `json:"clientID"`
	ClientSecret                  string `json:"clientSecret"`
	GrantType                     string `json:"grantType"`
	Scope                         string `json:"scope"`
	TTLForBearerTokenInRedisInSec int64  `json:"ttlForBearerTokenInRedisInSec"`
}

// LoanAppKafkaConfig ...
type LoanAppKafkaConfig struct {
	*sndconfig.KafkaConfig
}

// AppianConfig defines the config required to initialise the appian client.
type AppianConfig struct {
	HostAddress                   string                           `json:"hostAddress"`
	CircuitConfig                 map[string]hystrix.CommandConfig `json:"circuitConfig"`
	ClientID                      string                           `json:"clientID"`
	RegisteredClientID            string                           `json:"registeredClientID"`
	RegisteredClientSecret        string                           `json:"registeredClientSecret"`
	GrantType                     string                           `json:"grantType"`
	TTLForBearerTokenInRedisInSec int64                            `json:"ttlForBearerTokenInRedisInSec"`
}

// CronJobConfig ...
type CronJobConfig struct {
	PendingDocExpiryInDays string `json:"pendingDocExpiryInDays"`
}

// DynamicConstants ...
type DynamicConstants struct {
	UploadDocumentAPITimeoutInSecs int64  `json:"uploadDocumentApiTimeoutInSecs"`
	MockDocumentTypesApiResponse   string `json:"mockGetDocumentTypesResponse"`
}

type EnableEncryptionFlags struct {
	EnableIDNumberEncryption     bool `json:"enableIDNumberEncryption"`
	EnableSwitchToHashedIDNumber bool `json:"enableSwitchToHashedIDNumber"`
	EnableIDNumberHashShadow     bool `json:"enableIDNumberHashShadow"`
}

type EncryptionFeatureFlags struct {
	EnableKMSClient                       bool                  `json:"enableKMSClient"`
	FlexiCardApplicationWorkflow          EnableEncryptionFlags `json:"flexiCardApplicationWorkflow"`
	BundledApplicationWorkflow            EnableEncryptionFlags `json:"bundledApplicationWorkflow"`
	AppianApplication                     EnableEncryptionFlags `json:"appianApplication"`
	BizLoanApplication                    EnableEncryptionFlags `json:"bizLoanApplication"`
	UpdateLoanOfferWorkflow               EnableEncryptionFlags `json:"updateLoanOfferWorkflow"`
	GetLoanApplicationDetailsByIdentifier EnableEncryptionFlags `json:"getLoanApplicationDetailsByIdentifier"`
}

// FeatureFlags ...
type FeatureFlags struct {
	AppianFlag                                             bool                   `json:"appianFlag"`
	PreviousApplicationFlag                                bool                   `json:"previousApplicationFlag"`
	UpdateOfferDetailsNewImpl                              bool                   `json:"updateOfferDetailsNewImpl"`
	WhiteListingCheckFlag                                  bool                   `json:"whiteListingCheckFlag"`
	CoolingPeriodCheckFlag                                 bool                   `json:"coolingPeriodCheckFlag"`
	ApplicantCurrentApplicationCheckFlag                   bool                   `json:"applicantCurrentApplicationCheckFlag"`
	BusinessValidationFlag                                 bool                   `json:"businessValidationFlag"`
	AddWhitelistFlagToApplicantFlag                        bool                   `json:"addWhitelistFlagToApplicantFlag"`
	EnableFlexiCardFlag                                    bool                   `json:"enableFlexiCardFlag"`
	EnableBalanceTransferFeatureFlag                       bool                   `json:"enableBalanceTransfer"`
	EnableFlexiCreditEligibilityWithCustomerDataFlag       bool                   `json:"enableFlexiCreditEligibilityWithCustomerDataFlag"`
	EnableFlexiCreditEligibilityWithAccountServiceDataFlag bool                   `json:"enableFlexiCreditEligibilityWithDepositDataFlag"`
	EnableFlexiCreditPreselectFlag                         bool                   `json:"enableFlexiCreditPreselectFlag"`
	EnableFlexiCreditExpireNotificaiton                    bool                   `json:"enableFlexiCreditExpireNotificaiton"`
	EnableBizFlexiCredit                                   bool                   `json:"enableBizFlexiCredit"`
	EnableBizBankStatementsUpload                          bool                   `json:"enableBizBankStatementsUpload"`
	EnableFlexiTermLoanApplicationWorkflow                 bool                   `json:"enableFlexiTermLoanApplicationWorkflow"`
	EnableRetryableStream                                  bool                   `json:"enableRetryableStream"`
	EnableBIFForNotification                               bool                   `json:"enableBIFForNotification"`
	EnableOfflineEcosystemDataFetcher                      bool                   `json:"enableOfflineEcosystemDataFetcher"`
	EnableCDEEligibleProgram                               bool                   `json:"enableCDEEligibleProgram"`
	EnableUtilisationTracker                               bool                   `json:"enableUtilisationTracker"`
	EnableGetECSRReportByID                                bool                   `json:"enableGetECSRReportByID"` // TODO: remove feature flag once enabled in prod (https://gxsbank.atlassian.net/browse/MSME-10148)
	EnablePrivateLimited                                   bool                   `json:"enablePrivateLimited"`
	EnableEnhancedRiskCheck                                bool                   `json:"enableEnhancedRiskCheck"`
	EnableActiveProfile                                    bool                   `json:"enableActiveProfile"`
	EnablePersonalBankStatementsUpload                     bool                   `json:"enablePersonalBankStatementsUpload"`
	EncryptionFeatureFlags                                 EncryptionFeatureFlags `json:"encryptionFeatureFlags"`
	EnableOverarchingLimit                                 bool                   `json:"enableOverarchingLimit"`
	EnableBizFlexiCreditCdePostIncomeApplicantIncome       bool                   `json:"enableBizFlexiCreditCdePostIncomeApplicantIncome"`
}

// LocaleConfig defines values related to localisation, variable values that may change with time/region
type LocaleConfig struct {
	DefaultTimeZoneOffset       int64  `json:"defaultTimeZoneOffset"`
	DefaultCountryCode          string `json:"defaultCountryCode"`
	DefaultCurrencyCode         string `json:"defaultCurrencyCode"`
	DefaultNotificationLanguage string `json:"defaultNotificationLanguage"`
	DefaultTimeZone             string `json:"defaultTimeZone"`
}

// ApplicationStatusTransitionKafkaConfig ...
type ApplicationStatusTransitionKafkaConfig struct {
	*sndconfig.KafkaConfig
	Enable              bool `json:"enable"`
	MaxRetryCount       int  `json:"maxRetryCount"`
	DelayInMilliSeconds int  `json:"delayInMilliSeconds"`
}

// CreditBureauEnquiryKafkaConfig ...
type CreditBureauEnquiryKafkaConfig struct {
	*sndconfig.KafkaConfig
	Enable              bool `json:"enable"`
	MaxRetryCount       int  `json:"maxRetryCount"`
	DelayInMilliSeconds int  `json:"delayInMilliSeconds"`
}

// CreditCardAccountCreationKafkaConfig ...
type CreditCardAccountCreationKafkaConfig struct {
	*sndconfig.KafkaConfig
	Enable              bool `json:"enable"`
	MaxRetryCount       int  `json:"maxRetryCount"`
	DelayInMilliSeconds int  `json:"delayInMilliSeconds"`
}

// LOCAccountCreationEventKafkaConfig ...
type LOCAccountCreationEventKafkaConfig struct {
	*sndconfig.KafkaConfig
	Enable              bool `json:"enable"`
	MaxRetryCount       int  `json:"maxRetryCount"`
	DelayInMilliSeconds int  `json:"delayInMilliSeconds"`
}

// IncomeDerivationEventKafkaConfig ...
type IncomeDerivationEventKafkaConfig struct {
	*sndconfig.KafkaConfig
	Enable              bool `json:"enable"`
	MaxRetryCount       int  `json:"maxRetryCount"`
	DelayInMilliSeconds int  `json:"delayInMilliSeconds"`
}

// LendingNameScreeningEventKafkaConfig ...
type LendingNameScreeningEventKafkaConfig struct {
	*sndconfig.KafkaConfig
	Enable              bool `json:"enable"`
	MaxRetryCount       int  `json:"maxRetryCount"`
	DelayInMilliSeconds int  `json:"delayInMilliSeconds"`
}

// EcddVerdictEventKafkaConfig ...
type EcddVerdictEventKafkaConfig struct {
	*sndconfig.KafkaConfig
	Enable              bool `json:"enable"`
	MaxRetryCount       int  `json:"maxRetryCount"`
	DelayInMilliSeconds int  `json:"delayInMilliSeconds"`
}

// FinexusUpdateKafkaConfig ...
type FinexusUpdateKafkaConfig struct {
	*sndconfig.KafkaConfig
	Enable              bool `json:"enable"`
	MaxRetryCount       int  `json:"maxRetryCount"`
	DelayInMilliSeconds int  `json:"delayInMilliSeconds"`
}

// KafkaConfig ...
type KafkaConfig struct {
	*sndconfig.KafkaConfig
	Enable              bool `json:"enable"`
	MaxRetryCount       int  `json:"maxRetryCount"`
	DelayInMilliSeconds int  `json:"delayInMilliSeconds"`
}

// ServiceConfig ...
type ServiceConfig struct {
	ServiceName     string     `json:"serviceName"`
	BaseURL         string     `json:"baseURL"`
	CircuitBreaker  *CBSetting `json:"circuitBreaker"`
	WithHealthCheck bool       `json:"withHealthCheck"`
}

// CBSetting is the circuit breaker configurations for storage
// It adds ErrorHandler to ignore context.Canceled errors.
type CBSetting struct {
	hystrix.CommandConfig
	IgnoredHTTPCode []int `json:"ignoredHTTPCode"`
}

// FileTypeConfig ...
type FileTypeConfig struct {
	MaxFileSizeInBytes string   `json:"maxFileSizeInBytes"`
	AllowedExtensions  []string `json:"allowedExtensions"`
}

// CreateApplicationConfig ...
type CreateApplicationConfig struct {
	ExperianDelay time.Duration `json:"experianDelay"`
}

// MLOpsConfig ...
type MLOpsConfig struct {
	IAMHostAddress                string                           `json:"iamHostAddress"`
	DMHostAddress                 string                           `json:"dmHostAddress"`
	CircuitConfig                 map[string]hystrix.CommandConfig `json:"circuitConfig"`
	RegisteredClientID            string                           `json:"registeredClientID"`
	RegisteredSecret              string                           `json:"registeredSecret"`
	Expiry                        int64                            `json:"expiry"`
	TTLForBearerTokenInRedisInSec int64                            `json:"ttlForBearerTokenInRedisInSec"`
	MLOPSAuthTokenRedisKey        string                           `json:"mlopsAuthTokenRedisKey"`
	Endpoints                     Endpoints                        `json:"endpoints"`
}

// Endpoints ...
type Endpoints struct {
	CreditDecisionPath string `json:"creditDecisionPath"`
	AuthTokenPath      string `json:"authTokenPath,omitempty"`
}

// TrackerConfig ...
type TrackerConfig struct {
	EventTypeToConfigMap map[string]*UtilizationTypeToEventConfig `json:"eventTypeToConfigMap"`
}

type UtilizationTypeToEventConfig struct {
	UtilizationTypeToEventConfigMap map[string]*EventConfig `json:"utilizationTypeToEventConfigMap"`
	SupportedTimeUnits              []string                `json:"supportedTimeUnits"`
	ProgramName                     string                  `json:"programName"`
}

// EventConfig ...
type EventConfig struct {
	TimeUnit               []string `json:"timeUnit"`
	LockDurationInMilliSec int64    `json:"lockDurationInMilliSec"`
}

// BizCampaignConfig ...
type BizCampaignConfig struct {
	Enable       bool    `json:"enable"`
	InterestRate float64 `json:"interestRate"`
}

// CacheTimeoutConfig defines the configuration for cache timeout durations.
// ListLoanDocumentTypesTTLInMinutes specifies the TTL for loan document types in minutes.
type CacheTimeoutConfig struct {
	ListLoanDocumentTypesTTLInMinutes int64 `json:"listLoanDocumentTypesTTLInMinutes"`
}

// KMSEncryptionConfig config required for kms access
type KMSEncryptionConfig struct {
	KeyId             string `json:"keyId"`
	DataEncryptionKey string `json:"dataEncryptionKey"`
	HashKey           string `json:"hashKey"`
}
