package server

import (
	"context"
	"fmt"
	"net/http"
	"time"

	creditDecisionEngineClient "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api/client"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/external/appian"
	"gitlab.com/gx-regional/dakota/lending/loan-app/external/datalake"
	"gitlab.com/gx-regional/dakota/lending/loan-app/external/hedwig"
	mlScoringService "gitlab.com/gx-regional/dakota/lending/loan-app/external/mlscoringservice/api/client"
	"gitlab.com/gx-regional/dakota/lending/loan-app/kafka/publishers"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common/ecosystem"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common/tracker"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/commonapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/flexitermloans/factory"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/incomederivation"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/handlerlogic/offers"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/implementation/activeprofile"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/jobs"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/bizflexicreditapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/bundledapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexicardapplication"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/base"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/gxb"
	gxsFlexiTermLoanApplication "gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/gxs"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/flexitermloanapplication/superbank"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/mldecisionscore"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/updatefinexus"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/workflow/updateofferdetails"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.com/gx-regional/dakota/lending/loan-app/workers"
	"gitlab.com/gx-regional/dakota/schemas/streams"
	"gitlab.com/gx-regional/dakota/schemas/streams/apis/loan_app_lifecycle_event"
	ekybClient "gitlab.myteksi.net/dbmy/ekyb-service/api/client"
	gdProxyServiceAPIClient "gitlab.myteksi.net/dbmy/gd-proxy/api/client"
	riskBrokerServiceAPIClient "gitlab.myteksi.net/dbmy/risk-broker/api/client"

	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/common/tracing"
	digicardCoreAPIClient "gitlab.myteksi.net/dakota/digicard/digicard-core/api/client"
	experianClient "gitlab.myteksi.net/dakota/experian-adapter/api/client"
	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/lending/common/countries"
	creditBureau "gitlab.myteksi.net/dakota/lending/credit-bureau/api/client"
	applicationService "gitlab.myteksi.net/dakota/lending/external/applicationservice"
	externalConfig "gitlab.myteksi.net/dakota/lending/external/config"
	accountService "gitlab.myteksi.net/dakota/lending/external/corebanking/accountservice"
	productMaster "gitlab.myteksi.net/dakota/lending/external/corebanking/productmaster"
	grabAPI "gitlab.myteksi.net/dakota/lending/external/grab/api/client"
	"gitlab.myteksi.net/dakota/lending/external/notification"
	customerMaster "gitlab.myteksi.net/dakota/lending/external/onboarding/customermaster"
	"gitlab.myteksi.net/dakota/lending/external/transactionStatement"
	loanCore "gitlab.myteksi.net/dakota/lending/loan-core/api/client"
	perfiosAdapter "gitlab.myteksi.net/dakota/lending/perfios-adapter/api/client"
	pigeonService "gitlab.myteksi.net/dakota/pigeon/api/client"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	servusStatsD "gitlab.myteksi.net/dakota/servus/v2/statsd"
	whitelistservice "gitlab.myteksi.net/dakota/whitelist-service/api/client"
	"gitlab.myteksi.net/dakota/workflowengine"
	logger "gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"
)

func registerClients(app *servus.Application) {
	app.MustRegister("client.customerMaster", CustomerMasterClient)
	app.MustRegister("client.grabAPI", GrabClient)
	app.MustRegister("client.productMaster", ProductMasterClient)
	app.MustRegister("client.perfiosAdapter", PerfiosAdapterClient)
	app.MustRegister("client.appian", AppianClient)
	app.MustRegister("client.hedwig", HedwigClient)
	app.MustRegister("client.pigeonService", PigeonServiceClient)
	app.MustRegister("client.datalake", DatalakeClient)
	app.MustRegister("client.notificationService", NotificationClient)
	app.MustRegister("client.accountService", AccountServiceClient)
	app.MustRegister("client.ekybService", EkybServiceClient)
	app.MustRegister("client.mlScoringService", MlScoringServiceClient)
	app.MustRegister("client.bizMlScoringService", BizMlScoringServiceClient)
	app.MustRegister("client.creditBureauService", creditBureauService)
	app.MustRegister("client.creditDecisionEng", creditDecisionEng)
	app.MustRegister("client.digicardCore", digicardCoreClient)
	app.MustRegister("client.whitelistService", WhitelistServiceClient)
	app.MustRegister("client.applicationService", ApplicationServiceClient)
	app.MustRegister("client.experian", ExperianClient)
	app.MustRegister("client.riskBroker", riskBrokerClient)
	app.MustRegister("client.gdProxyClient", gdProxyClient)
	app.MustRegister("client.transactionStatement", TransactionStatementClient)
	app.MustRegister("client.loanCoreClient", loanCoreService)
}

func registerCache(app *servus.Application, redisClient redis.Client) {
	app.MustRegister("client.redis", redisClient)
}

// NotificationClient is a function that returns a `notification.NotificationService`
func NotificationClient(appCfg *config.AppConfig, tracer tracing.Tracer, statsD servusStatsD.Client) (notification.NotificationService, error) {
	return notification.NewNotificationServiceClient(notification.Config{
		PigeonConfig: notification.PigeonConfig{
			BaseURL:       appCfg.PigeonConfig.BaseURL,
			CircuitConfig: appCfg.PigeonConfig.CircuitConfig,
		},
		HedwigConfig: notification.HedwigConfig{
			HostAddress:         appCfg.Hedwig.ClientConfig.HostAddress,
			CircuitConfig:       appCfg.Hedwig.ClientConfig.CircuitConfig,
			ServiceName:         appCfg.Hedwig.ClientConfig.ServiceName,
			ServiceKey:          appCfg.Hedwig.ClientConfig.ServiceKey,
			PushServerPath:      appCfg.Hedwig.ClientConfig.PushServerPath,
			PushInboxServerPath: appCfg.Hedwig.ClientConfig.PushInboxServerPath,
			EmailServerPath:     appCfg.Hedwig.ClientConfig.EmailServerPath,
		},
		DefaultCountryCode: appCfg.LocaleConfig.DefaultCountryCode,
	}, tracer)
}

// TransactionStatementClient ...
func TransactionStatementClient(appCfg *config.AppConfig, logger logger.YallLogger, tracer tracing.Tracer, statsD servusStatsD.Client) (transactionStatement.TransactionStatements, error) {
	return transactionStatement.NewClient(&externalConfig.ServiceConfig{
		BaseURL:            appCfg.TransactionStatement.BaseURL,
		ServiceName:        appCfg.TransactionStatement.ServiceName,
		DefaultCountryCode: appCfg.LocaleConfig.DefaultCountryCode,
		WithHealthCheck:    appCfg.TransactionStatement.WithHealthCheck,
		CircuitBreaker: &externalConfig.CBSetting{
			CommandConfig:   appCfg.TransactionStatement.CircuitBreaker.CommandConfig,
			IgnoredHTTPCode: appCfg.TransactionStatement.CircuitBreaker.IgnoredHTTPCode,
		},
	}, tracer, logger, statsD)
}

// ProductMasterClient ...
func ProductMasterClient(appCfg *config.AppConfig, logger logger.YallLogger, tracer tracing.Tracer, statsD servusStatsD.Client) (productMaster.ProductMaster, error) {
	return productMaster.NewClient(&externalConfig.ServiceConfig{
		BaseURL:            appCfg.ProductMaster.BaseURL,
		ServiceName:        appCfg.ProductMaster.ServiceName,
		DefaultCountryCode: appCfg.LocaleConfig.DefaultCountryCode,
		WithHealthCheck:    appCfg.ProductMaster.WithHealthCheck,
		CircuitBreaker: &externalConfig.CBSetting{
			CommandConfig:   appCfg.ProductMaster.CircuitBreaker.CommandConfig,
			IgnoredHTTPCode: appCfg.ProductMaster.CircuitBreaker.IgnoredHTTPCode,
		},
	}, tracer, logger, statsD)
}

// HedwigClient initialization method
func HedwigClient(appCfg *config.AppConfig, tracer tracing.Tracer, statsD servusStatsD.Client) (*hedwig.Client, error) {
	return hedwig.NewHedwigClient(&appCfg.Hedwig.ClientConfig, tracer, statsD)
}

// CustomerMasterClient ...
func CustomerMasterClient(appCfg *config.AppConfig, logger logger.YallLogger, tracer tracing.Tracer, statsD servusStatsD.Client) (customerMaster.CustomerMaster, error) {
	return customerMaster.NewClient(&externalConfig.ServiceConfig{
		BaseURL:            appCfg.CustomerMaster.BaseURL,
		ServiceName:        appCfg.CustomerMaster.ServiceName,
		DefaultCountryCode: appCfg.LocaleConfig.DefaultCountryCode,
		WithHealthCheck:    appCfg.CustomerMaster.WithHealthCheck,
		CircuitBreaker: &externalConfig.CBSetting{
			CommandConfig:   appCfg.CustomerMaster.CircuitBreaker.CommandConfig,
			IgnoredHTTPCode: appCfg.CustomerMaster.CircuitBreaker.IgnoredHTTPCode,
		},
	}, tracer, logger, statsD)
}

// GrabClient ...
func GrabClient(appCfg *config.AppConfig, tracer tracing.Tracer, statsD servusStatsD.Client) (*grabAPI.GrabClient, error) {
	return grabAPI.NewGrabClient(
		appCfg.GrabConfig.BaseURL,
		klient.WithServiceName("grab"),
		klient.WithHTTPClient(makeHTTPClient(appCfg)),
		klient.WithTracing(tracer),
		klient.WithCircuitConfig(appCfg.GrabConfig.CircuitConfig),
		klient.WithStatsDClient(statsD),
		klient.WithoutHealthCheck(),
	)
}

// PigeonServiceClient ...
func PigeonServiceClient(appCfg *config.AppConfig, tracer tracing.Tracer, statsD servusStatsD.Client) (*pigeonService.PigeonClient, error) {
	return pigeonService.NewPigeonClient(
		appCfg.PigeonConfig.BaseURL,
		klient.WithServiceName("pigeon"),
		klient.WithHTTPClient(makeHTTPClient(appCfg)),
		klient.WithTracing(tracer),
		klient.WithCircuitConfig(appCfg.PigeonConfig.CircuitConfig),
		klient.WithStatsDClient(statsD),
	)
}

// AppianClient ...
func AppianClient(appCfg *config.AppConfig, tracer tracing.Tracer, statsD servusStatsD.Client) (*appian.Client, error) {
	return appian.NewAppianClient(&appCfg.AppianConfig, tracer, statsD)
}

// DatalakeClient ...
func DatalakeClient(appCfg *config.AppConfig, tracer tracing.Tracer, statsD servusStatsD.Client) (*datalake.Client, error) {
	return datalake.NewDatalakeClient(&appCfg.DatalakeConfig, tracer, statsD)
}

// AccountServiceClient ...
func AccountServiceClient(appCfg *config.AppConfig, logger logger.YallLogger, tracer tracing.Tracer, statsD servusStatsD.Client) (accountService.AccountService, error) {
	return accountService.NewClient(&externalConfig.ServiceConfig{
		BaseURL:            appCfg.AccountService.BaseURL,
		ServiceName:        appCfg.AccountService.ServiceName,
		DefaultCountryCode: appCfg.LocaleConfig.DefaultCountryCode,
		WithHealthCheck:    appCfg.AccountService.WithHealthCheck,
		CircuitBreaker: &externalConfig.CBSetting{
			CommandConfig:   appCfg.AccountService.CircuitBreaker.CommandConfig,
			IgnoredHTTPCode: appCfg.AccountService.CircuitBreaker.IgnoredHTTPCode,
		},
	}, tracer, logger, statsD)
}

// EkybServiceClient ...
func EkybServiceClient(appCfg *config.AppConfig, app *servus.Application) (*ekybClient.EkybServiceClient, error) {
	return ekybClient.NewEkybServiceClient(
		appCfg.EkybService.BaseURL,
		klientOptions("EkybService", appCfg, app.GetTracer(), app.GetLogger(), appCfg.CreditBureau.WithHealthCheck, true, app.GetStatsD(), app.GetDogStatsD())...)
}

// MlScoringServiceClient : ml scoring service client
func MlScoringServiceClient(appCfg *config.AppConfig, tracer tracing.Tracer) (*mlScoringService.MlScoringServiceClient, error) {
	return mlScoringService.NewMlScoringServiceClient(
		appCfg.MlScoringConfig.BaseURL,
		klient.WithHTTPClient(makeHTTPClient(appCfg)),
		klient.WithTracing(tracer),
		klient.WithoutHealthCheck(),
	)
}

// BizMlScoringServiceClient : ml scoring service client
func BizMlScoringServiceClient(appCfg *config.AppConfig, logger logger.YallLogger, tracer tracing.Tracer, statsD servusStatsD.Client) (*mlScoringService.MlScoringServiceClient, error) {
	return mlScoringService.NewMlScoringServiceClient(
		appCfg.BizMlScoringConfig.BaseURL,
		klient.WithHTTPClient(makeHTTPClient(appCfg)),
		klient.WithTracing(tracer))
}

// creditBureauService credit bureau service client creation
func creditBureauService(appCfg *config.AppConfig, app *servus.Application) (*creditBureau.CreditBureauClient, error) {
	return creditBureau.NewCreditBureauClient(
		appCfg.CreditBureau.BaseURL,
		klientOptions("CreditBureau", appCfg, app.GetTracer(), app.GetLogger(), appCfg.CreditBureau.WithHealthCheck, true, app.GetStatsD(), app.GetDogStatsD())...)
}

// loanCoreService loan core  service client creation
func loanCoreService(appCfg *config.AppConfig, app *servus.Application) (*loanCore.LoanCoreClient, error) {
	return loanCore.NewLoanCoreClient(
		appCfg.LoanCore.BaseURL,
		klientOptions("LoanCore", appCfg, app.GetTracer(), app.GetLogger(), appCfg.LoanCore.WithHealthCheck, true, app.GetStatsD(), app.GetDogStatsD())...)
}

// creditDecisionEng
func creditDecisionEng(appCfg *config.AppConfig, app *servus.Application) (*creditDecisionEngineClient.CrDecisionEngClient, error) {
	return creditDecisionEngineClient.NewCrDecisionEngClient(
		appCfg.CrDecisionEng.BaseURL,
		klientOptions("CrDecisionEng", appCfg, app.GetTracer(), app.GetLogger(), appCfg.CrDecisionEng.WithHealthCheck, true, app.GetStatsD(), app.GetDogStatsD())...)
}

// ApplicationServiceClient ...
func ApplicationServiceClient(appCfg *config.AppConfig, logger logger.YallLogger, tracer tracing.Tracer, statsD servusStatsD.Client) (applicationService.ApplicationService, error) {
	return applicationService.NewClient(&externalConfig.ServiceConfig{
		BaseURL:            appCfg.ApplicationService.BaseURL,
		ServiceName:        appCfg.ApplicationService.ServiceName,
		DefaultCountryCode: appCfg.LocaleConfig.DefaultCountryCode,
		WithHealthCheck:    appCfg.ApplicationService.WithHealthCheck,
		CircuitBreaker: &externalConfig.CBSetting{
			CommandConfig:   appCfg.ApplicationService.CircuitBreaker.CommandConfig,
			IgnoredHTTPCode: appCfg.ApplicationService.CircuitBreaker.IgnoredHTTPCode,
		},
	}, tracer, logger, statsD)
}

// ExperianClient ...
func ExperianClient(appCfg *config.AppConfig, app *servus.Application) (*experianClient.ExperianAdapterClient, error) {
	return experianClient.NewExperianAdapterClient(
		appCfg.ExperianAdapter.BaseURL,
		klientOptions("ExperianAdapter", appCfg, app.GetTracer(), app.GetLogger(), appCfg.ExperianAdapter.WithHealthCheck, true, app.GetStatsD(), app.GetDogStatsD())...)
}

// WhitelistServiceClient ...
func WhitelistServiceClient(appCfg *config.AppConfig, app *servus.Application) (*whitelistservice.WhitelistServiceClient, error) {
	return whitelistservice.NewWhitelistServiceClient(
		appCfg.WhitelistService.BaseURL,
		klientOptions("WhitelistService", appCfg, app.GetTracer(), app.GetLogger(), appCfg.WhitelistService.WithHealthCheck, true, app.GetStatsD(), app.GetDogStatsD())...)
}

func digicardCoreClient(appCfg *config.AppConfig, app *servus.Application) (*digicardCoreAPIClient.DigicardCoreClient, error) {
	return digicardCoreAPIClient.NewDigicardCoreClient(
		appCfg.DigicardCore.BaseURL,
		klientOptions("DigicardCore", appCfg, app.GetTracer(), app.GetLogger(), appCfg.DigicardCore.WithHealthCheck, true, app.GetStatsD(), app.GetDogStatsD())...)
}

// riskBrokerClient ...
//
//nolint:dupl
func riskBrokerClient(appCfg *config.AppConfig, logger logger.YallLogger, tracer tracing.Tracer, statsD servusStatsD.Client) (*riskBrokerServiceAPIClient.RiskBrokerClient, error) {
	return riskBrokerServiceAPIClient.NewRiskBrokerClient(
		appCfg.RiskBrokerServiceConfig.BaseURL,
		externalConfig.KlientOptions("risk-broker", &externalConfig.ServiceConfig{
			BaseURL:            appCfg.RiskBrokerServiceConfig.BaseURL,
			ServiceName:        appCfg.RiskBrokerServiceConfig.ServiceName,
			DefaultCountryCode: appCfg.LocaleConfig.DefaultCountryCode,
			WithHealthCheck:    appCfg.RiskBrokerServiceConfig.WithHealthCheck,
			CircuitBreaker: &externalConfig.CBSetting{
				CommandConfig:   appCfg.RiskBrokerServiceConfig.CircuitBreaker.CommandConfig,
				IgnoredHTTPCode: appCfg.RiskBrokerServiceConfig.CircuitBreaker.IgnoredHTTPCode,
			},
		}, tracer, logger, appCfg.RiskBrokerServiceConfig.WithHealthCheck, true, statsD)...,
	)
}

// gdProxyClient ...
//
//nolint:dupl
func gdProxyClient(appCfg *config.AppConfig, logger logger.YallLogger, tracer tracing.Tracer, statsD servusStatsD.Client) (*gdProxyServiceAPIClient.GdProxyClient, error) {
	return gdProxyServiceAPIClient.NewGdProxyClient(
		appCfg.GdProxyServiceConfig.BaseURL,
		externalConfig.KlientOptions("gd-proxy", &externalConfig.ServiceConfig{
			BaseURL:            appCfg.GdProxyServiceConfig.BaseURL,
			ServiceName:        appCfg.GdProxyServiceConfig.ServiceName,
			DefaultCountryCode: appCfg.LocaleConfig.DefaultCountryCode,
			WithHealthCheck:    appCfg.GdProxyServiceConfig.WithHealthCheck,
			CircuitBreaker: &externalConfig.CBSetting{
				CommandConfig:   appCfg.GdProxyServiceConfig.CircuitBreaker.CommandConfig,
				IgnoredHTTPCode: appCfg.GdProxyServiceConfig.CircuitBreaker.IgnoredHTTPCode,
			},
		}, tracer, logger, appCfg.GdProxyServiceConfig.WithHealthCheck, true, statsD)...,
	)
}

func makeHTTPClient(appCfg *config.AppConfig) *http.Client {
	httpClient := &http.Client{
		Transport: &http.Transport{
			MaxIdleConnsPerHost: appCfg.LoanAppConfig.ClientConfig.MaxIdleConnsPerHost,
			IdleConnTimeout:     time.Duration(appCfg.LoanAppConfig.ClientConfig.IdleConnTimeoutInMillis) * time.Millisecond,
		},
		Timeout: time.Duration(appCfg.LoanAppConfig.ClientConfig.TimeoutInMillis) * time.Millisecond,
	}
	return httpClient
}

func registerFlexiTermLoanApplicationHandlerLogic(app *servus.Application) {
	app.MustRegister("logic.FlexiTermLoanApplication", flexitermloans.NewFlexiTermLoanApplication)
}

func registerHandlerLogic(app *servus.Application) {
	app.MustRegister("logic.IncomeDerivation", incomederivation.NewIncomeDerivation)
	app.MustRegister("logic.CommonApplication", commonapplication.NewCommonApplication)
	app.MustRegister("logic.BusinessVerification", incomederivation.NewBusinessVerification)
}

func registerLogicHandler(cfg *config.AppConfig, app *servus.Application) {
	app.MustRegister("logic.AnalyserFactory", factory.NewLogicAnalyserFactory(cfg.LocaleConfig.DefaultCountryCode))
}

func registerQueryGenerator(cfg *config.AppConfig, app *servus.Application) {
	app.MustRegister("flexiCardQueryGenerator", flexicardapplication.NewFlexiCardQueryGeneratorFactory(cfg.LocaleConfig.DefaultCountryCode))
	app.MustRegister("flexiLoanQueryGenerator", gxsFlexiTermLoanApplication.NewFlexiLoanQueryGeneratorFactory(cfg.LocaleConfig.DefaultCountryCode))
	app.MustRegister("bundledApplicationQueryGenerator", bundledapplication.NewBundledApplicationQueryGeneratorFactory(cfg.LocaleConfig.DefaultCountryCode))
	app.MustRegister("bizFlexiCreditQueryGenerator", bizflexicreditapplication.NewBizFlexiCreditQueryGeneratorFactory(cfg.LocaleConfig.DefaultCountryCode))
}

func registerOfferDetailsClient(app *servus.Application) {
	app.MustRegister("client.OfferDetails", offers.NewOfferDetails)
}

func registerTracker(app *servus.Application) {
	app.MustRegister("eventUsageTracker", RegisterTracker)
}

func RegisterTracker(redisClient redis.Client, trackerDAO storage.IEventUsageTrackerDAO, auditTrailDAO storage.IAuditTrailEventDAO, databaseStore storage.DatabaseStore, appCfg *config.AppConfig, statsD servusStatsD.Client) tracker.Client {
	return tracker.RegisterEventUsageTracker(redisClient,
		appCfg.EventTrackerConfig,
		trackerDAO,
		auditTrailDAO,
		databaseStore,
		appCfg,
		statsD,
	)
}

func registerEcosystemDataFetcher(cfg *config.AppConfig, app *servus.Application) {
	app.MustRegister("ecosystemDataFetcher", ecosystem.NewClient(ecosystem.EcosystemConfig{
		IsOfflineEnabled: cfg.FeatureFlags.EnableOfflineEcosystemDataFetcher,
	}))
}

func registerStorage(app *servus.Application) {
	app.MustRegister("storage.ApplicantDao", storage.ApplicantDao)
	app.MustRegister("storage.ApplicationDao", storage.ApplicationDao)
	app.MustRegister("storage.RequestLogDao", storage.RequestLogDao)
	app.MustRegister("storage.AuditTrailEventDao", storage.AuditTrailEventDao)
	app.MustRegister("storage.EventUsageTrackerDao", storage.EventUsageTrackerDao)
	app.MustRegister("DBStore", &storage.DBStore{})
}

func registerKafkaWriter(appConfig *config.AppConfig, app *servus.Application) {
	kafkaWriter, err := streams.NewStaticWriter(context.Background(), constants.LoanApplicationLifecycleKafkaPublisherLogTag,
		*appConfig.LoanAppKafkaConfig.KafkaConfig, &loan_app_lifecycle_event.LoanAppLifecycleEvent{})
	if err != nil {
		panic(fmt.Sprintf("failed to create loan application stream writer, err=%s", err))
	}
	app.MustRegister("writer.loanApplicationLifecycleStream", kafkaWriter)
}

func registerPublishers(app *servus.Application) {
	app.MustRegister("publishers.loanAppLifecycleEvent", &publishers.LoanApplicationLifecyclePublisher{})
}

func registerJobSchedulers(appCfg *config.AppConfig, app *servus.Application) {
	ctx := context.Background()
	filters := []data.Condition{
		data.EqualTo("IsActive", 1),
	}
	jobsToSchedule, err := storage.SchedulerJobDao.Find(ctx, filters...)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.JobSchedulerInitializationLogTag, fmt.Sprintf("Error in fetching Db data, err:%s", err.Error()))
		panic(fmt.Sprintf("failed to fetch data from db, err: %s", err.Error()))
	}

	if err == data.ErrNoData || len(jobsToSchedule) == 0 {
		slog.FromContext(ctx).Info(constants.JobSchedulerInitializationLogTag, "no active job to schedule")
		return
	}

	// iterate over all jobs and start them async
	for _, jobSchedulerDetail := range jobsToSchedule {
		name, scheduler := jobs.SchedulerFactory(jobSchedulerDetail.SchedulerID)

		if name == "" {
			continue
		}
		app.MustRegister(name, scheduler)
		scheduler.ScheduleJob(ctx, appCfg, jobSchedulerDetail)
	}
}

func initializeWorkflows(app *servus.Application, appCfg *config.AppConfig) {
	// mlDecisionScore workflow
	mlDecisionScoreWorkflow := &mldecisionscore.WorkflowImpl{}
	app.MustRegister("workflow.mlDecisionScoreWorkflow", mlDecisionScoreWorkflow)
	mlDecisionScoreWorkflow.Register()

	// flexi term loan application workflow
	initializeFlexiTermLoanApplicationWorkflow(app, appCfg)

	// update offer (loan/BT/FC) details workflow
	updateOfferDetailsWorkflow := &updateofferdetails.WorkflowImpl{}
	app.MustRegister("workflow.updateOfferDetails", updateOfferDetailsWorkflow)
	updateOfferDetailsWorkflow.Register()

	// biz flexi credit application workflow
	bizFlexiCreditApplicationWorkflow := &bizflexicreditapplication.WorkflowImpl{}
	app.MustRegister("workflow.bizFlexiCreditApplication", bizFlexiCreditApplicationWorkflow)
	bizFlexiCreditApplicationWorkflow.Register()

	//create flexi-card workflow
	createFlexiCardApplicationWorkflow := &flexicardapplication.WorkflowImpl{}
	app.MustRegister("workflow.createFlexiCardApplicationWorkflow", createFlexiCardApplicationWorkflow)
	createFlexiCardApplicationWorkflow.Register()

	//bundled application workflow
	bundledApplicationWorkflow := &bundledapplication.WorkflowImpl{}
	app.MustRegister("workflow.bundledApplicationWorkflow", bundledApplicationWorkflow)
	bundledApplicationWorkflow.Register()

	// updateFinexus workflow
	updateFinexusWorkflow := &updatefinexus.WorkflowImpl{}
	app.MustRegister("workflow.updateFinexus", updateFinexusWorkflow)
	updateFinexusWorkflow.Register()

	workflowengine.StartWorkers()
}

// initDynamicConfigConstants ...
func initDynamicConfigConstants(appCfg *config.AppConfig) {
	initLocaleConfig(appCfg)
	constants.CoolingPeriodMap = appCfg.CoolingPeriod
	constants.StatusReasonToPercentageMap = appCfg.StatusReasonToPercentageMapping
	constants.EnableBalanceTransferFeatureFlag = appCfg.FeatureFlags.EnableBalanceTransferFeatureFlag
	constants.UploadDocumentAPITimeoutInSecs = appCfg.DynamicConstants.UploadDocumentAPITimeoutInSecs
	constants.EnableBizFlexiCredit = appCfg.FeatureFlags.EnableBizFlexiCredit
	constants.EnableBIFForNotification = appCfg.FeatureFlags.EnableBIFForNotification
	constants.EnableBizBankStatementsUpload = appCfg.FeatureFlags.EnableBizBankStatementsUpload
	constants.EnableCDEEligibleProgram = appCfg.FeatureFlags.EnableCDEEligibleProgram
	constants.EnableUtilisationTracker = appCfg.FeatureFlags.EnableUtilisationTracker
	constants.EnableGetECSRReportByID = appCfg.FeatureFlags.EnableGetECSRReportByID
	constants.EnablePrivateLimited = appCfg.FeatureFlags.EnablePrivateLimited
	constants.EnableActiveProfile = appCfg.FeatureFlags.EnableActiveProfile
	constants.ListLoanDocumentTypesTTLInMinutes = appCfg.CacheTimeoutConfig.ListLoanDocumentTypesTTLInMinutes
	constants.EnablePersonalBankStatementsUpload = appCfg.FeatureFlags.EnablePersonalBankStatementsUpload
}

// PerfiosAdapterClient ...
func PerfiosAdapterClient(appCfg *config.AppConfig, app *servus.Application) (*perfiosAdapter.PerfiosAdapterClient, error) {
	kOptions := klientOptions("PerfiosAdapter", appCfg, app.GetTracer(), app.GetLogger(),
		appCfg.PerfiosAdapter.WithHealthCheck, true, app.GetStatsD(), app.GetDogStatsD())
	kOptions = append(kOptions, klient.WithHTTPClient(makeHTTPClient(appCfg)))
	return perfiosAdapter.NewPerfiosAdapterClient(
		appCfg.PerfiosAdapter.BaseURL,
		kOptions...)
}

// NotificationServiceClient ...
func NotificationServiceClient(appCfg *config.AppConfig, tracer tracing.Tracer) (notification.NotificationService, error) {
	return notification.NewNotificationServiceClient(notification.Config{
		PigeonConfig: notification.PigeonConfig{
			BaseURL:       appCfg.PigeonConfig.BaseURL,
			CircuitConfig: appCfg.PigeonConfig.CircuitConfig,
		},
		HedwigConfig: notification.HedwigConfig{
			HostAddress:         appCfg.Hedwig.ClientConfig.HostAddress,
			PushServerPath:      appCfg.Hedwig.ClientConfig.PushServerPath,
			PushInboxServerPath: appCfg.Hedwig.ClientConfig.PushInboxServerPath,
		},
		DefaultCountryCode: appCfg.LocaleConfig.DefaultCountryCode,
	}, tracer)
}

func initLocaleConfig(appCfg *config.AppConfig) {
	constants.DefaultTimeZoneOffset = appCfg.LocaleConfig.DefaultTimeZoneOffset
	constants.DefaultCountryCode = appCfg.LocaleConfig.DefaultCountryCode
	constants.DefaultCurrencyCode = appCfg.LocaleConfig.DefaultCurrencyCode
	constants.DefaultNotificationLanguage = appCfg.LocaleConfig.DefaultNotificationLanguage
	constants.DefaultTimeZone = appCfg.LocaleConfig.DefaultTimeZone
}

func initializeFlexiTermLoanApplicationWorkflow(app *servus.Application, appCfg *config.AppConfig) {
	baseImplWorkflow := &base.WorkflowImpl{}
	baseImplWorkflow.WorkflowInterface = baseImplWorkflow
	app.MustRegister("workflow.createFlexiTermLoanApplicationBaseWorkflow", baseImplWorkflow)

	switch constants.DefaultCountryCode {
	case countries.ID:
		if appCfg.FeatureFlags.EnableFlexiTermLoanApplicationWorkflow {
			createSuperbankFlexiTermLoanApplicationWorkflow := &superbank.WorkflowImpl{WorkflowImpl: *baseImplWorkflow}
			createSuperbankFlexiTermLoanApplicationWorkflow.WorkflowInterface = createSuperbankFlexiTermLoanApplicationWorkflow
			app.MustRegister("workflow.createFlexiTermLoanApplicationWorkflow", createSuperbankFlexiTermLoanApplicationWorkflow)
			createSuperbankFlexiTermLoanApplicationWorkflow.Register()
		}
	case countries.MY:
		if appCfg.FeatureFlags.EnableFlexiTermLoanApplicationWorkflow {
			createGXBFlexiTermLoanApplicationWorkflow := &gxb.WorkflowImpl{WorkflowImpl: *baseImplWorkflow}
			createGXBFlexiTermLoanApplicationWorkflow.WorkflowInterface = createGXBFlexiTermLoanApplicationWorkflow
			app.MustRegister("workflow.createFlexiTermLoanApplicationWorkflow", createGXBFlexiTermLoanApplicationWorkflow)
			createGXBFlexiTermLoanApplicationWorkflow.Register()
		}
	case countries.SG:
		if appCfg.FeatureFlags.EnableFlexiTermLoanApplicationWorkflow {
			createGXSFlexiLoanApplicationWorkFlow := &gxsFlexiTermLoanApplication.WorkflowImpl{WorkflowImpl: *baseImplWorkflow}
			createGXSFlexiLoanApplicationWorkFlow.WorkflowInterface = createGXSFlexiLoanApplicationWorkFlow
			app.MustRegister("workflow.createFlexiTermLoanApplicationWorkflow", createGXSFlexiLoanApplicationWorkFlow)
			createGXSFlexiLoanApplicationWorkFlow.Register()
		} else {
			app.MustRegister("workflow.createFlexiTermLoanApplicationWorkflow", baseImplWorkflow)
		}
	default:
		app.MustRegister("workflow.createFlexiTermLoanApplicationWorkflow", baseImplWorkflow)
	}
}

func registerActiveProfileHandler(cfg *config.AppConfig, app *servus.Application) {
	app.MustRegister("logic.ActiveProfile", activeprofile.NewActiveProfileImpl(cfg, app))
}

func initWorkers(app *servus.Application) {
	app.MustRegister("workerInitializer", workersInitializer)
}

func workersInitializer(
	appCfg *config.AppConfig,
	app *servus.Application,
	applicantDAO storage.IApplicantDAO,
	redisClient redis.Client,
	databaseStore storage.DatabaseStore,
) *workers.WorkerMgr {
	ctx := slog.NewContextWithLogger(context.Background(), app.GetLogger())
	wm := workers.NewWorkerMgr(ctx)
	if appCfg.BackfillEncryptedAndHashedID.Enabled {
		appPopulateEncHashIDNumberWorker := workers.NewPopulateEncHashIDNumberWorker(
			ctx,
			*appCfg,
			applicantDAO,
			redisClient,
			databaseStore,
		)
		wm.AddWorker(appPopulateEncHashIDNumberWorker)
	}
	wm.StartWorkers()
	return &wm
}
