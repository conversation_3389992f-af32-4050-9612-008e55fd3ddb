package validation

import (
	"testing"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
)

func TestValidateApplicantPortfolioDetails(t *testing.T) {
	tests := []struct {
		name    string
		args    *api.ApplicantPortfolioDetails
		wantErr bool
	}{
		{
			name: "should not return error when ApplicantPortfolioDetails validation is success",
			args: &api.ApplicantPortfolioDetails{
				Status: "SUCCESS",
			},
			wantErr: false,
		},
		{
			name: "should return error when ApplicantPortfolioDetails status is empty",
			args: &api.ApplicantPortfolioDetails{
				Status: "",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			if err := ValidateApplicantPortfolioDetails(test.args); (err != nil) != test.wantErr {
				t.Errorf("ValidateApplicantPortfolioDetails() error = %v, wantErr %v", err, test.wantErr)
			}
		})
	}
}
