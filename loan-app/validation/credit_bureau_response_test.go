package validation

import (
	"testing"

	"github.com/stretchr/testify/assert"
	crDecisionEng "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/utils"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestValidateCreditBureauResponse(t *testing.T) {
	type args struct {
		creditBureauResponse *api.CreditBureauResponse
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "should not return error when CreditBureauResponse validation is success",
			args:    args{creditBureauResponse: &api.CreditBureauResponse{Status: "SUCCESS"}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			if err := ValidateCreditBureauResponse(test.args.creditBureauResponse); (err != nil) != test.wantErr {
				t.Errorf("ValidateCreditDecisionResponse() = %v, want %v", err, test.wantErr)
			}
		})
	}
}

func TestValidateResponseStatus(t *testing.T) {
	type args struct {
		status string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "should return error when ResponseStatus is Missing",
			args:    args{status: ""},
			wantErr: true,
		},
		{
			name:    "should not return error when ResponseStatus is valid",
			args:    args{status: "SUCCESS"},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			if err := ValidateResponseStatus(test.args.status); (err != nil) != test.wantErr {
				t.Errorf("ValidateResponseStatus() = %v, want %v", err, test.wantErr)
			}
		})
	}
}

func Test_ValidatePreBureauResponse(t *testing.T) {
	scenarios := []struct {
		desc         string
		response     crDecisionEng.FICOCreditDecisionResponse
		productTypes []crDecisionEng.ProductType
		expectedErr  []servus.ErrorDetail
	}{
		{
			desc: "Success: preBureauResponse valid",
			response: crDecisionEng.FICOCreditDecisionResponse{
				Message: &crDecisionEng.Message{
					Status: utils.GetPointer("SUCCESS"),
					Application: &crDecisionEng.Application{
						Products: []crDecisionEng.Product{{
							ProductType:     utils.GetPointer(crDecisionEng.ProductType_FLEXI_CREDIT_CARD),
							ProductDecision: &crDecisionEng.ProductDecision{RecommendedCreditDecision: utils.GetPointer("APPROVE")},
						}},
					},
				},
			},
			productTypes: []crDecisionEng.ProductType{crDecisionEng.ProductType_FLEXI_CREDIT_CARD},
			expectedErr:  nil,
		},
		{
			desc:        "Failure: preBureauResponse not present",
			expectedErr: []servus.ErrorDetail{{ErrorCode: commonErr.ErrMissingCreditDecisionResponse.Code, Message: commonErr.ErrMissingCreditDecisionResponse.Message}},
		},
		{
			desc: "Failure: status not present",
			response: crDecisionEng.FICOCreditDecisionResponse{
				Message: &crDecisionEng.Message{
					Status: nil,
					Application: &crDecisionEng.Application{
						Products: []crDecisionEng.Product{{
							ProductType:     utils.GetPointer(crDecisionEng.ProductType_FLEXI_CREDIT_CARD),
							ProductDecision: &crDecisionEng.ProductDecision{RecommendedCreditDecision: utils.GetPointer("APPROVE")},
						}},
					},
				},
			},
			expectedErr: []servus.ErrorDetail{{ErrorCode: commonErr.ErrMissingStatus.Code, Message: commonErr.ErrMissingStatus.Message}},
		},
		{
			desc: "Failure: Status invalid",
			response: crDecisionEng.FICOCreditDecisionResponse{
				Message: &crDecisionEng.Message{
					Status: utils.GetPointer("NOT SUCCESS"),
					Application: &crDecisionEng.Application{
						Products: []crDecisionEng.Product{{
							ProductType:     utils.GetPointer(crDecisionEng.ProductType_FLEXI_CREDIT_CARD),
							ProductDecision: &crDecisionEng.ProductDecision{RecommendedCreditDecision: utils.GetPointer("APPROVE")},
						}},
					},
				},
			},
			expectedErr: []servus.ErrorDetail{{ErrorCode: commonErr.ErrInvalidCrDecisionEngResponseStatus.Code, Message: commonErr.ErrInvalidCrDecisionEngResponseStatus.Message}},
		},
		{
			desc: "Failure: Product missing",
			response: crDecisionEng.FICOCreditDecisionResponse{
				Message: &crDecisionEng.Message{
					Status: utils.GetPointer("SUCCESS"),
					Application: &crDecisionEng.Application{
						Products: nil,
					},
				},
			},
			expectedErr: []servus.ErrorDetail{{ErrorCode: commonErr.ErrMissingProducts.Code, Message: commonErr.ErrMissingProducts.Message}},
		},
		{
			desc: "Failure: product decision not found",
			response: crDecisionEng.FICOCreditDecisionResponse{
				Message: &crDecisionEng.Message{
					Status: utils.GetPointer("SUCCESS"),
					Application: &crDecisionEng.Application{
						Products: []crDecisionEng.Product{{
							ProductType:     utils.GetPointer(crDecisionEng.ProductType_FLEXI_CREDIT_CARD),
							ProductDecision: nil,
						}},
					},
				},
			},
			productTypes: []crDecisionEng.ProductType{crDecisionEng.ProductType_FLEXI_CREDIT_CARD},
			expectedErr:  []servus.ErrorDetail{{ErrorCode: commonErr.ErrMissingProductDecision.Code, Message: commonErr.ErrMissingProductDecision.Message}},
		},
		{
			desc: "Failure: RecommendedCreditDecision invalid",
			response: crDecisionEng.FICOCreditDecisionResponse{
				Message: &crDecisionEng.Message{
					Status: utils.GetPointer("SUCCESS"),
					Application: &crDecisionEng.Application{
						Products: []crDecisionEng.Product{{
							ProductType:     utils.GetPointer(crDecisionEng.ProductType_FLEXI_CREDIT_CARD),
							ProductDecision: &crDecisionEng.ProductDecision{RecommendedCreditDecision: utils.GetPointer("INVALID")},
						}},
					},
				},
			},
			productTypes: []crDecisionEng.ProductType{crDecisionEng.ProductType_FLEXI_CREDIT_CARD},
			expectedErr:  nil,
		},
	}
	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			err := ValidatePreBureauResponse(&scenario.response, scenario.productTypes)
			if scenario.expectedErr != nil {
				assert.NotNil(t, err)
				assert.Equal(t, scenario.expectedErr, err)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

func Test_ValidatePostBureauResponse(t *testing.T) {
	scenarios := []struct {
		desc         string
		response     crDecisionEng.FICOCreditDecisionResponse
		productTypes []crDecisionEng.ProductType
		expectedErr  []servus.ErrorDetail
	}{
		{
			desc: "Success: preBureauResponse valid",
			response: crDecisionEng.FICOCreditDecisionResponse{
				Message: &crDecisionEng.Message{
					Status: utils.GetPointer("SUCCESS"),
					Application: &crDecisionEng.Application{
						Products: []crDecisionEng.Product{{
							ProductType:     utils.GetPointer(crDecisionEng.ProductType_FLEXI_CREDIT_CARD),
							ProductDecision: &crDecisionEng.ProductDecision{RecommendedCreditDecision: utils.GetPointer("APPROVE")},
						}},
					},
				},
			},
			productTypes: []crDecisionEng.ProductType{crDecisionEng.ProductType_FLEXI_CREDIT_CARD},
			expectedErr:  nil,
		},
		{
			desc:        "Failure: preBureauResponse not present",
			expectedErr: []servus.ErrorDetail{{ErrorCode: commonErr.ErrMissingCreditDecisionResponse.Code, Message: commonErr.ErrMissingCreditDecisionResponse.Message}},
		},
		{
			desc: "Failure: status not present",
			response: crDecisionEng.FICOCreditDecisionResponse{
				Message: &crDecisionEng.Message{
					Status: nil,
					Application: &crDecisionEng.Application{
						Products: []crDecisionEng.Product{{
							ProductType:     utils.GetPointer(crDecisionEng.ProductType_FLEXI_CREDIT_CARD),
							ProductDecision: &crDecisionEng.ProductDecision{RecommendedCreditDecision: utils.GetPointer("APPROVE")},
						}},
					},
				},
			},
			expectedErr: []servus.ErrorDetail{{ErrorCode: commonErr.ErrMissingStatus.Code, Message: commonErr.ErrMissingStatus.Message}},
		},
		{
			desc: "Failure: Status invalid",
			response: crDecisionEng.FICOCreditDecisionResponse{
				Message: &crDecisionEng.Message{
					Status: utils.GetPointer("NOT SUCCESS"),
					Application: &crDecisionEng.Application{
						Products: []crDecisionEng.Product{{
							ProductType:     utils.GetPointer(crDecisionEng.ProductType_FLEXI_CREDIT_CARD),
							ProductDecision: &crDecisionEng.ProductDecision{RecommendedCreditDecision: utils.GetPointer("APPROVE")},
						}},
					},
				},
			},
			expectedErr: []servus.ErrorDetail{{ErrorCode: commonErr.ErrInvalidCrDecisionEngResponseStatus.Code, Message: commonErr.ErrInvalidCrDecisionEngResponseStatus.Message}},
		},
		{
			desc: "Failure: Product missing",
			response: crDecisionEng.FICOCreditDecisionResponse{
				Message: &crDecisionEng.Message{
					Status: utils.GetPointer("SUCCESS"),
					Application: &crDecisionEng.Application{
						Products: nil,
					},
				},
			},
			expectedErr: []servus.ErrorDetail{{ErrorCode: commonErr.ErrMissingProducts.Code, Message: commonErr.ErrMissingProducts.Message}},
		},
		{
			desc: "Failure: product decision not found",
			response: crDecisionEng.FICOCreditDecisionResponse{
				Message: &crDecisionEng.Message{
					Status: utils.GetPointer("SUCCESS"),
					Application: &crDecisionEng.Application{
						Products: []crDecisionEng.Product{{
							ProductType:     utils.GetPointer(crDecisionEng.ProductType_FLEXI_CREDIT_CARD),
							ProductDecision: nil,
						}},
					},
				},
			},
			productTypes: []crDecisionEng.ProductType{crDecisionEng.ProductType_FLEXI_CREDIT_CARD},
			expectedErr:  []servus.ErrorDetail{{ErrorCode: commonErr.ErrMissingProductDecision.Code, Message: commonErr.ErrMissingProductDecision.Message}},
		},
		{
			desc: "Failure: RecommendedCreditDecision invalid",
			response: crDecisionEng.FICOCreditDecisionResponse{
				Message: &crDecisionEng.Message{
					Status: utils.GetPointer("SUCCESS"),
					Application: &crDecisionEng.Application{
						Products: []crDecisionEng.Product{{
							ProductType:     utils.GetPointer(crDecisionEng.ProductType_FLEXI_CREDIT_CARD),
							ProductDecision: &crDecisionEng.ProductDecision{RecommendedCreditDecision: utils.GetPointer("INVALID")},
						}},
					},
				},
			},
			productTypes: []crDecisionEng.ProductType{crDecisionEng.ProductType_FLEXI_CREDIT_CARD},
			expectedErr:  nil,
		},
	}
	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			err := ValidatePostBureauResponse(&scenario.response, scenario.productTypes)
			if scenario.expectedErr != nil {
				assert.NotNil(t, err)
				assert.Equal(t, scenario.expectedErr, err)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}
