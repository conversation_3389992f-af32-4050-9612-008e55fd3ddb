package validation

import (
	"context"
	"testing"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
)

// TestValidateApplicantsSG ...
func TestValidateApplicantsSG(t *testing.T) {
	type args struct {
		ctx        context.Context
		applicants []api.FlexiTermLoanApplicant
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "should return error when applicants are missing",
			args: args{
				ctx:        context.Background(),
				applicants: nil,
			},
			wantErr: true,
		},
		{
			name: "should return error when applicant type is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     "",
						IDType:            api.IDType_EMPL,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_C,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						WhitelistFlag:     "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when applicant type is invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     "INVALID",
						IDType:            api.IDType_EMPL,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_C,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						WhitelistFlag:     "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when ID type is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            "",
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_C,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						WhitelistFlag:     "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when ID type is invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            "Incorrect IDType",
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_C,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						WhitelistFlag:     "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when Gender is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            "",
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_C,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						WhitelistFlag:     "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when Gender is invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            "Invalid Gender",
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_C,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						WhitelistFlag:     "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when MaritalStatus is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     "",
						ResidentialStatus: api.ResidentialStatus_C,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						WhitelistFlag:     "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when MaritalStatus is invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     "Invalid MaritalStatus",
						ResidentialStatus: api.ResidentialStatus_C,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						WhitelistFlag:     "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when ResidentialStatus is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: "",
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						WhitelistFlag:     "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when ResidentialStatus is invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: "Invalid Residential Status",
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						WhitelistFlag:     "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when ContactType is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_C,
						ContactType:       "",
						AddressType:       "WORK",
						WhitelistFlag:     "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when ContactType is invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_C,
						ContactType:       "INVALID",
						AddressType:       "WORK",
						WhitelistFlag:     "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when AddressType is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_C,
						ContactType:       "RESIDENTIAL",
						AddressType:       "",
						WhitelistFlag:     "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when AddressType is invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_C,
						ContactType:       "RESIDENTIAL",
						AddressType:       "INVALID",
						WhitelistFlag:     "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when EmploymentType is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_P,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:         "",
							LengthOfEmployment:     0,
							TotalWorkingExperience: 0,
						},
						Income: &api.Income{
							IncomeDocuments: []api.IncomeDocument{{
								IncomeDocumentType: api.IncomeDocumentType_cpf,
							}},
						},
						WhitelistFlag: "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when EmploymentType is invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_P,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:         "Invalid ResidentialStatus",
							LengthOfEmployment:     0,
							TotalWorkingExperience: 0,
						},
						Income: &api.Income{
							IncomeDocuments: []api.IncomeDocument{{
								IncomeDocumentType: api.IncomeDocumentType_cpf,
							}},
						},
						WhitelistFlag: "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when LengthOfEmployment is Invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_P,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:         api.EmploymentType_SE,
							LengthOfEmployment:     -1,
							TotalWorkingExperience: 0,
						},
						WhitelistFlag: "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when TotalWorkingExperience is Invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_P,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:         api.EmploymentType_SE,
							LengthOfEmployment:     0,
							TotalWorkingExperience: -1,
						},
						WhitelistFlag: "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when IncomeDocumentType is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_P,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						Income: &api.Income{
							IncomeDocuments: []api.IncomeDocument{{
								IncomeDocumentType: "",
							}},
						},
						WhitelistFlag: "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when IncomeDocumentType is invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_P,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						Income: &api.Income{
							IncomeDocuments: []api.IncomeDocument{{
								IncomeDocumentType: "Invalid IncomeDocumentType",
							}},
						},
						WhitelistFlag: "Y",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should not return error when whitelist flag is empty",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:                api.ApplicantType_PRIMARY,
						IDType:                       api.IDType_NRIC,
						IDNumber:                     "SG12345",
						Gender:                       api.Gender_MALE,
						MaritalStatus:                api.MaritalStatus_SINGLE,
						ResidentialStatus:            api.ResidentialStatus_P,
						ContactType:                  "RESIDENTIAL",
						AddressType:                  "WORK",
						WhitelistFlag:                "Y",
						Race:                         "sd",
						CustomerRelationshipOpenDate: "12 jan 2022",
						StreetName:                   "abd",
						PostalCode:                   "asd",
						State:                        "ssda",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should return error when whitelist flag is INVALID",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_P,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						WhitelistFlag:     "INVALID",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when IncomeAmount is Invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_P,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						WhitelistFlag:     "Y",
						Income: &api.Income{IncomeDocuments: []api.IncomeDocument{{
							IncomeDocumentType: api.IncomeDocumentType_salary,
							IncomeComponents: []api.IncomeComponent{{
								IncomeOccurrences: []api.IncomeOccurrence{{IncomeAmount: -1}},
							}},
						}},
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should not return error when IncomeDocuments is Missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:                api.ApplicantType_PRIMARY,
						IDType:                       api.IDType_NRIC,
						IDNumber:                     "SG1234",
						Gender:                       api.Gender_MALE,
						MaritalStatus:                api.MaritalStatus_SINGLE,
						ResidentialStatus:            api.ResidentialStatus_P,
						ContactType:                  "RESIDENTIAL",
						AddressType:                  "WORK",
						WhitelistFlag:                "Y",
						Income:                       &api.Income{IncomeDocuments: nil},
						Race:                         "sd",
						CustomerRelationshipOpenDate: "12 jan 2022",
						StreetName:                   "abd",
						PostalCode:                   "asd",
						State:                        "ssda",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should not return error when IncomeOccurrences is Missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:                api.ApplicantType_PRIMARY,
						IDType:                       api.IDType_NRIC,
						IDNumber:                     "SG1234",
						Gender:                       api.Gender_MALE,
						MaritalStatus:                api.MaritalStatus_SINGLE,
						ResidentialStatus:            api.ResidentialStatus_P,
						ContactType:                  "RESIDENTIAL",
						AddressType:                  "WORK",
						WhitelistFlag:                "Y",
						Race:                         "sd",
						CustomerRelationshipOpenDate: "12 jan 2022",
						StreetName:                   "abd",
						PostalCode:                   "asd",
						State:                        "ssda",
						Income: &api.Income{IncomeDocuments: []api.IncomeDocument{{
							IncomeDocumentType: api.IncomeDocumentType_salary,
							IncomeComponents: []api.IncomeComponent{{
								IncomeOccurrences: nil,
							}},
						}},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should not return error when applicant data is proper",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						ApplicantType:     api.ApplicantType_PRIMARY,
						IDType:            api.IDType_NRIC,
						IDNumber:          "SG1234",
						Gender:            api.Gender_MALE,
						MaritalStatus:     api.MaritalStatus_SINGLE,
						ResidentialStatus: api.ResidentialStatus_P,
						ContactType:       "RESIDENTIAL",
						AddressType:       "WORK",
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:         api.EmploymentType_GIG,
							LengthOfEmployment:     0,
							TotalWorkingExperience: 0,
						},
						Income: &api.Income{
							IncomeDocuments: []api.IncomeDocument{{
								IncomeDocumentType: api.IncomeDocumentType_salary,
							}},
						},
						WhitelistFlag:                "Y",
						Race:                         "sd",
						CustomerRelationshipOpenDate: "12 jan 2022",
						StreetName:                   "abd",
						PostalCode:                   "asd",
						State:                        "ssda",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		data := tt
		t.Run(data.name, func(t *testing.T) {
			if err := ValidateSGApplicants(data.args.applicants); (err != nil) != data.wantErr {
				t.Errorf("ValidateApplicants() = %v, want %v", err, data.wantErr)
			}
		})
	}
}
