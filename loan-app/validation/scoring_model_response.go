package validation

import (
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	mlScoringAPI "gitlab.com/gx-regional/dakota/lending/loan-app/external/mlscoringservice/api"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// ValidateScoringModelResponse ...
func ValidateScoringModelResponse(scoringModelResponse api.ScoringModelResponse) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	errs = append(errs, ValidateResponseStatus(scoringModelResponse.Status)...)
	return errs
}

// ValidateApplicationScoringModelResponse ...
func ValidateApplicationScoringModelResponse(scoringModelResponse *mlScoringAPI.GetApplicationScoreResponse) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if scoringModelResponse.Application == nil {
		errs = append(errs, servus.ErrorDetail{ErrorCode: commonErr.ErrMissingAscoreResponse.Code, Message: commonErr.ErrMissingAscoreResponse.Message})
	}
	return errs
}
