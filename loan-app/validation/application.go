// Package validation ...
package validation

import (
	"context"
	"strings"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"
	"gitlab.com/gx-regional/dakota/lending/loan-app/mapper"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

// ValidateApplicationType ...
func ValidateApplicationType(applicationType api.ApplicationType) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(applicationType)) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingApplicationType.Code, Message: apiErr.ErrMissingApplicationType.Message})
	}
	if !(api.ApplicationType_NEW == applicationType || applicationType == api.ApplicationType_REVIEW) {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidApplicationType.Code, Message: apiErr.ErrInvalidApplicationType.Message})
	}
	return errs
}

// ValidateChannel ...
func ValidateChannel(channel api.Channel) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(channel)) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingChannel.Code, Message: apiErr.ErrMissingChannel.Message})
	}
	if _, ok := mapper.ChannelTypes[channel]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidChannel.Code, Message: apiErr.ErrInvalidChannel.Message})
	}
	return errs
}

// ValidateProducts ...
func ValidateProducts(products []api.Product) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if len(products) == 0 {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingProducts.Code, Message: apiErr.ErrMissingProducts.Message})
	}
	for _, product := range products {
		errs = append(errs, ValidateProductCode(product.ProductType)...)
		errs = append(errs, ValidateProductSubCode(product.SubProductType)...)
	}
	return errs
}

// ValidateProductCode ...
func ValidateProductCode(productType api.ProductType) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(productType)) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingProductType.Code, Message: apiErr.ErrMissingProductType.Message})
	}
	if _, ok := mapper.ProductTypes[productType]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidProductType.Code, Message: apiErr.ErrInvalidProductType.Message})
	}
	return errs
}

// ValidateProductSubCode ...
func ValidateProductSubCode(subProductType api.SubProductType) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if _, ok := mapper.SubProductTypes[subProductType]; !ok {
		if strings.TrimSpace(string(subProductType)) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingProductSubType.Code, Message: apiErr.ErrMissingProductSubType.Message})
		}
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidProductSubType.Code, Message: apiErr.ErrInvalidProductSubType.Message})
	}
	return errs
}

// ValidateStatusAndStatusReason ...
func ValidateStatusAndStatusReason(ctx context.Context, message *api.Message) ([]servus.ErrorDetail, error) {
	status := message.Status
	statusReason := message.StatusReason
	var errs []servus.ErrorDetail
	// status validations
	errs = append(errs, ValidateStatus(status)...)
	if len(errs) != 0 {
		return errs, nil
	}
	// statusReason validations
	errs = append(errs, ValidateStatusReason(statusReason, message.Application.CountryCode)...)
	if len(errs) != 0 {
		return errs, nil
	}
	application, err := common.FetchApplicationDetails(ctx, []data.Condition{data.EqualTo("ApplicationID", message.Application.ApplicationID)}, constants.UpdateApplicationLogTag)
	if err != nil {
		return nil, err
	}
	if application[0].StatusReason == string(api.ApplicationStatusReason_LIMIT_CREATION_SUCCESSFUL) && message.StatusReason != api.ApplicationStatusReason_LIMIT_CREATION_SUCCESSFUL {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrApplicationInApprovedStatus.Code, Message: apiErr.ErrApplicationInApprovedStatus.Message})
	}
	return errs, nil
}

// ValidateCreatedBy ...
func ValidateCreatedBy(createdBy string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(createdBy) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingCreatedBy.Code, Message: apiErr.ErrMissingCreatedBy.Message})
	}
	return errs
}

// ValidateUpdatedBy ...
func ValidateUpdatedBy(UpdatedBy string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(UpdatedBy) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingUpdatedBy.Code, Message: apiErr.ErrMissingUpdatedBy.Message})
	}
	return errs
}

// ValidateStatus ...
// nolint:dupl
func ValidateStatus(status api.ApplicationStatus_ApplicationStatus) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(status)) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingStatus.Code, Message: apiErr.ErrMissingStatus.Message})
	}
	if _, ok := mapper.Statuses[status]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidStatus.Code, Message: apiErr.ErrInvalidStatus.Message})
	}
	return errs
}

// ValidateStatusReason ...
// nolint:dupl
func ValidateStatusReason(statusReason api.ApplicationStatusReason, countryCode string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(statusReason)) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingStatusReason.Code, Message: apiErr.ErrMissingStatusReason.Message})
	}
	if _, ok := mapper.ApplicationStatusReason[countryCode][statusReason]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidStatusReason.Code, Message: apiErr.ErrInvalidStatusReason.Message})
	}
	return errs
}

// ValidateApplicationID ...
// nolint:dupl
func ValidateApplicationID(ApplicationID string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(ApplicationID) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingApplicationID.Code, Message: apiErr.ErrMissingApplicationID.Message})
	}
	return errs
}

// ValidateCountryCode : to validate country code and accept only if in acceptable country code list
func ValidateCountryCode(countryCode string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(countryCode) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingCountryCode.Code, Message: apiErr.ErrMissingCountryCode.Message})
	}
	if _, ok := mapper.CountryCodes[countryCode]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidCountryCode.Code, Message: apiErr.ErrInvalidCountryCode.Message})
	}

	return errs
}

// ValidateApplicationTypeNew ...
func ValidateApplicationTypeNew(applicationType api.ApplicationType) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(applicationType)) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingApplicationType.Code, Message: apiErr.ErrMissingApplicationType.Message})
	}
	if api.ApplicationType_NEW != applicationType {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidApplicationType.Code, Message: apiErr.ErrInvalidApplicationType.Message})
	}
	return errs
}

// ValidateFlexiCardProducts ...
func ValidateFlexiCardProducts(products []api.Product) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if len(products) == 0 {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingProducts.Code, Message: apiErr.ErrMissingProducts.Message})
	}
	for _, product := range products {
		if strings.TrimSpace(string(product.ProductType)) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingProductType.Code, Message: apiErr.ErrMissingProductType.Message})
		} else if product.ProductType != api.ProductType_FLEXI_CREDIT_CARD {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidProductType.Code, Message: apiErr.ErrInvalidProductType.Message})
		}

		if strings.TrimSpace(string(product.SubProductType)) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingProductSubType.Code, Message: apiErr.ErrMissingProductSubType.Message})
		} else if product.SubProductType != api.SubProductType_DEFAULT_FLEXI_CREDIT_CARD {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidProductSubType.Code, Message: apiErr.ErrInvalidProductSubType.Message})
		}
	}
	return errs
}

// ValidateBundledProducts ...
func ValidateBundledProducts(products []api.Product) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if len(products) != 2 {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidProduct.Code, Message: apiErr.ErrInvalidProduct.Message})
	}
	var isCardProductPresent, isLoanProductPresent bool

	for _, product := range products {
		if strings.TrimSpace(string(product.ProductType)) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingProductType.Code, Message: apiErr.ErrMissingProductType.Message})
		}
		if strings.TrimSpace(string(product.SubProductType)) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingProductSubType.Code, Message: apiErr.ErrMissingProductSubType.Message})
		}
		if product.ProductType == api.ProductType_FLEXI_CREDIT_CARD && product.SubProductType == api.SubProductType_DEFAULT_FLEXI_CREDIT_CARD {
			isCardProductPresent = true
		}
		if product.ProductType == api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT && product.SubProductType == api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT {
			isLoanProductPresent = true
		}
	}
	if !isCardProductPresent || !isLoanProductPresent {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidProduct.Code, Message: apiErr.ErrInvalidProduct.Message})
	}

	return errs
}

// ValidatePreSelect ...
// nolint:dupl
func ValidatePreSelect(preSelect api.PreSelectType) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(preSelect)) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingPreSelect.Code, Message: apiErr.ErrMissingPreSelect.Message})
	}

	if _, ok := mapper.PreSelectType[preSelect]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidPreSelect.Code, Message: apiErr.ErrInvalidPreSelect.Message})
	}
	return errs
}
