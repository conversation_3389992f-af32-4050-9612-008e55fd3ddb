package validation

import (
	"strings"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// ValidateExtraDeviceInfo ...
func ValidateExtraDeviceInfo(deviceInfo *api.DeviceInfo) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if strings.TrimSpace(deviceInfo.AdjustId) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingAdjustID.Code, Message: apiErr.ErrMissingAdjustID.Message})
	}

	if strings.TrimSpace(deviceInfo.Imei) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingIMEI.Code, Message: apiErr.ErrMissingIMEI.Message})
	}

	if strings.TrimSpace(deviceInfo.AppPackageName) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingAppPackageName.Code, Message: apiErr.ErrMissingAppPackageName.Message})
	}

	return errs
}

// ValidatePartnerPayloadInfo ...
// nolint:funlen
func ValidatePartnerPayloadInfo(partnerPayload *api.PartnerPayload) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if partnerPayload == nil {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingPartnerPayload.Code, Message: apiErr.ErrMissingPartnerPayload.Message})
	} else {
		if strings.TrimSpace(partnerPayload.ProdId) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingProdId.Code, Message: apiErr.ErrMissingProdId.Message})
		}

		if strings.TrimSpace(partnerPayload.BizChannelId) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingBizChannelId.Code, Message: apiErr.ErrMissingBizChannelId.Message})
		}

		if partnerPayload.OrderId == 0 {
			return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidOrderId.Code, Message: apiErr.ErrInvalidOrderId.Message})
		}

		if partnerPayload.FirstLoanIdx == 0 {
			return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidFirstLoanIdx.Code, Message: apiErr.ErrInvalidFirstLoanIdx.Message})
		}

		if partnerPayload.RequestedLoanAmount == 0 {
			return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidRequestedLoanAmount.Code, Message: apiErr.ErrInvalidRequestedLoanAmount.Message})
		}

		if partnerPayload.Commission == 0 {
			return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidCommission.Code, Message: apiErr.ErrInvalidCommission.Message})
		}

		if strings.TrimSpace(partnerPayload.RequestedTenure) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingRequestedTenure.Code, Message: apiErr.ErrMissingRequestedTenure.Message})
		}

		if partnerPayload.InterestRate == 0 {
			return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidInterestRate.Code, Message: apiErr.ErrInvalidInterestRate.Message})
		}

		if strings.TrimSpace(partnerPayload.DisbursementDate) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingDisbursementDate.Code, Message: apiErr.ErrMissingDisbursementDate.Message})
		}

		if strings.TrimSpace(partnerPayload.FirstDueDate) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingFirstDueDate.Code, Message: apiErr.ErrMissingFirstDueDate.Message})
		}

		if strings.TrimSpace(partnerPayload.RequestedCurrencyCode) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingRequestedCurrencyCode.Code, Message: apiErr.ErrMissingRequestedCurrencyCode.Message})
		}

		if strings.TrimSpace(partnerPayload.DrawDownAccount) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingDrawDownAccount.Code, Message: apiErr.ErrMissingDrawDownAccount.Message})
		}

		if strings.TrimSpace(partnerPayload.DrawDownCcyCode) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingDrawDownCcyCode.Code, Message: apiErr.ErrMissingDrawDownCcyCode.Message})
		}

		if strings.TrimSpace(partnerPayload.BankCode) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingBankCode.Code, Message: apiErr.ErrMissingBankCode.Message})
		}

		// accountInfo validations
		errs = append(errs, ValidateAccountInfo(partnerPayload.AccountInfo)...)

		// creditInfo validations
		errs = append(errs, ValidateCreditInfo(partnerPayload.CreditInfo)...)

		// accountInfo validations
		errs = append(errs, ValidateContractList(partnerPayload.ContractList)...)
	}

	return errs
}

// ValidateAccountInfo ...
func ValidateAccountInfo(accountInfo *api.AccountInfo) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if accountInfo.UserId == 0 {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidUserId.Code, Message: apiErr.ErrInvalidUserId.Message})
	}

	if accountInfo.NumberOfChild == 0 {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidNumberOfChild.Code, Message: apiErr.ErrInvalidNumberOfChild.Message})
	}

	if strings.TrimSpace(accountInfo.CustRiskLevel) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingCustRiskLevel.Code, Message: apiErr.ErrMissingCustRiskLevel.Message})
	}

	if strings.TrimSpace(accountInfo.WorkingMonths) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingWorkingMonths.Code, Message: apiErr.ErrMissingWorkingMonths.Message})
	}

	if strings.TrimSpace(accountInfo.StayMonths) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingStayMonths.Code, Message: apiErr.ErrMissingStayMonths.Message})
	}

	if strings.TrimSpace(accountInfo.RegionCode) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingRegionCode.Code, Message: apiErr.ErrMissingRegionCode.Message})
	}

	return errs
}

// ValidateCreditInfo ...
func ValidateCreditInfo(creditInfo *api.CreditInfo) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if creditInfo.PReviewResults == 0 {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidPReviewResults.Code, Message: apiErr.ErrInvalidPReviewResults.Message})
	}

	if strings.TrimSpace(creditInfo.RejectNodeId) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingRejectNodeId.Code, Message: apiErr.ErrMissingRejectNodeId.Message})
	}

	if creditInfo.LtLevel2 == 0 {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidLtLevel2.Code, Message: apiErr.ErrInvalidLtLevel2.Message})
	}

	if creditInfo.StLevel2 == 0 {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidStLevel2.Code, Message: apiErr.ErrInvalidStLevel2.Message})
	}

	if creditInfo.LongTermUserType == 0 {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidLongTermUserType.Code, Message: apiErr.ErrInvalidLongTermUserType.Message})
	}

	if creditInfo.ShortTermUserType == 0 {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidShortTermUserType.Code, Message: apiErr.ErrInvalidShortTermUserType.Message})
	}

	if creditInfo.PProductCategory == 0 {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidPProductCategory.Code, Message: apiErr.ErrInvalidPProductCategory.Message})
	}

	return errs
}

// ValidateContractList ...
func ValidateContractList(contractLists []api.ContractList) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if len(contractLists) == 0 {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingContractList.Code, Message: apiErr.ErrMissingContractList.Message})
	}

	for _, contractList := range contractLists {
		if strings.TrimSpace(contractList.ContractName) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingContractName.Code, Message: apiErr.ErrMissingContractName.Message})
		}

		if strings.TrimSpace(contractList.ContractURL) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingContractUrl.Code, Message: apiErr.ErrMissingContractUrl.Message})
		}
	}

	return errs
}
