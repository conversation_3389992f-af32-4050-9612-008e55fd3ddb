package validation

import (
	"testing"

	"github.com/stretchr/testify/assert"
	mlScoringAPI "gitlab.com/gx-regional/dakota/lending/loan-app/external/mlscoringservice/api"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
)

func TestScoringModelResponse(t *testing.T) {
	type args struct {
		scoringModelResponse api.ScoringModelResponse
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "should not return error when ScoringModelResponse validation is success",
			args:    args{scoringModelResponse: api.ScoringModelResponse{Status: "SUCCESS"}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			if err := ValidateScoringModelResponse(test.args.scoringModelResponse); (err != nil) != test.wantErr {
				t.<PERSON>("ValidateScoringModelResponse() = %v, want %v", err, test.wantErr)
			}
		})
	}
}

func Test_ValidateApplicationScoringModelResponse(t *testing.T) {
	scenarios := []struct {
		desc        string
		response    mlScoringAPI.GetApplicationScoreResponse
		expectedErr []servus.ErrorDetail
	}{
		{
			desc:        "Success: a-score application present",
			response:    mlScoringAPI.GetApplicationScoreResponse{Application: &mlScoringAPI.Application{}},
			expectedErr: nil,
		},
		{
			desc:        "Failure: a-score application not present",
			response:    mlScoringAPI.GetApplicationScoreResponse{Application: nil},
			expectedErr: []servus.ErrorDetail{{ErrorCode: commonErr.ErrMissingAscoreResponse.Code, Message: commonErr.ErrMissingAscoreResponse.Message}},
		},
	}
	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			err := ValidateApplicationScoringModelResponse(&scenario.response)
			if scenario.expectedErr != nil {
				assert.NotNil(t, err)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}
