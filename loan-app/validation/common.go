package validation

import (
	"strings"

	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// ValidateReferenceID ...
func ValidateReferenceID(referenceID string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if err := validateEmptyString(referenceID, "referenceID", commonErr.ErrMissingReferenceID); err != nil {
		return append(errs, *err)
	}
	return errs
}

// ValidateOnboardingApplicationID ...
func ValidateOnboardingApplicationID(onboardingApplicationID string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if err := validateEmptyString(onboardingApplicationID, "onboardingApplicationID", commonErr.ErrMissingOnboardingApplicationID); err != nil {
		return append(errs, *err)
	}
	return errs
}

func validateEmptyString(field string, fieldName string, err commonErr.CustomError) *servus.ErrorDetail {
	if strings.TrimSpace(field) == "" {
		return &servus.ErrorDetail{ErrorCode: err.Code, Message: err.Message, Path: fieldName}
	}
	return nil
}
