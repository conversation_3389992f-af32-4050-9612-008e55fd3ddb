package request

import (
	"context"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/validation"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// validateCommonUpdateReq...
func validateCommonUpdateReq(ctx context.Context, req *api.UpdateFlexiTermLoanApplicationRequest, errs []servus.ErrorDetail, application *api.FlexiTermLoanApplication) ([]servus.ErrorDetail, error) {
	// Application validations
	errs = append(errs, validation.ValidateApplicationType(application.ApplicationType)...)
	errs = append(errs, validation.ValidateChannel(application.Channel)...)
	errs = append(errs, validation.ValidateProducts(application.Products)...)

	// Status/Reason validations
	appendErr, dbErr := validation.ValidateStatusAndStatusReason(ctx, req.Message)
	if dbErr != nil {
		return nil, dbErr
	}

	errs = append(errs, appendErr...)

	// UpdatedBy validations
	errs = append(errs, validation.ValidateUpdatedBy(req.Message.UpdatedBy)...)

	// Integration responses validations
	if application.EcosystemResponse != nil {
		errs = append(errs, validation.ValidateEcosystemResponse(application.EcosystemResponse)...)
	}
	if req.Message.StatusReason == api.ApplicationStatusReason_CDE_POST_BUREAU_APPROVED {
		errs = append(errs, validation.ValidateCreditDecisionResponse(application.CreditDecisionResponse)...)
	}
	if req.Message.StatusReason == api.ApplicationStatusReason_LIMIT_CREATION_SUCCESSFUL {
		errs = append(errs, validation.ValidateLimitCreationResponse(application.LimitCreationResponse)...)
	}
	if application.ScoringModelResponse != nil {
		errs = append(errs, validation.ValidateScoringModelResponse(*application.ScoringModelResponse)...)
	}
	return errs, nil
}

// ValidateUpdateStatusReqCommon ...
func ValidateUpdateStatusReqCommon(req *api.UpdateFlexiTermLoanApplicationStatusRequest) ([]servus.ErrorDetail, error) {
	var errs []servus.ErrorDetail
	// UpdatedBy validations
	errs = append(errs, validation.ValidateUpdatedBy(req.UpdatedBy)...)
	// status validations
	errs = append(errs, validation.ValidateStatus(req.Status)...)
	// statusReason validations
	errs = append(errs, validation.ValidateStatusReason(req.StatusReason, req.CountryCode)...)
	// ApplicationID validations
	errs = append(errs, validation.ValidateApplicationID(req.ApplicationID)...)

	return errs, nil
}
