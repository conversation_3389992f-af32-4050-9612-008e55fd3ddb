package request

import (
	"context"

	"golang.org/x/exp/slices"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/validations"
	"gitlab.com/gx-regional/dakota/lending/loan-app/validation"
	"gitlab.myteksi.net/dakota/lending/common/constant"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// MYRequestValidator ...
type MYRequestValidator struct {
	BaseImpl
}

// ValidateCreateReq ...
func (v *MYRequestValidator) ValidateCreateReq(req *api.CreateFlexiTermLoanApplicationRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	// Application validation
	errs = append(errs, validation.ValidateApplicationType(req.Message.Application.ApplicationType)...)
	errs = append(errs, validation.ValidateChannel(req.Message.Application.Channel)...)
	errs = append(errs, validation.ValidateProducts(req.Message.Application.Products)...)
	errs = append(errs, validation.ValidatePreSelect(req.Message.Application.PreSelect)...)
	errs = append(errs, validation.ValidateCountryCode(req.Message.Application.CountryCode)...)

	// OnboardingApplicationID validation
	errs = append(errs, validations.ValidateOnboardingApplicationID(req.Message.OnboardingApplicationID)...)

	// Applicant validation
	errs = append(errs, validation.ValidateDBMYApplicants(req.Message.Application.Applicants)...)

	// CreatedBy validations
	errs = append(errs, validation.ValidateCreatedBy(req.Message.CreatedBy)...)

	return errs
}

// ValidateUpdateReq ...
func (v *MYRequestValidator) ValidateUpdateReq(ctx context.Context, req *api.UpdateFlexiTermLoanApplicationRequest) ([]servus.ErrorDetail, error) {
	var errs []servus.ErrorDetail

	application := req.Message.Application

	// Validate dbmy applicant
	errs = append(errs, validation.ValidateDBMYApplicants(application.Applicants)...)

	// validate common req
	errs, err := validateCommonUpdateReq(ctx, req, errs, application)
	if err != nil {
		return nil, err
	}

	// if application rejected
	if req.Message.Status == api.ApplicationStatus_ApplicationStatus_REJECTED {
		errs = append(errs, validateRejectionCodeRequestDBMY(req)...)
	}

	// if CDE verdict approve
	if req.Message.StatusReason == api.ApplicationStatusReason_POST_INCOME_DERIVATION_CDE_APPROVED {
		errs = append(errs, validation.ValidateCreditBureauResponseV2(application.CreditBureauResponseV2)...)
	}

	return errs, nil
}

// ValidateUpdateStatusReq ...
func (v *MYRequestValidator) ValidateUpdateStatusReq(req *api.UpdateFlexiTermLoanApplicationStatusRequest) ([]servus.ErrorDetail, error) {
	var errs []servus.ErrorDetail
	// UpdatedBy validations
	errs = append(errs, validation.ValidateUpdatedBy(req.UpdatedBy)...)
	// status validations
	errs = append(errs, validation.ValidateStatus(req.Status)...)
	// statusReason validations
	errs = append(errs, validation.ValidateStatusReason(req.StatusReason, req.CountryCode)...)
	// ApplicationID validations
	errs = append(errs, validation.ValidateApplicationID(req.ApplicationID)...)
	return errs, nil
}

func validateRejectionCodeRequestDBMY(req *api.UpdateFlexiTermLoanApplicationRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	switch req.Message.StatusReason {
	case api.ApplicationStatusReason_FRAUD_PRE_BUREAU_REJECTED,
		api.ApplicationStatusReason_AML_PRE_BUREAU_REJECTED:
		errs = append(errs, ValidateFraudCheckRejectReasons(req)...)
	case api.ApplicationStatusReason_PRE_INCOME_DERIVATION_CDE_REJECTED,
		api.ApplicationStatusReason_POST_INCOME_DERIVATION_CDE_REJECTED:
		errs = append(errs, validateCRDecisionRejectReasons(req)...)
	default:
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrRejectedByInvalid.Code, Message: apiErr.ErrRejectedByInvalid.Message})
	}

	return errs
}

func validateCRDecisionRejectReasons(req *api.UpdateFlexiTermLoanApplicationRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	decisionResponses := req.Message.Application.CreditDecisionResponse
	for _, decisionResponse := range decisionResponses {
		errs = append(errs, ValidateCallTypeCheck(decisionResponse.CallType)...)
		if (req.Message.StatusReason == api.ApplicationStatusReason_PRE_INCOME_DERIVATION_CDE_REJECTED && decisionResponse.CallType == api.CallType_POST_BUREAU) ||
			(req.Message.StatusReason == api.ApplicationStatusReason_POST_INCOME_DERIVATION_CDE_REJECTED && decisionResponse.CallType == api.CallType_POST_INCOME_DERIVATION) {
			errs = ValidateCDEFailureReq(decisionResponse, errs)
		}
	}
	return errs
}

func (v *MYRequestValidator) ValidateGetDocumentTypesRequest(req *api.GetDocumentTypesRequest) []servus.ErrorDetail {
	allowedProductVariantCodes := []string{constant.FlexiLineOfCreditProductVariantCode}
	var errs []servus.ErrorDetail
	if !slices.Contains(allowedProductVariantCodes, req.ProductVariantCode) {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidProductVariantCode.Code, Message: apiErr.ErrInvalidProductVariantCode.Message})
	}
	return errs
}
