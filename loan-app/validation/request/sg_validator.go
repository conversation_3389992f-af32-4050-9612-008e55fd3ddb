package request

import (
	"context"
	"strings"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/validation"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// SGRequestValidator ...
type SGRequestValidator struct {
	BaseImpl
}

// ValidateCreateReq ...
func (v *SGRequestValidator) ValidateCreateReq(req *api.CreateFlexiTermLoanApplicationRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	// Application validations
	errs = append(errs, validation.ValidateApplicationType(req.Message.Application.ApplicationType)...)
	errs = append(errs, validation.ValidateChannel(req.Message.Application.Channel)...)
	errs = append(errs, validation.ValidateProducts(req.Message.Application.Products)...)

	// Applicant validations
	errs = append(errs, validation.ValidateSGApplicants(req.Message.Application.Applicants)...)

	// CreatedBy validations
	errs = append(errs, validation.ValidateCreatedBy(req.Message.CreatedBy)...)

	// DeviceID validations
	if strings.TrimSpace(req.Message.DeviceID) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingDeviceId.Code, Message: apiErr.ErrMissingDeviceId.Message})
	}

	return errs
}

// ValidateUpdateReq ...
func (v *SGRequestValidator) ValidateUpdateReq(ctx context.Context, req *api.UpdateFlexiTermLoanApplicationRequest) ([]servus.ErrorDetail, error) {
	var errs []servus.ErrorDetail

	application := req.Message.Application

	errs, dbErr := validateCommonUpdateReq(ctx, req, errs, application)

	if req.Message.StatusReason == api.ApplicationStatusReason_CDE_POST_BUREAU_APPROVED {
		errs = append(errs, validation.ValidateCreditBureauResponse(application.CreditBureauResponse)...)
	}

	// Applicant SG validations
	errs = append(errs, validation.ValidateSGApplicants(req.Message.Application.Applicants)...)

	if dbErr != nil {
		return nil, dbErr
	}

	return errs, nil
}

// ValidateCreateFlexiCardApplicationReq : to validate create flexi-card application request
//
// nolint:dupl
func (v *SGRequestValidator) ValidateCreateFlexiCardApplicationReq(req *api.CreateFlexiCardApplicationRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	//Application validations
	errs = append(errs, validation.ValidateApplicationTypeNew(req.Message.Application.ApplicationType)...)
	errs = append(errs, validation.ValidateCountryCode(req.Message.Application.CountryCode)...)
	errs = append(errs, validation.ValidateChannel(req.Message.Application.Channel)...)
	errs = append(errs, validation.ValidateFlexiCardProducts(req.Message.Application.Products)...)

	//Applicant validations
	errs = append(errs, validation.ValidateSGApplicants(req.Message.Application.Applicants)...)

	return errs
}

// ValidateCreateBundledApplicationReq : to validate create bundle application request
//
// nolint:dupl
func (v *SGRequestValidator) ValidateCreateBundledApplicationReq(req *api.CreateBundledApplicationRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	errs = append(errs, validation.ValidateApplicationTypeNew(req.Message.Application.ApplicationType)...)
	errs = append(errs, validation.ValidateCountryCode(req.Message.Application.CountryCode)...)
	errs = append(errs, validation.ValidateChannel(req.Message.Application.Channel)...)
	errs = append(errs, validation.ValidateSGApplicants(req.Message.Application.Applicants)...)
	errs = append(errs, validation.ValidateBundledProducts(req.Message.Application.Products)...)
	return errs
}

func (v *SGRequestValidator) ValidateGetDocumentTypesRequest(req *api.GetDocumentTypesRequest) []servus.ErrorDetail {
	//TODO implement me
	return nil
}
