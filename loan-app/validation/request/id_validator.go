package request

import (
	"context"
	"regexp"
	"strings"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/validations"
	"gitlab.com/gx-regional/dakota/lending/loan-app/mapper"
	reqDataMapper "gitlab.com/gx-regional/dakota/lending/loan-app/utils/mapper"
	"gitlab.com/gx-regional/dakota/lending/loan-app/validation"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// IDRequestValidator ...
type IDRequestValidator struct {
	BaseImpl
}

// ValidateCreateReq ...
func (v *IDRequestValidator) ValidateCreateReq(req *api.CreateFlexiTermLoanApplicationRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	// Application validations
	errs = append(errs, validation.ValidateApplicationType(req.Message.Application.ApplicationType)...)
	errs = append(errs, validation.ValidateChannel(req.Message.Application.Channel)...)
	errs = append(errs, validation.ValidateProducts(req.Message.Application.Products)...)

	// Applicant validations
	errs = append(errs, validation.ValidateIDApplicants(req.Message.Application.Applicants)...)

	// CreatedBy validations
	errs = append(errs, validation.ValidateCreatedBy(req.Message.CreatedBy)...)

	// DeviceInfo validations
	errs = append(errs, ValidateDeviceInfo(req.Message.DeviceInfo)...)

	if req.Message.Application.Channel == api.Channel_AKULAKU {
		errs = append(errs, validation.ValidateExtraDeviceInfo(req.Message.DeviceInfo)...)
		errs = append(errs, validation.ValidatePartnerPayloadInfo(req.Message.Application.PartnerPayload)...)
	}

	return errs
}

// ValidateUpdateStatusReq ...
func (v *IDRequestValidator) ValidateUpdateStatusReq(req *api.UpdateFlexiTermLoanApplicationStatusRequest) ([]servus.ErrorDetail, error) {
	return ValidateUpdateStatusReqCommon(req)
}

// ValidateUpdateReq ...
func (v *IDRequestValidator) ValidateUpdateReq(ctx context.Context, req *api.UpdateFlexiTermLoanApplicationRequest) ([]servus.ErrorDetail, error) {
	var errs []servus.ErrorDetail

	application := req.Message.Application

	// Applicant Id validations
	errs = append(errs, validation.ValidateIDApplicants(req.Message.Application.Applicants)...)

	errs, dbErr := validateCommonUpdateReq(ctx, req, errs, application)
	if dbErr != nil {
		return nil, dbErr
	}

	if req.Message.StatusReason == api.ApplicationStatusReason_CDE_POST_BUREAU_APPROVED {
		errs = append(errs, validation.ValidateCreditBureauResponseV2(application.CreditBureauResponseV2)...)
	}

	if req.Message.Status == api.ApplicationStatus_ApplicationStatus_REJECTED {
		errs = append(errs, ValidateRejectionCodeRequest(req)...)
	}

	// DeviceInfo validations
	errs = append(errs, ValidateDeviceInfo(req.Message.DeviceInfo)...)

	return errs, nil
}

// ValidateCreateOrUpdateWhitelistApplicantRequest validates a CreateOrUpdateWhitelistApplicantRequest.
func (v *IDRequestValidator) ValidateCreateOrUpdateWhitelistApplicantRequest(req *api.CreateOrUpdateWhitelistApplicantRequest) ([]servus.ErrorDetail, error) {
	var errs []servus.ErrorDetail

	if len(req.WhitelistedUserDetails) == 0 {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: apiErr.ErrWhitelistedUserDetails.Code,
			Message:   apiErr.ErrWhitelistedUserDetails.Message,
			Path:      "whitelistedUserDetails",
		})
	} else if len(req.WhitelistedUserDetails) > 500 {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: apiErr.ErrMaximumWhitelistedUserDetails.Code,
			Message:   apiErr.ErrMaximumWhitelistedUserDetails.Message + ": maximum 500 data",
			Path:      "whitelistedUserDetails",
		})
	}
	return errs, nil
}

// ValidateWhitelistedUserDetails validates the details of a whitelisted user.
func (v *IDRequestValidator) ValidateWhitelistedUserDetails(whitelistedUserDetail *api.WhitelistedUserDetails) []servus.ErrorDetail {
	errs := []servus.ErrorDetail{}
	if whitelistedUserDetail.Value == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: apiErr.ErrMissingWhitelistValue.Code,
			Message:   apiErr.ErrMissingWhitelistValue.Message,
			Path:      "whitelistedUserDetails.value",
		})
	} else if whitelistedUserDetail.Key == api.WhitelistKey_ContactNumber {
		if whitelistedUserDetail.Value[0] == '+' {
			if !regexp.MustCompile(`^[0-9]+$`).MatchString(whitelistedUserDetail.Value[1:]) {
				errs = append(errs, servus.ErrorDetail{
					ErrorCode: apiErr.ErrInvalidContactNumber.Code,
					Message:   apiErr.ErrInvalidContactNumber.Message,
					Path:      "whitelistedUserDetails.value",
				})
			}
		} else if !regexp.MustCompile(`^[0-9]+$`).MatchString(whitelistedUserDetail.Value) {
			errs = append(errs, servus.ErrorDetail{
				ErrorCode: apiErr.ErrInvalidContactNumber.Code,
				Message:   apiErr.ErrInvalidContactNumber.Message,
				Path:      "whitelistedUserDetails.value",
			})
		}
	} else if whitelistedUserDetail.Key == api.WhitelistKey_IDNumber {
		if !regexp.MustCompile(`^[0-9]+$`).MatchString(whitelistedUserDetail.Value) {
			errs = append(errs, servus.ErrorDetail{
				ErrorCode: apiErr.ErrInvalidIDNumber.Code,
				Message:   apiErr.ErrInvalidIDNumber.Message,
				Path:      "whitelistedUserDetails.message",
			})
		}
	}
	if whitelistedUserDetail.Key == "" || !isValidWhitelistKey(whitelistedUserDetail.Key) {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: apiErr.ErrInvalidWhitelistKey.Code,
			Message:   apiErr.ErrInvalidWhitelistKey.Message,
			Path:      "whitelistedUserDetails.key",
		})
	}

	return errs
}

// ValidateGetWhitelistHistoryApplicantRequest validates the input request for getting whitelist history of an applicant.
// nolint:dupl
func (v *IDRequestValidator) ValidateGetWhitelistHistoryApplicantRequest(req *api.GetWhitelistHistoryApplicantRequest) ([]servus.ErrorDetail, error) {
	var errs []servus.ErrorDetail
	errs = append(errs, validations.ValidateApplicantWhitelistID(req.ApplicantWhitelistID)...)
	return errs, nil
}

// ValidateGetWhitelistApplicant validates a GetWhitelistApplicantRequest.
func (v *IDRequestValidator) ValidateGetWhitelistApplicant(req *api.GetWhitelistApplicantListRequest) ([]servus.ErrorDetail, error) {
	var errs []servus.ErrorDetail

	// Check if the page size is less than 1.
	if req.PageSize < 1 {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: apiErr.ErrInvalidPage.Code,
			Message:   apiErr.ErrInvalidPage.Message,
			Path:      "page",
		})
	}
	if req.Key != "" && !isValidWhitelistKey(req.Key) {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: apiErr.ErrInvalidWhitelistKey.Code,
			Message:   apiErr.ErrInvalidWhitelistKey.Message,
			Path:      "key",
		})
	}
	if !req.StartUpdatedAt.IsZero() && !req.EndUpdatedAt.IsZero() {
		if req.EndUpdatedAt.Before(req.StartUpdatedAt) {
			errs = append(errs, servus.ErrorDetail{
				ErrorCode: apiErr.ErrInvalidUpdatedAtRange.Code,
				Message:   apiErr.ErrInvalidUpdatedAtRange.Message,
				Path:      "updatedAt",
			})
		}
	}
	if !req.StartUpdatedAt.IsZero() && !req.EndUpdatedAt.IsZero() {
		if req.EndUpdatedAt.Before(req.StartUpdatedAt) {
			errs = append(errs, servus.ErrorDetail{
				ErrorCode: apiErr.ErrInvalidUpdatedAtRange.Code,
				Message:   apiErr.ErrInvalidUpdatedAtRange.Message,
				Path:      "updatedAt",
			})
		}
	}

	return errs, nil
}

// isValidWhitelistKey checks if a given key is valid.
func isValidWhitelistKey(key api.WhitelistKey) bool {
	return key == api.WhitelistKey_IDNumber || key == api.WhitelistKey_ContactNumber || key == api.WhitelistKey_CIFNumber
}

// ValidateGetEligibilityRequestV2 ...
func (v *IDRequestValidator) ValidateGetEligibilityRequestV2(req *api.GetApplicantEligibilityRequestV2) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if req.IDNumber != "" && !regexp.MustCompile(`^[0-9]+$`).MatchString(req.IDNumber) {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: apiErr.ErrInvalidIDNumber.Code,
			Message:   apiErr.ErrInvalidIDNumber.Message,
			Path:      "IDNumber",
		})
	}
	if req.ContactNumber == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: apiErr.ErrMissingContactNumber.Code,
			Message:   apiErr.ErrMissingContactNumber.Message,
			Path:      "contactNumber",
		})
	} else if req.ContactNumber[0] == '+' {
		if !regexp.MustCompile(`^[0-9]+$`).MatchString(req.ContactNumber[1:]) {
			errs = append(errs, servus.ErrorDetail{
				ErrorCode: apiErr.ErrInvalidContactNumber.Code,
				Message:   apiErr.ErrInvalidContactNumber.Message,
				Path:      "contactNumber",
			})
		}
	} else if !regexp.MustCompile(`^[0-9]+$`).MatchString(req.ContactNumber) {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: apiErr.ErrInvalidContactNumber.Code,
			Message:   apiErr.ErrInvalidContactNumber.Message,
			Path:      "contactNumber",
		})
	}
	errs = append(errs, validation.ValidateApplicantID(req.CifNumber)...)

	return errs
}

// ValidateRejectionCodeRequest ...
func ValidateRejectionCodeRequest(req *api.UpdateFlexiTermLoanApplicationRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	RejectedBy := req.Message.StatusReason
	if RejectedBy == api.ApplicationStatusReason_HYGIENE_CHECK_REJECTED || RejectedBy == api.ApplicationStatusReason_FRAUD_PRE_BUREAU_REJECTED || RejectedBy == api.ApplicationStatusReason_FRAUD_POST_BUREAU_REJECTED {
		errs = append(errs, ValidateFraudCheckRejectReasons(req)...)
	} else if RejectedBy == api.ApplicationStatusReason_CDE_PRE_BUREAU_REJECTED || RejectedBy == api.ApplicationStatusReason_CDE_POST_BUREAU_REJECTED {
		errs = append(errs, ValidateCRDecisionRejectReasons(req)...)
	} else {
		// append error
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrRejectedByInvalid.Code, Message: apiErr.ErrRejectedByInvalid.Message})
	}
	return errs
}

// ValidateCRDecisionRejectReasons ...
func ValidateCRDecisionRejectReasons(req *api.UpdateFlexiTermLoanApplicationRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	decisionResponses := req.Message.Application.CreditDecisionResponse
	for _, decisionResponse := range decisionResponses {
		errs = append(errs, ValidateCallTypeCheck(decisionResponse.CallType)...)
		if (req.Message.StatusReason == api.ApplicationStatusReason_CDE_PRE_BUREAU_REJECTED && decisionResponse.CallType == constants.PreBureau) ||
			(req.Message.StatusReason == api.ApplicationStatusReason_CDE_POST_BUREAU_REJECTED && decisionResponse.CallType == constants.PostBureau) {
			errs = ValidateCDEFailureReq(decisionResponse, errs)
		}
	}
	return errs
}

// ValidateCDEFailureReq ...
func ValidateCDEFailureReq(decisionResponse api.CreditDecisionResponse, errs []servus.ErrorDetail) []servus.ErrorDetail {
	application := reqDataMapper.MapToDecisionApplication(decisionResponse.Application)
	for _, product := range application.Products {
		criterias := product.ProductDecision.UwCriterias
		if criterias == nil || len(criterias) <= 0 {
			// append error
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingRejectedErrorCodeArray.Code, Message: apiErr.ErrMissingRejectedErrorCodeArray.Message})
		}
	}
	return errs
}

// ValidateFraudCheckRejectReasons ...
func ValidateFraudCheckRejectReasons(req *api.UpdateFlexiTermLoanApplicationRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	decisionResponses := req.Message.Application.FraudCheckResponseV2
	for _, decisionResponse := range decisionResponses {
		errs = append(errs, ValidateCallTypeCheck(decisionResponse.FraudChecktype)...)
	}
	return errs
}

// ValidateCallTypeCheck ...
func ValidateCallTypeCheck(callType api.CallType) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(callType)) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrCallTypeInvalid.Code, Message: apiErr.ErrCallTypeNotValid.Message})
	}
	if _, ok := mapper.CallType[callType]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrCallTypeInvalid.Code, Message: apiErr.ErrCallTypeNotValid.Message})
	}
	return errs
}

// ValidateDeviceInfo ...
func ValidateDeviceInfo(deviceInfo *api.DeviceInfo) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	// GPSInfo validations
	errs = append(errs, ValidateGPSInfo(deviceInfo.GpsInfo)...)

	// OperatingSystem validations
	if strings.TrimSpace(deviceInfo.OperatingSystem) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingOperatingSystem.Code, Message: apiErr.ErrMissingOperatingSystem.Message})
	}

	// OsVersion validations
	if strings.TrimSpace(deviceInfo.OsVersion) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingOsVersion.Code, Message: apiErr.ErrMissingOsVersion.Message})
	}

	// DeviceMade validations
	if strings.TrimSpace(deviceInfo.DeviceMade) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingDeviceMade.Code, Message: apiErr.ErrMissingDeviceMade.Message})
	}

	// DeviceModel validations
	if strings.TrimSpace(deviceInfo.DeviceModel) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingDeviceModel.Code, Message: apiErr.ErrMissingDeviceModel.Message})
	}

	// DeviceID validations
	errs = append(errs, ValidateDeviceID(deviceInfo.DeviceID)...)

	// IpAddress validations
	if strings.TrimSpace(deviceInfo.IpAddress) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingIpAddress.Code, Message: apiErr.ErrMissingIpAddress.Message})
	}

	// AppVersion validations
	if strings.TrimSpace(deviceInfo.AppVersion) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingAppVersion.Code, Message: apiErr.ErrMissingAppVersion.Message})
	}

	return errs
}

// ValidateGPSInfo ...
func ValidateGPSInfo(gpsInfo *api.GPSInfo) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if gpsInfo.Latitude == 0 {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingLatitude.Code, Message: apiErr.ErrMissingLatitude.Message})
	}

	if gpsInfo.Longitude == 0 {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingLongitude.Code, Message: apiErr.ErrMissingLongitude.Message})
	}

	if gpsInfo.Accuracy == 0 {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingAccuracy.Code, Message: apiErr.ErrMissingAccuracy.Message})
	}

	return errs
}

// ValidateDeviceID ...
func ValidateDeviceID(deviceID *api.DeviceID) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if strings.TrimSpace(deviceID.Mobile) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingDeviceId.Code, Message: apiErr.ErrMissingDeviceId.Message})
	}

	return errs
}

func (v *IDRequestValidator) ValidateGetDocumentTypesRequest(req *api.GetDocumentTypesRequest) []servus.ErrorDetail {
	// TODO implement me
	return nil
}
