// Package request ...
package request

import (
	"fmt"

	"gitlab.myteksi.net/dakota/lending/common/countries"
)

var countryValidator map[string]Validator

func init() {
	countryValidator = make(map[string]Validator)
	registerValidator(countries.SG, &SGRequestValidator{})
	registerValidator(countries.ID, &IDRequestValidator{})
	registerValidator(countries.MY, &MYRequestValidator{})
}

func registerValidator(countryCode string, validator Validator) {
	if _, ok := countryValidator[countryCode]; !ok {
		countryValidator[countryCode] = validator
	} else {
		panic(fmt.Sprintf("countrCode=%s validator is already registered", countryCode))
	}
}

// GetRequestValidator ...
func GetRequestValidator(country string) Validator {
	if validator, ok := countryValidator[country]; ok {
		return validator
	}
	// todo: return default validator and fail the validation
	return &SGRequestValidator{} // default validator for backward compatibility
}
