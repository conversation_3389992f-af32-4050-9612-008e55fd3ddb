package request

import (
	"context"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// Validator ...
type Validator interface {
	ValidateCreateReq(req *api.CreateFlexiTermLoanApplicationRequest) []servus.ErrorDetail
	ValidateUpdateReq(ctx context.Context, req *api.UpdateFlexiTermLoanApplicationRequest) ([]servus.ErrorDetail, error)
	ValidateUpdateStatusReq(req *api.UpdateFlexiTermLoanApplicationStatusRequest) ([]servus.ErrorDetail, error)
	ValidateCreateOrUpdateWhitelistApplicantRequest(req *api.CreateOrUpdateWhitelistApplicantRequest) ([]servus.ErrorDetail, error)
	ValidateWhitelistedUserDetails(whitelistedUserDetail *api.WhitelistedUserDetails) []servus.ErrorDetail
	ValidateGetWhitelistApplicant(req *api.GetWhitelistApplicantListRequest) ([]servus.ErrorDetail, error)
	ValidateGetWhitelistHistoryApplicantRequest(req *api.GetWhitelistHistoryApplicantRequest) ([]servus.ErrorDetail, error)
	ValidateGetEligibilityRequestV2(req *api.GetApplicantEligibilityRequestV2) []servus.ErrorDetail
	ValidateCreateFlexiCardApplicationReq(req *api.CreateFlexiCardApplicationRequest) []servus.ErrorDetail
	ValidateCreateBundledApplicationReq(req *api.CreateBundledApplicationRequest) []servus.ErrorDetail
	ValidateGetDocumentTypesRequest(req *api.GetDocumentTypesRequest) []servus.ErrorDetail
}

// BaseImpl ...
type BaseImpl struct{}

// ValidateCreateReq ...
func (BaseImpl) ValidateCreateReq(req *api.CreateFlexiTermLoanApplicationRequest) []servus.ErrorDetail {
	// TODO implement me
	panic("implement me")
}

// ValidateUpdateReq ....
func (BaseImpl) ValidateUpdateReq(ctx context.Context, req *api.UpdateFlexiTermLoanApplicationRequest) ([]servus.ErrorDetail, error) {
	// TODO implement me
	panic("implement me")
}

// ValidateUpdateStatusReq ...
func (BaseImpl) ValidateUpdateStatusReq(req *api.UpdateFlexiTermLoanApplicationStatusRequest) ([]servus.ErrorDetail, error) {
	// TODO implement me
	panic("implement me")
}

// ValidateCreateOrUpdateWhitelistApplicantRequest ...
func (BaseImpl) ValidateCreateOrUpdateWhitelistApplicantRequest(req *api.CreateOrUpdateWhitelistApplicantRequest) ([]servus.ErrorDetail, error) {
	// TODO implement me
	panic("implement me")
}

// ValidateWhitelistedUserDetails ...
func (BaseImpl) ValidateWhitelistedUserDetails(whitelistedUserDetail *api.WhitelistedUserDetails) []servus.ErrorDetail {
	panic("implement me")
}

// ValidateGetWhitelistApplicant ...
func (BaseImpl) ValidateGetWhitelistApplicant(req *api.GetWhitelistApplicantListRequest) ([]servus.ErrorDetail, error) {
	// TODO implement me
	panic("implement me")
}

// ValidateGetWhitelistHistoryApplicantRequest ...
func (BaseImpl) ValidateGetWhitelistHistoryApplicantRequest(req *api.GetWhitelistHistoryApplicantRequest) ([]servus.ErrorDetail, error) {
	// TODO implement me
	panic("implement me")
}

// ValidateGetEligibilityRequestV2 ...
func (BaseImpl) ValidateGetEligibilityRequestV2(req *api.GetApplicantEligibilityRequestV2) []servus.ErrorDetail {
	// TODO implement me
	panic("implement me")
}

// ValidateCreateFlexiCardApplicationReq : to validate create flexi-card application request
func (BaseImpl) ValidateCreateFlexiCardApplicationReq(req *api.CreateFlexiCardApplicationRequest) []servus.ErrorDetail {
	//TODO implement me
	panic("implement me")
}

// ValidateCreateBundledApplicationReq : to validate create  application request
func (BaseImpl) ValidateCreateBundledApplicationReq(req *api.CreateBundledApplicationRequest) []servus.ErrorDetail {
	//TODO implement me
	panic("implement me")
}
