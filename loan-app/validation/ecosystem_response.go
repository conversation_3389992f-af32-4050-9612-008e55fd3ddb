package validation

import (
	"strings"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/mapper"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// ValidateEcosystemResponse ...
func ValidateEcosystemResponse(ecosystemResponse []api.EcosystemResponse) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	for _, e := range ecosystemResponse {
		errs = append(errs, ValidatePartner(e.Partner)...)
		errs = append(errs, ValidateResponseStatus(e.Status)...)
	}
	return errs
}

// ValidatePartner ...
func ValidatePartner(partner string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(partner) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingPartner.Code, Message: apiErr.ErrMissingPartner.Message})
	}
	if _, ok := mapper.Partner[partner]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidPartner.Code, Message: apiErr.ErrInvalidPartner.Message})
	}
	return errs
}
