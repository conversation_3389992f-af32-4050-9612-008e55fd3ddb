package validation

import (
	"strings"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/mapper"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// ValidateDBMYApplicants ...
func ValidateDBMYApplicants(applicants []api.FlexiTermLoanApplicant) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if len(applicants) == 0 {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingApplicants.Code, Message: apiErr.ErrMissingApplicants.Message})
	}

	for _, applicant := range applicants {
		// ValidateSafeID
		errs = append(errs, ValidateSafeID(applicant.SafeID)...)

		// ValidateApplicantID
		errs = append(errs, ValidateApplicantID(applicant.ApplicantID)...)

		// Validate contact details
		errs = append(errs, ValidateContactType(applicant.ContactType)...)
		errs = append(errs, ValidateContactNumber(applicant.ContactNumber)...)

		// ValidateFullName
		errs = append(errs, ValidateFullName(applicant.FullName)...)

		// ValidateDateOfBirth
		errs = append(errs, ValidateDateOfBirth(applicant.DateOfBirth)...)

		// ValidateNationality
		errs = append(errs, ValidateNationality(applicant.Nationality)...)

		// ValidateIDDetails
		errs = append(errs, ValidateIDDetails(applicant.PrimaryID)...)

		// ValidateGender
		errs = append(errs, ValidateGender(applicant.Gender)...)

		// ValidateEmploymentDetail
		errs = append(errs, ValidateDBMYEmploymentDetail(applicant.EmploymentDetail)...)

		// ValidateDBMYAddresses
		errs = append(errs, ValidateDBMYAddresses(applicant.AddressDetails)...)

		// ValidateConsent
		errs = append(errs, ValidateConsent(applicant.Consent)...)

		// Validate Non Fi commitment
		errs = append(errs, ValidateNonFiCommitment(applicant.AdditionalData)...)
	}
	return errs
}

// ValidateNonFiCommitment ...
func ValidateNonFiCommitment(additionalDatas []api.AdditionalData) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	found := false
	for _, additionalData := range additionalDatas {
		if additionalData.Key == string(mapper.NonFiCommitment) {
			found = true
			if strings.TrimSpace(additionalData.Value) == "" {
				return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidNonFiCommitment.Code, Message: apiErr.ErrInvalidNonFiCommitment.Message})
			}
		}
	}

	if !found {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingNonFiCommitment.Code, Message: apiErr.ErrMissingNonFiCommitment.Message})
	}

	return errs
}

// ValidateNationality ...
func ValidateNationality(nationality string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(nationality) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingNationality.Code, Message: apiErr.ErrMissingNationality.Message})
	}
	return errs
}

// ValidateSafeID ...
func ValidateSafeID(safeID string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(safeID) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingSafeID.Code, Message: apiErr.ErrMissingSafeID.Message})
	}
	return errs
}

// ValidateIDDetails ...
func ValidateIDDetails(idDetails *api.IDDetails) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	errs = append(errs, ValidateIDType(idDetails.Type)...)
	errs = append(errs, ValidateIDNumber(idDetails.Number)...)

	return errs
}

// ValidateDBMYEmploymentDetail ...
func ValidateDBMYEmploymentDetail(employmentDetail *api.EmploymentDetail) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if employmentDetail == nil {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingEmploymentInfo.Code, Message: apiErr.ErrMissingEmploymentInfo.Message})
	}

	// Validate Employment type
	errs = append(errs, ValidateDBMYEmploymentType(employmentDetail.EmploymentType)...)
	// Validate income per month
	errs = append(errs, ValidateIncomePerMonth(employmentDetail.IncomePerMonth)...)
	// Occupation code
	errs = append(errs, ValidateOccupation(employmentDetail.OccupationCode)...)

	return errs
}

// ValidateOccupation ...
func ValidateOccupation(occupationCode string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(occupationCode) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingOccupationCode.Code, Message: apiErr.ErrMissingOccupationCode.Message})
	}
	return errs
}

// ValidateDBMYEmploymentType ...
func ValidateDBMYEmploymentType(employmentType api.EmploymentType) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(employmentType)) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingEmploymentType.Code, Message: apiErr.ErrMissingEmploymentType.Message})
	}

	if _, ok := mapper.EmploymentTypeDBMY[employmentType]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidEmploymentType.Code, Message: apiErr.ErrInvalidEmploymentType.Message})
	}
	return errs
}

// ValidateDBMYAddresses ...
func ValidateDBMYAddresses(addresses []api.AddressDetails) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if len(addresses) == 0 {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingAddressInfo.Code, Message: apiErr.ErrMissingAddressInfo.Message})
	}

	for _, address := range addresses {
		// Address type
		errs = append(errs, ValidateAddressType(address.AddressType)...)
		// Address line
		errs = append(errs, ValidateAddressLine(address.AddressLine)...)
		// Validate Province
		errs = append(errs, ValidateProvince(address.Province)...)
		// Validate Postcode
		errs = append(errs, ValidatePostalCode(address.PostalCode)...)
	}
	return errs
}

// ValidateAddressLine ...
func ValidateAddressLine(addressLine string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(addressLine) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingAddressLine.Code, Message: apiErr.ErrMissingAddressLine.Message})
	}
	return errs
}

// ValidateProvince ...
func ValidateProvince(province string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(province) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingProvince.Code, Message: apiErr.ErrMissingProvince.Message})
	}
	return errs
}

// ValidateCity ...
func ValidateCity(city string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(city) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingCity.Code, Message: apiErr.ErrMissingCity.Message})
	}
	return errs
}
