package validation

import (
	"context"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.com/gx-regional/dakota/lending/loan-app/test/requests"
	"gitlab.myteksi.net/dakota/lending/common/countries"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

const (
	invalidValue = "INVALID"
)

func Test_ValidateInitDocumentUploadRequest(t *testing.T) {
	type args struct {
		ctx context.Context
		req *api.InitDocumentUploadRequest
	}

	type testCase struct {
		name    string
		args    args
		reqFunc func(req *api.InitDocumentUploadRequest)
		want    []servus.ErrorDetail
		wantErr bool
	}

	tests := []testCase{
		{
			name: "happy-path: Validation succeeded",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.InitDocumentUploadRequest) {
				// request is complete to validate
			},
			wantErr: false,
		},
		{
			name: "sad-path: referenceID not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.InitDocumentUploadRequest) {
				req.ReferenceID = ""
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrMissingReferenceID.Code,
					Message:   commonErr.ErrMissingReferenceID.Message,
				},
			},
		},
		{
			name: "sad-path: onboardingApplicationID not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.InitDocumentUploadRequest) {
				req.OnboardingApplicationID = ""
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrMissingOnboardingApplicationID.Code,
					Message:   commonErr.ErrMissingOnboardingApplicationID.Message,
				},
			},
		},
		{
			name: "sad-path: productVariantCode not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.InitDocumentUploadRequest) {
				req.ProductVariantCode = ""
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrMissingProductVariantCode.Code,
					Message:   commonErr.ErrMissingProductVariantCode.Message,
				},
			},
		},
		{
			name: "sad-path: productVariantCode not valid",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.InitDocumentUploadRequest) {
				req.ProductVariantCode = invalidValue
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrInvalidProductVariantCode.Code,
					Message:   commonErr.ErrInvalidProductVariantCode.Message,
				},
			},
		},
		{
			name: "sad-path: fileType not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.InitDocumentUploadRequest) {
				req.FileType = ""
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrMissingFileType.Code,
					Message:   commonErr.ErrMissingFileType.Message,
				},
			},
		},
		{
			name: "sad-path: fileType not valid",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.InitDocumentUploadRequest) {
				req.FileType = invalidValue
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrInvalidFileType.Code,
					Message:   commonErr.ErrInvalidFileType.Message,
				},
			},
		},
		{
			name: "sad-path: subFileType not valid",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.InitDocumentUploadRequest) {
				req.SubFileType = invalidValue
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrInvalidSubFileType.Code,
					Message:   commonErr.ErrInvalidSubFileType.Message,
				},
			},
		},
		{
			name: "sad-path: personal bank statement subFileType not supported when enablePersonalBankStatementsUpload feature flag is false",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.InitDocumentUploadRequest) {
				req.SubFileType = api.SubFileType_PERSONAL_BANK_STATEMENT
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrInvalidSubFileType.Code,
					Message:   commonErr.ErrInvalidSubFileType.Message,
					Path:      "subFileType",
				},
			},
		},
	}

	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			request := requests.SampleInitDocumentUploadRequest()
			test.reqFunc(request)
			test.args.req = request

			constants.DefaultCountryCode = countries.MY
			err := ValidateInitDocumentUploadRequest(test.args.req)
			if (len(err) != 0) != test.wantErr {
				t.Errorf("ValidateInitDocumentUploadRequest() error = %v, wantErr %t", err, test.wantErr)
				return
			}

			if !test.wantErr {
				assert.Equal(t, test.want, err)
			}

			t.Log(err)
		})
	}
}

func Test_ValidateInitEpfLoginRequest(t *testing.T) {
	type args struct {
		ctx context.Context
		req *api.InitEpfLoginRequest
	}

	type testCase struct {
		name    string
		args    args
		reqFunc func(req *api.InitEpfLoginRequest)
		want    []servus.ErrorDetail
		wantErr bool
	}

	tests := []testCase{
		{
			name: "happy-path: Validation succeeded",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.InitEpfLoginRequest) {
				// request is complete to validate
			},
			wantErr: false,
		},
		{
			name: "sad-path: referenceID not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.InitEpfLoginRequest) {
				req.ReferenceID = ""
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrMissingReferenceID.Code,
					Message:   commonErr.ErrMissingReferenceID.Message,
				},
			},
		},
		{
			name: "sad-path: onboardingApplicationID not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.InitEpfLoginRequest) {
				req.OnboardingApplicationID = ""
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrMissingOnboardingApplicationID.Code,
					Message:   commonErr.ErrMissingOnboardingApplicationID.Message,
				},
			},
		},
		{
			name: "sad-path: productVariantCode not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.InitEpfLoginRequest) {
				req.ProductVariantCode = ""
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrMissingProductVariantCode.Code,
					Message:   commonErr.ErrMissingProductVariantCode.Message,
				},
			},
		},
		{
			name: "sad-path: productVariantCode not valid",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.InitEpfLoginRequest) {
				req.ProductVariantCode = invalidValue
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrInvalidProductVariantCode.Code,
					Message:   commonErr.ErrInvalidProductVariantCode.Message,
				},
			},
		},
	}

	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			request := requests.SampleInitEpfLoginRequest()
			test.reqFunc(request)
			test.args.req = request

			err := ValidateInitEpfLoginRequest(test.args.req)
			if (len(err) != 0) != test.wantErr {
				t.Errorf("ValidateInitEpfLoginRequest() error = %v, wantErr %t", err, test.wantErr)
				return
			}

			if !test.wantErr {
				assert.Equal(t, test.want, err)
			}

			t.Log(err)
		})
	}
}

func Test_ValidateDocumentUploadRequest(t *testing.T) {
	type args struct {
		ctx context.Context
		req *api.DocumentUploadRequest
	}

	type testCase struct {
		name    string
		args    args
		reqFunc func(req *api.DocumentUploadRequest)
		want    error
		wantErr bool
	}

	tests := []testCase{
		{
			name: "happy-path: Validation succeeded",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.DocumentUploadRequest) {
				// request is complete to validate
			},
			wantErr: false,
		},
		{
			name: "sad-path: referenceID not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.DocumentUploadRequest) {
				req.ReferenceID = ""
			},
			wantErr: true,
			want: commonErr.BuildErrorResponseWithErrorDetail(
				http.StatusBadRequest, commonErr.ErrInvalidInitDocumentUploadRequest.Code,
				commonErr.ErrInvalidInitDocumentUploadRequest.Message, []servus.ErrorDetail{
					{
						ErrorCode: commonErr.ErrMissingReferenceID.Code,
						Message:   commonErr.ErrMissingReferenceID.Message,
					},
				}),
		},
		{
			name: "sad-path: initTxnReferenceID not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.DocumentUploadRequest) {
				req.InitTxnReferenceID = ""
			},
			wantErr: true,
			want: commonErr.BuildErrorResponseWithErrorDetail(
				http.StatusBadRequest, commonErr.ErrInvalidInitDocumentUploadRequest.Code,
				commonErr.ErrInvalidInitDocumentUploadRequest.Message, []servus.ErrorDetail{
					{
						ErrorCode: commonErr.ErrMissingInitTxnReferenceID.Code,
						Message:   commonErr.ErrMissingInitTxnReferenceID.Message,
					},
				}),
		},
		{
			name: "sad-path: onboardingApplicationID not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.DocumentUploadRequest) {
				req.OnboardingApplicationID = ""
			},
			wantErr: true,
			want: commonErr.BuildErrorResponseWithErrorDetail(
				http.StatusBadRequest, commonErr.ErrInvalidInitDocumentUploadRequest.Code,
				commonErr.ErrInvalidInitDocumentUploadRequest.Message, []servus.ErrorDetail{
					{
						ErrorCode: commonErr.ErrMissingOnboardingApplicationID.Code,
						Message:   commonErr.ErrMissingOnboardingApplicationID.Message,
					},
				}),
		},
		{
			name: "sad-path: fileID not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.DocumentUploadRequest) {
				req.FileID = ""
			},
			wantErr: true,
			want: commonErr.BuildErrorResponseWithErrorDetail(
				http.StatusBadRequest, commonErr.ErrInvalidInitDocumentUploadRequest.Code,
				commonErr.ErrInvalidInitDocumentUploadRequest.Message, []servus.ErrorDetail{
					{
						ErrorCode: commonErr.ErrMissingFileID.Code,
						Message:   commonErr.ErrMissingFileID.Message,
					},
				}),
		},
		{
			name: "sad-path: fileBase64String not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.DocumentUploadRequest) {
				req.FileBase64String = ""
			},
			wantErr: true,
			want: commonErr.BuildErrorResponseWithErrorDetail(
				http.StatusBadRequest, commonErr.ErrInvalidInitDocumentUploadRequest.Code,
				commonErr.ErrInvalidInitDocumentUploadRequest.Message, []servus.ErrorDetail{
					{
						ErrorCode: commonErr.ErrMissingFileByte.Code,
						Message:   commonErr.ErrMissingFileByte.Message,
					},
				}),
		},
	}

	fileTypeConfig := config.FileTypeConfig{
		MaxFileSizeInBytes: "5242880",
		AllowedExtensions:  []string{"PDF"},
	}

	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			request := requests.SampleDocumentUploadRequest()
			test.reqFunc(request)
			test.args.req = request

			err := ValidateDocumentUploadRequest(test.args.req, fileTypeConfig)
			if (err != nil) != test.wantErr {
				t.Errorf("ValidateDocumentUploadRequest() error = %v, wantErr %t", err, test.wantErr)
				return
			}

			if !test.wantErr {
				assert.Equal(t, test.want, err)
			}

			t.Log(err)
		})
	}
}

func Test_ValidateDocument(t *testing.T) {
	type args struct {
		ctx context.Context
		req *api.DocumentUploadRequest
	}

	type testCase struct {
		name        string
		args        args
		reqFunc     func(req *api.DocumentUploadRequest)
		want        error
		wantErr     bool
		fileSizeErr bool
	}

	tests := []testCase{
		{
			name: "happy-path: Validation succeeded",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.DocumentUploadRequest) {
				// request is complete to validate
			},
			wantErr: false,
			want:    nil,
		},
		{
			name: "sad-path: fileByte not valid",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.DocumentUploadRequest) {
				req.FileBase64String = "JVBERi0INVALIDFILEBYTE"
			},
			wantErr: true,
			want: servus.ServiceError{
				HTTPCode: http.StatusBadRequest,
				Code:     commonErr.ErrInvalidFileByte.Code,
				Message:  commonErr.ErrInvalidFileByte.Message,
			},
		},
		{
			name: "sad-path: file size not valid",
			args: args{
				ctx: context.Background(),
			},
			reqFunc:     func(req *api.DocumentUploadRequest) {},
			wantErr:     true,
			fileSizeErr: true,
			want: servus.ServiceError{
				HTTPCode: http.StatusBadRequest,
				Code:     commonErr.ErrInvalidFileSize.Code,
				Message:  commonErr.ErrInvalidFileSize.Message,
			},
		},
	}

	fileTypeConfig := config.FileTypeConfig{
		MaxFileSizeInBytes: "5242880",
		AllowedExtensions:  []string{"PDF"},
	}

	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			request := requests.SampleDocumentUploadRequest()
			test.reqFunc(request)
			test.args.req = request

			if test.fileSizeErr {
				fileTypeConfig.MaxFileSizeInBytes = "1024"
			}

			err := ValidateDocument(test.args.req, fileTypeConfig)

			if (err != nil) != test.wantErr {
				t.Errorf("ValidateDocument() error = %v, wantErr %t", err, test.wantErr)
				return
			}

			if !test.wantErr {
				assert.Equal(t, test.want, err)
			}

			t.Log(err)
		})
	}
}

func Test_ValidateConfirmDocumentUploadRequest(t *testing.T) {
	type args struct {
		ctx context.Context
		req *api.ConfirmDocumentUploadRequest
	}

	type testCase struct {
		name    string
		args    args
		reqFunc func(req *api.ConfirmDocumentUploadRequest)
		want    []servus.ErrorDetail
		wantErr bool
	}

	tests := []testCase{
		{
			name: "happy-path: Validation succeeded",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.ConfirmDocumentUploadRequest) {
				// request is complete to validate
			},
			wantErr: false,
		},
		{
			name: "sad-path: referenceID not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.ConfirmDocumentUploadRequest) {
				req.ReferenceID = ""
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrMissingReferenceID.Code,
					Message:   commonErr.ErrMissingReferenceID.Message,
				},
			},
		},
		{
			name: "sad-path: onboardingApplicationID not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.ConfirmDocumentUploadRequest) {
				req.OnboardingApplicationID = ""
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrMissingOnboardingApplicationID.Code,
					Message:   commonErr.ErrMissingOnboardingApplicationID.Message,
				},
			},
		},
	}

	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			request := requests.SampleConfirmDocumentUploadRequest()
			test.reqFunc(request)
			test.args.req = request

			err := ValidateConfirmDocumentUploadRequest(test.args.req)
			if (len(err) != 0) != test.wantErr {
				t.Errorf("ValidateConfirmDocumentUploadRequest() error = %v, wantErr %t", err, test.wantErr)
				return
			}

			if !test.wantErr {
				assert.Equal(t, test.want, err)
			}

			t.Log(err)
		})
	}
}

func Test_ValidateConfirmEpfLoginRequest(t *testing.T) {
	type args struct {
		ctx context.Context
		req *api.ConfirmEpfLoginRequest
	}

	type testCase struct {
		name    string
		args    args
		reqFunc func(req *api.ConfirmEpfLoginRequest)
		want    []servus.ErrorDetail
		wantErr bool
	}

	tests := []testCase{
		{
			name: "happy-path: Validation succeeded",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.ConfirmEpfLoginRequest) {
				// request is complete to validate
			},
			wantErr: false,
		},
		{
			name: "sad-path: referenceID not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.ConfirmEpfLoginRequest) {
				req.ReferenceID = ""
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrMissingReferenceID.Code,
					Message:   commonErr.ErrMissingReferenceID.Message,
				},
			},
		},
		{
			name: "sad-path: onboardingApplicationID not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.ConfirmEpfLoginRequest) {
				req.OnboardingApplicationID = ""
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrMissingOnboardingApplicationID.Code,
					Message:   commonErr.ErrMissingOnboardingApplicationID.Message,
				},
			},
		},
		{
			name: "sad-path: clientTxnID not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.ConfirmEpfLoginRequest) {
				req.ClientTxnID = ""
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrMissingClientTxnID.Code,
					Message:   commonErr.ErrMissingClientTxnID.Message,
				},
			},
		},
		{
			name: "sad-path: status not passed",
			args: args{
				ctx: context.Background(),
			},
			reqFunc: func(req *api.ConfirmEpfLoginRequest) {
				req.Status = nil
			},
			wantErr: true,
			want: []servus.ErrorDetail{
				{
					ErrorCode: commonErr.ErrMissingEpfLoginStatus.Code,
					Message:   commonErr.ErrMissingEpfLoginStatus.Message,
				},
			},
		},
	}

	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			request := requests.SampleConfirmEpfLoginRequest(true)
			test.reqFunc(request)
			test.args.req = request

			err := ValidateConfirmEpfLoginRequest(test.args.req)
			if (len(err) != 0) != test.wantErr {
				t.Errorf("ValidateConfirmEpfLoginRequest() error = %v, wantErr %t", err, test.wantErr)
				return
			}

			if !test.wantErr {
				assert.Equal(t, test.want, err)
			}

			t.Log(err)
		})
	}
}
