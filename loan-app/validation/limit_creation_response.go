package validation

import (
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// ValidateLimitCreationResponse ...
func ValidateLimitCreationResponse(limitCreationResponse *api.LimitCreationResponse) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if limitCreationResponse == nil {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingLimitCreationResponse.Code, Message: apiErr.ErrMissingLimitCreationResponse.Message})
	}
	return append(errs, ValidateResponseStatus(limitCreationResponse.Status)...)
}
