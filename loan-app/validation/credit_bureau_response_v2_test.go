package validation

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
)

func Test_ValidateCreditBureauResponseV2(t *testing.T) {
	scenarios := []struct {
		desc     string
		data     []api.CreditBureauResponse
		hasError bool
	}{
		{
			desc:     "Failure - No response received",
			data:     nil,
			hasError: true,
		},
		{
			desc: "Success - Credit Bureau Data Status SUCCESS",
			data: []api.CreditBureauResponse{{
				Status:     "SUCCESS",
				DataSource: "Test data source",
			}},
			hasError: false,
		},
		{
			desc: "Failure - Credit Bureau Data Status empty",
			data: []api.CreditBureauResponse{{
				Status:     "",
				DataSource: "Test data source",
			}},
			hasError: true,
		},
	}
	for _, tt := range scenarios {
		test := tt
		t.Run(test.desc, func(t *testing.T) {
			error := ValidateCreditBureauResponseV2(test.data)
			if test.hasError == false {
				assert.Nil(t, error)
			} else {
				assert.NotNil(t, error)
			}
		})
	}
}

func Test_ValidateDataSource(t *testing.T) {
	scenarios := []struct {
		desc     string
		data     string
		hasError bool
	}{
		{
			desc:     "Failure - Empty data source",
			data:     "",
			hasError: true,
		},
		{
			desc:     "Success - Data source present ",
			data:     "Test data source",
			hasError: false,
		},
	}
	for _, tt := range scenarios {
		test := tt
		t.Run(test.desc, func(t *testing.T) {
			error := ValidateDataSource(test.data)
			if test.hasError == false {
				assert.Nil(t, error)
			} else {
				assert.NotNil(t, error)
			}
		})
	}
}
