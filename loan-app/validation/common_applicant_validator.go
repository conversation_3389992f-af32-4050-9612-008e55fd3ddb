package validation

import (
	"strings"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/mapper"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// ValidateApplicantType ...
// nolint:dupl
func ValidateApplicantType(applicantType api.ApplicantType) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(applicantType)) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingApplicantType.Code, Message: apiErr.ErrMissingApplicantType.Message})
	}
	if _, ok := mapper.ApplicantTypes[applicantType]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidApplicantType.Code, Message: apiErr.ErrInvalidApplicantType.Message})
	}
	return errs
}

// ValidateIDType ...
// nolint:dupl
func ValidateIDType(idType api.IDType) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(idType)) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingIDType.Code, Message: apiErr.ErrMissingIDType.Message})
	}
	if _, ok := mapper.IDTypes[idType]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidIDType.Code, Message: apiErr.ErrInvalidIDType.Message})
	}
	return errs
}

// ValidateIDNumber ...
// nolint:dupl
func ValidateIDNumber(IDNumber string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(IDNumber) == "" || len(IDNumber) > 36 {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingIDNumber.Code, Message: apiErr.ErrMissingIDNumber.Message})
	}
	return errs
}

// ValidateGender ...
// nolint:dupl
func ValidateGender(gender api.Gender) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(gender)) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingGender.Code, Message: apiErr.ErrMissingGender.Message})
	}
	if _, ok := mapper.Gender[gender]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidGender.Code, Message: apiErr.ErrInvalidGender.Message})
	}
	return errs
}

// ValidateMaritalStatus ...
// nolint:dupl
func ValidateMaritalStatus(maritalStatus api.MaritalStatus) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(maritalStatus)) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingMaritalStatus.Code, Message: apiErr.ErrMissingMaritalStatus.Message})
	}
	if _, ok := mapper.MaritalStatus[maritalStatus]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidMaritalStatus.Code, Message: apiErr.ErrInvalidMaritalStatus.Message})
	}
	return errs
}

// ValidateContactType ...
// nolint:dupl
func ValidateContactType(contactType string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(contactType) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingContactType.Code, Message: apiErr.ErrMissingContactType.Message})
	}
	if _, ok := mapper.ContactType[contactType]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidContactType.Code, Message: apiErr.ErrInvalidContactType.Message})
	}
	return errs
}

// ValidateAddressType ...
// nolint:dupl
func ValidateAddressType(addressType string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(addressType) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingAddressType.Code, Message: apiErr.ErrMissingAddressType.Message})
	}
	if _, ok := mapper.AddressType[addressType]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidAddressType.Code, Message: apiErr.ErrInvalidAddressType.Message})
	}
	return errs
}

// ValidateWhitelistFlag ...
// nolint:dupl
func ValidateWhitelistFlag(whitelistFlag string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(whitelistFlag) == "" {
		return errs
	}
	if _, ok := mapper.WhitelistFlag[whitelistFlag]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidWhitelistFlag.Code, Message: apiErr.ErrInvalidWhitelistFlag.Message})
	}
	return errs
}

// ValidateIncomeAmount ...
func ValidateIncomeAmount(incomeComponents []api.IncomeComponent) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if incomeComponents == nil {
		return nil
	}
	for _, incomeComponent := range incomeComponents {
		incomeOccurrences := incomeComponent.IncomeOccurrences
		if incomeOccurrences == nil {
			continue
		}
		for _, incomeOccurrence := range incomeOccurrences {
			if incomeOccurrence.IncomeAmount < 0 {
				errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidIncomeAmount.Code, Message: apiErr.ErrInvalidIncomeAmount.Message})
			}
		}
	}
	return errs
}

// ValidateApplicantSafeID ...
// nolint:dupl
func ValidateApplicantSafeID(safeID string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(safeID) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingSafeID.Code, Message: apiErr.ErrMissingSafeID.Message})
	}
	return errs
}
