package validation

import (
	"strings"

	crDecisionEng "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/logic/common"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/mapper"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// ValidateCreditDecisionResponse ...
func ValidateCreditDecisionResponse(creditDecisionResponse []api.CreditDecisionResponse) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if len(creditDecisionResponse) == 0 {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingCreditDecisionResponse.Code, Message: apiErr.ErrMissingCreditDecisionResponse.Message})
	}
	for _, c := range creditDecisionResponse {
		errs = append(errs, ValidateCallType(c.CallType)...)
		errs = append(errs, ValidateResponseStatus(c.Status)...)
	}
	return errs
}

// ValidateCallType ...
func ValidateCallType(callType api.CallType) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(callType)) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingCallType.Code, Message: apiErr.ErrMissingCallType.Message})
	}
	if _, ok := mapper.CallType[callType]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidCallType.Code, Message: apiErr.ErrInvalidCallType.Message})
	}
	return errs
}

// ValidateCreditDecisionEngResponse ...
func ValidateCreditDecisionEngResponse(response *crDecisionEng.FICOCreditDecisionResponse) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if response == nil || response.Message == nil {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingCreditDecisionResponse.Code, Message: apiErr.ErrMissingCreditDecisionResponse.Message})
	}
	if response.Message.Status == nil {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingStatus.Code, Message: apiErr.ErrMissingStatus.Message})
	}
	return errs
}

// ValidateProductDecision ...
func ValidateProductDecision(application *crDecisionEng.Application, productTypes []crDecisionEng.ProductType) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if application == nil {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingApplication.Code, Message: apiErr.ErrMissingApplication.Message})
	}
	if application.Products == nil {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingProducts.Code, Message: apiErr.ErrMissingProducts.Message})
	}
	for _, productType := range productTypes {
		products := common.GetProductByType(application.Products, productType)
		if len(products) != 1 || products[0].ProductDecision == nil {
			return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingProductDecision.Code, Message: apiErr.ErrMissingProductDecision.Message})
		}
		if products[0].ProductDecision.RecommendedCreditDecision == nil || *products[0].ProductDecision.RecommendedCreditDecision == "" {
			return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingRecommendedCreditDecision.Code, Message: apiErr.ErrMissingRecommendedCreditDecision.Message})
		}
	}
	return errs
}
