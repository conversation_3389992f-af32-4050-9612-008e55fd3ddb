package validation

import (
	"strings"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// ValidateCreditBureauResponseV2 ...
func ValidateCreditBureauResponseV2(creditBureauResponse []api.CreditBureauResponse) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if creditBureauResponse == nil {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingCreditBureauResponse.Code, Message: apiErr.ErrMissingCreditBureauResponse.Message})
	}
	for _, response := range creditBureauResponse {
		errs = append(errs, ValidateResponseStatus(response.Status)...)
		errs = append(errs, ValidateDataSource(response.DataSource)...)
	}
	return errs
}

// ValidateDataSource ...
func ValidateDataSource(dataSource string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(dataSource) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingDataSource.Code, Message: apiErr.ErrMissingDataSource.Message})
	}
	return errs
}
