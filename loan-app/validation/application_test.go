package validation

import (
	"context"
	"testing"

	"gitlab.myteksi.net/dakota/lending/common/countries"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func TestValidateApplicationType(t *testing.T) {
	type args struct {
		applicationType api.ApplicationType
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "should throw error when there is empty application type",
			args:    args{applicationType: ""},
			wantErr: true,
		},
		{
			name:    "should throw error when there is invalid application type",
			args:    args{applicationType: "INVALID"},
			wantErr: true,
		},
		{
			name:    "should return nil when application type is passed proper",
			args:    args{applicationType: api.ApplicationType_NEW},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		data := tt
		t.Run(data.name, func(t *testing.T) {
			if err := ValidateApplicationType(data.args.applicationType); (err != nil) != data.wantErr {
				t.Errorf("ValidateApplicationType() = %v, want %v", err, data.wantErr)
			}
		})
	}
}

func TestValidateChannel(t *testing.T) {
	type args struct {
		channel api.Channel
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "should throw error when there is empty channel",
			args:    args{channel: ""},
			wantErr: true,
		},
		{
			name:    "should throw error when there is invalid channel",
			args:    args{channel: "INVALID"},
			wantErr: true,
		},
		{
			name:    "should return nil when channel is passed proper",
			args:    args{channel: api.Channel_GRAB},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		data := tt
		t.Run(data.name, func(t *testing.T) {
			if err := ValidateChannel(data.args.channel); (err != nil) != data.wantErr {
				t.Errorf("ValidateChannel() = %v, want %v", err, data.wantErr)
			}
		})
	}
}

func TestValidateStatusAndReason(t *testing.T) {
	type args struct {
		message *api.Message
	}
	tests := []struct {
		name              string
		args              args
		application       []*storage.Application
		dbErr             error
		wantValidationErr bool
		wantDBErr         bool
	}{
		{
			name:              "should throw error when there is empty status",
			args:              args{&api.Message{Status: "", Application: &api.FlexiTermLoanApplication{ApplicationID: "123"}}},
			dbErr:             data.ErrNoData,
			wantValidationErr: true,
		},
		{
			name:              "should throw error when there is wrong status",
			args:              args{&api.Message{Status: "abc", Application: &api.FlexiTermLoanApplication{ApplicationID: "123"}}},
			dbErr:             data.ErrNoData,
			wantValidationErr: true,
		},
		{
			name:              "should throw error when there is empty status",
			args:              args{&api.Message{StatusReason: "", Application: &api.FlexiTermLoanApplication{ApplicationID: "123"}}},
			dbErr:             data.ErrNoData,
			wantValidationErr: true,
		},
		{
			name:              "should throw error when there is wrong status",
			args:              args{&api.Message{StatusReason: "abc", Application: &api.FlexiTermLoanApplication{ApplicationID: "123"}}},
			dbErr:             data.ErrNoData,
			wantValidationErr: true,
		},
		{
			name:              "should return error when there is an approved application",
			args:              args{&api.Message{Status: api.ApplicationStatus_ApplicationStatus_PENDING_ACCEPTANCE, StatusReason: api.ApplicationStatusReason_CDE_POST_BUREAU_APPROVED, Application: &api.FlexiTermLoanApplication{ApplicationID: "123", CountryCode: countries.ID}}},
			application:       []*storage.Application{{Status: "APPROVED", StatusReason: "LIMIT_CREATION_SUCCESSFUL"}},
			wantValidationErr: true,
		},
		{
			name:              "should return nil when there is an approved application",
			dbErr:             data.ErrNoData,
			args:              args{&api.Message{Status: api.ApplicationStatus_ApplicationStatus_PENDING_ACCEPTANCE, StatusReason: api.ApplicationStatusReason_CDE_POST_BUREAU_APPROVED, Application: &api.FlexiTermLoanApplication{ApplicationID: "123", CountryCode: countries.ID}}},
			wantValidationErr: false,
		},
		{
			name:      "should return error when there is an DB Error",
			dbErr:     data.ErrTimedOut,
			args:      args{&api.Message{Status: api.ApplicationStatus_ApplicationStatus_PENDING_ACCEPTANCE, StatusReason: api.ApplicationStatusReason_CDE_POST_BUREAU_APPROVED, Application: &api.FlexiTermLoanApplication{ApplicationID: "123", CountryCode: countries.ID}}},
			wantDBErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			mockApplicationDAO := &storage.MockIApplicationDAO{}
			storage.ApplicationDao = mockApplicationDAO
			mockApplicationDAO.On("Find", mock.Anything, mock.Anything).
				Return(tt.application, tt.dbErr)
			validationErr, dbErr := ValidateStatusAndStatusReason(context.Background(), tt.args.message)
			if tt.wantValidationErr {
				assert.NotEmpty(t, validationErr)
			} else if tt.wantDBErr {
				assert.Error(t, dbErr)
			} else {
				assert.Empty(t, validationErr)
			}
		})
	}
}

func TestValidateProducts(t *testing.T) {
	type args struct {
		products []api.Product
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "should throw error when products are empty",
			args: args{
				products: nil,
			},
			wantErr: true,
		},
		{
			name: "should throw error when product code is invalid",
			args: args{
				products: []api.Product{
					{
						ProductType: "",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should throw error when product code is invalid",
			args: args{
				products: []api.Product{
					{
						ProductType: "INVALID",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return nil when product type is passed proper",
			args: args{
				products: []api.Product{
					{
						ProductType:    api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
						SubProductType: api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should throw error when product sub type is missing",
			args: args{
				products: []api.Product{
					{
						ProductType:           "",
						SubProductType:        "",
						RequestedLoanAmount:   0,
						RequestedCurrencyCode: "",
						RequestedTenure:       0,
						InterestRate:          0,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return error when product sub type is invalid",
			args: args{
				products: []api.Product{
					{
						SubProductType: "INVALID",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should return nil when proper product sub type is passed proper",
			args: args{
				products: []api.Product{
					{
						ProductType:    api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
						SubProductType: api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		data := tt
		t.Run(data.name, func(t *testing.T) {
			if err := ValidateProducts(data.args.products); (err != nil) != data.wantErr {
				t.Errorf("ValidateProductCode() = %v, want %v", err, data.wantErr)
			}
		})
	}
}

func TestValidateCreatedBy(t *testing.T) {
	tests := []struct {
		name      string
		createdBy string
		wantErr   bool
	}{
		{
			name:      "should return error if createdBy is empty",
			createdBy: "",
			wantErr:   true,
		},
		{
			name:      "should NOT return error if createdBy has a value",
			createdBy: "Appian",
			wantErr:   false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			err := ValidateCreatedBy(test.createdBy)
			if test.wantErr {
				assert.NotEmpty(t, err)
			} else {
				assert.Empty(t, err)
			}
		})
	}
}

func TestValidateUpdatedBy(t *testing.T) {
	tests := []struct {
		name      string
		updatedBy string
		wantErr   bool
	}{
		{
			name:      "should return error if updatedBy is empty",
			updatedBy: "",
			wantErr:   true,
		},
		{
			name:      "should NOT return error if updatedBy has a value",
			updatedBy: "Appian",
			wantErr:   false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			err := ValidateUpdatedBy(test.updatedBy)
			if test.wantErr {
				assert.NotEmpty(t, err)
			} else {
				assert.Empty(t, err)
			}
		})
	}
}

func TestValidateStatus(t *testing.T) {
	tests := []struct {
		name    string
		status  api.ApplicationStatus_ApplicationStatus
		wantErr bool
	}{
		{
			name:    "should return error if status is empty",
			status:  "",
			wantErr: true,
		},
		{
			name:    "should return error if status is Not in map",
			status:  "XYZ",
			wantErr: true,
		},
		{
			name:    "should NOT return error if status is in map",
			status:  api.ApplicationStatus_ApplicationStatus_PENDING_ACCEPTANCE,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			err := ValidateStatus(test.status)
			if test.wantErr {
				assert.NotEmpty(t, err)
			} else {
				assert.Empty(t, err)
			}
		})
	}
}

func TestValidateStatusReason(t *testing.T) {
	tests := []struct {
		name         string
		statusReason api.ApplicationStatusReason
		wantErr      bool
	}{
		{
			name:         "should return error if statusReason is empty",
			statusReason: "",
			wantErr:      true,
		},
		{
			name:         "should return error if statusReason is Not in map",
			statusReason: "XYZ",
			wantErr:      true,
		},
		{
			name:         "should NOT return error if statusReason is in map",
			statusReason: api.ApplicationStatusReason_CDE_POST_BUREAU_APPROVED,
			wantErr:      false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			err := ValidateStatusReason(test.statusReason, countries.ID)
			if test.wantErr {
				assert.NotEmpty(t, err)
			} else {
				assert.Empty(t, err)
			}
		})
	}
}

func TestValidateApplicationID(t *testing.T) {
	tests := []struct {
		name          string
		applicationID string
		wantErr       bool
	}{
		{
			name:          "should return error if applicationID is empty",
			applicationID: "",
			wantErr:       true,
		},
		{
			name:          "should NOT return error if applicationID has a value",
			applicationID: "123",
			wantErr:       false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			err := ValidateApplicationID(test.applicationID)
			if test.wantErr {
				assert.NotEmpty(t, err)
			} else {
				assert.Empty(t, err)
			}
		})
	}
}

func TestValidateCountryCode(t *testing.T) {
	tests := []struct {
		name        string
		countryCode string
		wantErr     bool
	}{
		{
			name:        "should return error if countryCode is empty",
			countryCode: "",
			wantErr:     true,
		},
		{
			name:        "should return error if countryCode has a value but not in acceptable country list",
			countryCode: "Appian",
			wantErr:     true,
		},
		{
			name:        "should return error if countryCode has a value and in acceptable country list",
			countryCode: "SG",
			wantErr:     false,
		},
		{
			name:        "should return error if countryCode has a value and in acceptable country list",
			countryCode: "ID",
			wantErr:     false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(test.name, func(t *testing.T) {
			err := ValidateCountryCode(test.countryCode)
			if test.wantErr {
				assert.NotEmpty(t, err)
			} else {
				assert.Empty(t, err)
			}
		})
	}
}

func Test_ValidateApplicationID(t *testing.T) {
	testCases := []struct {
		Name          string
		ApplicationID string
		ExpectedError bool
	}{
		{Name: "Empty application Id", ApplicationID: "", ExpectedError: true},
		{Name: "Valid application id", ApplicationID: "abc123", ExpectedError: false},
		{Name: "Invalid application id", ApplicationID: "   ", ExpectedError: true},
	}

	for _, tt := range testCases {
		test := tt
		t.Run(test.Name, func(t *testing.T) {
			errs := ValidateApplicationID(test.ApplicationID)
			if test.ExpectedError && len(errs) == 0 {
				t.Errorf("Expected an error for ApplicationID: %s, but got none", test.ApplicationID)
			}
			if !test.ExpectedError && len(errs) > 0 {
				t.Errorf("Did not expect an error for ApplicationID: %s, but got errors: %v", test.ApplicationID, errs)
			}
		})
	}
}

func Test_ValidateProductCode(t *testing.T) {
	testCases := []struct {
		Name          string
		productType   api.ProductType
		ExpectedError bool
	}{
		{Name: "Empty productType", ExpectedError: true},
		{Name: "Valid productType", productType: api.ProductType_FLEXI_CREDIT_CARD, ExpectedError: false},
		{Name: "Invalid productType", productType: api.ProductType("Invalid"), ExpectedError: true},
	}

	for _, tt := range testCases {
		test := tt
		t.Run(test.Name, func(t *testing.T) {
			errs := ValidateProductCode(test.productType)
			if test.ExpectedError && len(errs) == 0 {
				t.Errorf("Expected an error for productType: %s, but got none", test.productType)
			}
			if !test.ExpectedError && len(errs) > 0 {
				t.Errorf("Did not expect an error for productType: %s, but got errors: %v", test.productType, errs)
			}
		})
	}
}

func Test_ValidateProductSubCode(t *testing.T) {
	testCases := []struct {
		Name           string
		subProductType api.SubProductType
		ExpectedError  bool
	}{
		{Name: "Empty subProductType", ExpectedError: true},
		{Name: "Valid subProductType", subProductType: api.SubProductType_DEFAULT_FLEXI_CREDIT_CARD, ExpectedError: false},
		{Name: "Invalid subProductType", subProductType: api.SubProductType("Invalid"), ExpectedError: true},
	}

	for _, tt := range testCases {
		test := tt
		t.Run(test.Name, func(t *testing.T) {
			errs := ValidateProductSubCode(test.subProductType)
			if test.ExpectedError && len(errs) == 0 {
				t.Errorf("Expected an error for subProductType: %s, but got none", test.subProductType)
			}
			if !test.ExpectedError && len(errs) > 0 {
				t.Errorf("Did not expect an error for subProductType: %s, but got errors: %v", test.subProductType, errs)
			}
		})
	}
}

func Test_ValidateApplicationTypeNew(t *testing.T) {
	testCases := []struct {
		Name               string
		applicationTypeNew api.ApplicationType
		ExpectedError      bool
	}{
		{Name: "Empty applicationType", ExpectedError: true},
		{Name: "Valid applicationType", applicationTypeNew: api.ApplicationType_NEW, ExpectedError: false},
		{Name: "Invalid applicationType", applicationTypeNew: api.ApplicationType("Invalid"), ExpectedError: true},
	}

	for _, tt := range testCases {
		test := tt
		t.Run(test.Name, func(t *testing.T) {
			errs := ValidateApplicationTypeNew(test.applicationTypeNew)
			if test.ExpectedError && len(errs) == 0 {
				t.Errorf("Expected an error for applicationType: %s, but got none", test.applicationTypeNew)
			}
			if !test.ExpectedError && len(errs) > 0 {
				t.Errorf("Did not expect an error for applicationType: %s, but got errors: %v", test.applicationTypeNew, errs)
			}
		})
	}
}

func Test_ValidateFlexiCardProducts(t *testing.T) {
	testCases := []struct {
		Name          string
		products      []api.Product
		ExpectedError bool
	}{
		{Name: "Empty FlexiCardProducts", ExpectedError: true},
		{Name: "Valid FlexiCardProducts", products: []api.Product{{
			ProductType:           api.ProductType_FLEXI_CREDIT_CARD,
			SubProductType:        api.SubProductType_DEFAULT_FLEXI_CREDIT_CARD,
			RequestedLoanAmount:   500,
			RequestedCurrencyCode: "SGD",
		}}, ExpectedError: false},
		{Name: "Invalid FlexiCardProducts", products: []api.Product{{ProductType: api.ProductType("Invalid")}}, ExpectedError: true},
	}

	for _, tt := range testCases {
		test := tt
		t.Run(test.Name, func(t *testing.T) {
			errs := ValidateFlexiCardProducts(test.products)
			if test.ExpectedError && len(errs) == 0 {
				t.Errorf("Expected an error for products: %v, but got none", test.products)
			}
			if !test.ExpectedError && len(errs) > 0 {
				t.Errorf("Did not expect an error for products: %v, but got errors: %v", test.products, errs)
			}
		})
	}
}

func Test_ValidateBundledProducts(t *testing.T) {
	testCases := []struct {
		Name          string
		products      []api.Product
		ExpectedError bool
	}{
		{Name: "Empty ValidateBundledProducts", ExpectedError: true},
		{
			Name: "Valid ValidateBundledProducts", products: []api.Product{{
				ProductType:           api.ProductType_FLEXI_CREDIT_CARD,
				SubProductType:        api.SubProductType_DEFAULT_FLEXI_CREDIT_CARD,
				RequestedLoanAmount:   500,
				RequestedCurrencyCode: "SGD",
				ProductPriority:       2,
			},
				{
					ProductType:           api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
					SubProductType:        api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
					RequestedLoanAmount:   500,
					RequestedCurrencyCode: "SGD",
					RequestedTenure:       12,
					InterestRate:          3,
					ProductPriority:       1,
				},
			}, ExpectedError: false,
		},
		{
			Name: "Missing card product", products: []api.Product{
				{
					ProductType:           api.ProductType_FLEXI_LOAN_LINE_OF_CREDIT,
					SubProductType:        api.SubProductType_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT,
					RequestedLoanAmount:   500,
					RequestedCurrencyCode: "SGD",
					RequestedTenure:       12,
					InterestRate:          3,
					ProductPriority:       1,
				},
			}, ExpectedError: true,
		},
		{
			Name: "Missing loan product", products: []api.Product{
				{
					ProductType:           api.ProductType_FLEXI_CREDIT_CARD,
					SubProductType:        api.SubProductType_DEFAULT_FLEXI_CREDIT_CARD,
					RequestedLoanAmount:   500,
					RequestedCurrencyCode: "SGD",
					ProductPriority:       2,
				},
			}, ExpectedError: true,
		},
		{Name: "Invalid ValidateBundledProducts", products: []api.Product{{ProductType: api.ProductType("Invalid")}}, ExpectedError: true},
	}

	for _, tt := range testCases {
		test := tt
		t.Run(test.Name, func(t *testing.T) {
			errs := ValidateBundledProducts(test.products)
			if test.ExpectedError && len(errs) == 0 {
				t.Errorf("Expected an error for products: %v, but got none", test.products)
			}
			if !test.ExpectedError && len(errs) > 0 {
				t.Errorf("Did not expect an error for products: %v, but got errors: %v", test.products, errs)
			}
		})
	}
}
