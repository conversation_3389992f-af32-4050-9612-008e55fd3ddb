package validation

import (
	"testing"

	crDecisionEng "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
)

func TestValidateCreditDecisionResponse(t *testing.T) {
	type args struct {
		creditDecisionResponse []api.CreditDecisionResponse
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "should not return error when CreditBureauResponse validation is success",
			args:    args{creditDecisionResponse: []api.CreditDecisionResponse{{Status: "SUCCESS", CallType: "PRE_BUREAU"}}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			if err := ValidateCreditDecisionResponse(test.args.creditDecisionResponse); (err != nil) != test.wantErr {
				t.Errorf("ValidateCreditDecisionResponse() = %v, want %v", err, test.wantErr)
			}
		})
	}
}

func TestValidateCallType(t *testing.T) {
	type args struct {
		callType api.CallType
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "should return error when CallType is Missing",
			args:    args{callType: ""},
			wantErr: true,
		},
		{
			name:    "should return error when CallType is Invalid",
			args:    args{callType: "Invalid CallType"},
			wantErr: true,
		},
		{
			name:    "should not return error when CallType is valid",
			args:    args{callType: "PRE_BUREAU"},
			wantErr: false,
		},
		{
			name:    "should not return error when CallType is valid",
			args:    args{callType: "POST_BUREAU"},
			wantErr: false,
		},

		{
			name:    "should not return error when CallType is valid",
			args:    args{callType: "FINAL_DECISION"},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			if err := ValidateCallType(test.args.callType); (err != nil) != test.wantErr {
				t.Errorf("ValidateCallType() = %v, want %v", err, test.wantErr)
			}
		})
	}
}

func Test_ValidateCreditDecisionEngResponse(t *testing.T) {
	type args struct {
		response *crDecisionEng.FICOCreditDecisionResponse
	}
	status := "SUCCESS"

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "should return error when response is Missing",
			args:    args{response: nil},
			wantErr: true,
		},
		{
			name:    "should return error when response.Message is Missing",
			args:    args{response: &crDecisionEng.FICOCreditDecisionResponse{Message: nil}},
			wantErr: true,
		},
		{
			name:    "should return error when response.Message.Status is Missing",
			args:    args{response: &crDecisionEng.FICOCreditDecisionResponse{Message: &crDecisionEng.Message{Status: nil}}},
			wantErr: true,
		},
		{
			name:    "should not return error when CallType is valid",
			args:    args{response: &crDecisionEng.FICOCreditDecisionResponse{Message: &crDecisionEng.Message{Status: &status}}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			if err := ValidateCreditDecisionEngResponse(test.args.response); (err != nil) != test.wantErr {
				t.Errorf("ValidateCreditDecisionEngResponse() = %v, want %v", err, test.wantErr)
			}
		})
	}
}

func Test_ValidateProductDecision(t *testing.T) {
	type args struct {
		application  *crDecisionEng.Application
		productTypes []crDecisionEng.ProductType
	}
	productTypeFlexiCreditCard := crDecisionEng.ProductType_FLEXI_CREDIT_CARD
	recommendedCreditDecision := "APPROVE"
	emptyRecommendedCreditDecision := ""
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "should return error when application is Missing",
			args:    args{application: nil},
			wantErr: true,
		},
		{
			name:    "should return error when application.Products is Missing",
			args:    args{application: &crDecisionEng.Application{Products: nil}},
			wantErr: true,
		},
		{
			name:    "should return error when application.Products is Missing",
			args:    args{application: &crDecisionEng.Application{Products: nil}},
			wantErr: true,
		},
		{
			name: "should return error when application.Products of applicationType is Missing",
			args: args{
				application: &crDecisionEng.Application{
					Products: []crDecisionEng.Product{},
				},
				productTypes: []crDecisionEng.ProductType{productTypeFlexiCreditCard},
			},
			wantErr: true,
		},
		{
			name: "should return error when application.Products.ProductDecision of ProductType is Missing",
			args: args{
				application: &crDecisionEng.Application{
					Products: []crDecisionEng.Product{{
						ProductType: &productTypeFlexiCreditCard,
					}},
				},
				productTypes: []crDecisionEng.ProductType{productTypeFlexiCreditCard},
			},
			wantErr: true,
		},
		{
			name: "should return error when application.Products.ProductDecision.RecommendedCreditDecision of ProductType is Missing",
			args: args{
				application: &crDecisionEng.Application{
					Products: []crDecisionEng.Product{{
						ProductType:     &productTypeFlexiCreditCard,
						ProductDecision: &crDecisionEng.ProductDecision{RecommendedCreditDecision: nil},
					}},
				},
				productTypes: []crDecisionEng.ProductType{productTypeFlexiCreditCard},
			},
			wantErr: true,
		},
		{
			name: "should return error when application.Products.ProductDecision.RecommendedCreditDecision of ProductType is Missing",
			args: args{
				application: &crDecisionEng.Application{
					Products: []crDecisionEng.Product{{
						ProductType:     &productTypeFlexiCreditCard,
						ProductDecision: &crDecisionEng.ProductDecision{RecommendedCreditDecision: &emptyRecommendedCreditDecision},
					}},
				},
				productTypes: []crDecisionEng.ProductType{productTypeFlexiCreditCard},
			},
			wantErr: true,
		},
		{
			name: "should not return error when inputs are valid",
			args: args{
				application: &crDecisionEng.Application{
					Products: []crDecisionEng.Product{{
						ProductType:     &productTypeFlexiCreditCard,
						ProductDecision: &crDecisionEng.ProductDecision{RecommendedCreditDecision: &recommendedCreditDecision},
					}},
				},
				productTypes: []crDecisionEng.ProductType{productTypeFlexiCreditCard},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			if err := ValidateProductDecision(test.args.application, test.args.productTypes); (err != nil) != test.wantErr {
				t.Errorf("ValidateCreditDecisionEngResponse() = %v, want %v", err, test.wantErr)
			}
		})
	}
}
