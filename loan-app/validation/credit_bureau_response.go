package validation

import (
	"strings"

	crDecisionEngAPI "gitlab.com/gx-regional/dakota/lending/cr-decision-eng/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// ValidateCreditBureauResponse ...
func ValidateCreditBureauResponse(creditBureauResponse *api.CreditBureauResponse) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if creditBureauResponse == nil {
		return append(errs, servus.ErrorDetail{ErrorCode: commonErr.ErrMissingCreditBureauResponse.Code, Message: commonErr.ErrMissingCreditBureauResponse.Message})
	}
	return append(errs, ValidateResponseStatus(creditBureauResponse.Status)...)
}

// ValidateResponseStatus ...
func ValidateResponseStatus(status string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(status) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: commonErr.ErrMissingStatus.Code, Message: commonErr.ErrMissingStatus.Message})
	}
	return errs
}

// ValidatePreBureauResponse ...
func ValidatePreBureauResponse(response *crDecisionEngAPI.FICOCreditDecisionResponse, productTypes []crDecisionEngAPI.ProductType) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	errs = append(errs, ValidateCreditDecisionEngResponse(response)...)
	if len(errs) != 0 {
		return errs
	}
	if *response.Message.Status != string(constants.CDEResponseStatusSuccess) {
		return append(errs, servus.ErrorDetail{ErrorCode: commonErr.ErrInvalidCrDecisionEngResponseStatus.Code, Message: commonErr.ErrInvalidCrDecisionEngResponseStatus.Message})
	}

	errs = append(errs, ValidateProductDecision(response.Message.Application, productTypes)...)
	return errs
}

// ValidatePostBureauResponse ...
func ValidatePostBureauResponse(response *crDecisionEngAPI.FICOCreditDecisionResponse, productTypes []crDecisionEngAPI.ProductType) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	errs = append(errs, ValidateCreditDecisionEngResponse(response)...)
	if len(errs) != 0 {
		return errs
	}
	if *response.Message.Status != string(constants.CDEResponseStatusSuccess) {
		return append(errs, servus.ErrorDetail{ErrorCode: commonErr.ErrInvalidCrDecisionEngResponseStatus.Code, Message: commonErr.ErrInvalidCrDecisionEngResponseStatus.Message})
	}
	errs = append(errs, ValidateProductDecision(response.Message.Application, productTypes)...)
	return errs
}
