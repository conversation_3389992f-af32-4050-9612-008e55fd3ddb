package validation

import (
	"strconv"
	"strings"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// ValidateIDApplicants ...
func ValidateIDApplicants(applicants []api.FlexiTermLoanApplicant) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if len(applicants) == 0 {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingApplicants.Code, Message: apiErr.ErrMissingApplicants.Message})
	}
	for _, applicant := range applicants {
		// ValidateApplicantType ...
		errs = append(errs, ValidateApplicantType(applicant.ApplicantType)...)
		// ValidateGender ...
		errs = append(errs, ValidateGender(applicant.Gender)...)
		// ValidateMaritalStatus ...
		errs = append(errs, ValidateMaritalStatus(applicant.MaritalStatus)...)
		// ValidateContactType ...
		errs = append(errs, ValidateContactType(applicant.ContactType)...)
		// ValidateAddressDetails ...
		errs = append(errs, ValidateAddressDetails(applicant.AddressDetails)...)
		// ValidateApplicantID ...
		errs = append(errs, ValidateApplicantID(applicant.ApplicantID)...)
		// ValidatePrimaryID ...
		errs = append(errs, ValidatePrimaryID(applicant.PrimaryID)...)
		// ValidateFullName ...
		errs = append(errs, ValidateFullName(applicant.FullName)...)
		// ValidateContactNumber ...
		errs = append(errs, ValidateContactNumber(applicant.ContactNumber)...)
		// ValidateDateOfBirth ...
		errs = append(errs, ValidateDateOfBirth(applicant.DateOfBirth)...)
		// ValidateFundsPurposeOfUse ...
		errs = append(errs, ValidateFundsPurposeOfUse(applicant.FundsPurposeOfUse)...)
		// ValidatePrimaryFundSource ...
		errs = append(errs, ValidatePrimaryFundSource(applicant.PrimaryFundSource)...)
		// ValidateEmploymentDetails ...
		errs = append(errs, ValidateEmploymentDetails(applicant.EmploymentDetail)...)
		// ValidateMotherMaidenName ...
		errs = append(errs, ValidateMotherMaidenName(applicant.MotherMaidenName)...)
		// ValidateEducation ...
		errs = append(errs, ValidateEducation(applicant.Education)...)
		// ValidateWorkAddressLine ...
		errs = append(errs, ValidateWorkAddressLine(applicant.WorkAddressLine)...)
		// ValidateEmergencyContactName ...
		errs = append(errs, ValidateEmergencyContactName(applicant.EmergencyContactName)...)
		// ValidateEmergencyContactMobileNumber ...
		errs = append(errs, ValidateEmergencyContactMobileNumber(applicant.EmergencyContactMobileNumber)...)
		// ValidateEmergencyContactRelationship ...
		errs = append(errs, ValidateEmergencyContactRelationship(applicant.EmergencyContactRelationship)...)
		// ValidateConsent
		errs = append(errs, ValidateConsent(applicant.Consent)...)
		// ValidateApplicantSafeID
		errs = append(errs, ValidateApplicantSafeID(applicant.SafeID)...)
	}
	return errs
}

// ValidateConsent ...
func ValidateConsent(consent *api.Consent) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if consent == nil {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingConsentValue.Code, Message: apiErr.ErrMissingConsentValue.Message})
	}
	if strings.TrimSpace(consent.Id) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingConsentValue.Code, Message: apiErr.ErrMissingConsentValue.Message})
	}
	return errs
}

// ValidateAddressDetails ...
//
//nolint:gocognit
func ValidateAddressDetails(addressDetails []api.AddressDetails) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if addressDetails == nil {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingAddressLine.Code, Message: apiErr.ErrMissingAddressLine.Message})
		return errs
	}
	for _, address := range addressDetails {
		errs = append(errs, ValidateAddressType(address.AddressType)...)
		if strings.TrimSpace(address.AddressLine) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingAddressLine.Code, Message: apiErr.ErrMissingAddressLine.Message})
		}
		RT, errRW := strconv.Atoi(address.RT)
		if errRW != nil || RT < 0 {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidRT.Code, Message: apiErr.ErrInvalidRT.Message})
		}
		RW, errRW := strconv.Atoi(address.RW)
		if errRW != nil || RW < 0 {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidRW.Code, Message: apiErr.ErrInvalidRW.Message})
		}
		if strings.TrimSpace(address.Village) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingVillage.Code, Message: apiErr.ErrMissingVillage.Message})
		}
		if strings.TrimSpace(address.District) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingDistrict.Code, Message: apiErr.ErrMissingDistrict.Message})
		}
		if strings.TrimSpace(address.City) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingCity.Code, Message: apiErr.ErrMissingCity.Message})
		}
		if strings.TrimSpace(address.Province) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingProvince.Code, Message: apiErr.ErrMissingProvince.Message})
		}
		if strings.TrimSpace(address.PostalCode) == "" {
			errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingPostalCode.Code, Message: apiErr.ErrMissingPostalCode.Message})
		}
	}
	return errs
}

// ValidateApplicantID ...
func ValidateApplicantID(applicantID string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(applicantID) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingApplicantID.Code, Message: apiErr.ErrMissingApplicantID.Message})
	}
	return errs
}

// ValidatePrimaryID ...
func ValidatePrimaryID(primaryID *api.IDDetails) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if primaryID == nil {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingId.Code, Message: apiErr.ErrMissingId.Message})
	}
	errs = append(errs, ValidateIDType(primaryID.Type)...)
	if strings.TrimSpace(primaryID.Number) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingIDNumber.Code, Message: apiErr.ErrMissingIDNumber.Message})
	}
	return errs
}

// ValidateFullName ...
func ValidateFullName(fullName string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(fullName) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingFullName.Code, Message: apiErr.ErrMissingFullName.Message})
	}
	return errs
}

// ValidateContactNumber ...
func ValidateContactNumber(contactNumber string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(contactNumber) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingContactNumber.Code, Message: apiErr.ErrMissingContactNumber.Message})
	}
	return errs
}

// ValidateDateOfBirth ...
func ValidateDateOfBirth(dateOfBirth string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(dateOfBirth) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingDateOfBirth.Code, Message: apiErr.ErrMissingDateOfBirth.Message})
	}
	return errs
}

// ValidateFundsPurposeOfUse ...
func ValidateFundsPurposeOfUse(fundsPurposeOfUse string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(fundsPurposeOfUse) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidFundsPurposeOfUse.Code, Message: apiErr.ErrInvalidFundsPurposeOfUse.Message})
	}
	return errs
}

// ValidatePrimaryFundSource ...
func ValidatePrimaryFundSource(primaryFundSource string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(primaryFundSource) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidPrimaryFundSource.Code, Message: apiErr.ErrInvalidPrimaryFundSource.Message})
	}
	return errs
}

// ValidateEmploymentDetails ...
func ValidateEmploymentDetails(employmentDetail *api.EmploymentDetail) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if employmentDetail == nil {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidLengthOfEmployment.Code, Message: apiErr.ErrInvalidLengthOfEmployment.Message})
	}
	errs = append(errs, ValidateOccupationCode(employmentDetail.OccupationCode)...)
	errs = append(errs, ValidatePosition(employmentDetail.Position)...)
	errs = append(errs, ValidateIncomePerMonth(employmentDetail.IncomePerMonth)...)
	errs = append(errs, ValidateEmployerName(employmentDetail.EmployerName)...)
	errs = append(errs, ValidateIndustrySector(employmentDetail.IndustrySector)...)
	return errs
}

// ValidateOccupationCode ...
func ValidateOccupationCode(occupationCode string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(occupationCode) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingOccupationCode.Code, Message: apiErr.ErrMissingOccupationCode.Message})
	}
	return errs
}

// ValidatePosition ...
func ValidatePosition(position string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(position) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingPosition.Code, Message: apiErr.ErrMissingPosition.Message})
	}
	return errs
}

// ValidateIncomePerMonth ...
func ValidateIncomePerMonth(incomePerMonth string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if incomePerMonth == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidIncomePerMonth.Code, Message: apiErr.ErrInvalidIncomePerMonth.Message})
	}
	return errs
}

// ValidateEmployerName ...
func ValidateEmployerName(employerName string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(employerName) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingEmployerName.Code, Message: apiErr.ErrMissingEmployerName.Message})
	}
	return errs
}

// ValidateIndustrySector ...
func ValidateIndustrySector(industrySector string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(industrySector) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingIndustrySector.Code, Message: apiErr.ErrMissingIndustrySector.Message})
	}
	return errs
}

// ValidateMotherMaidenName ...
func ValidateMotherMaidenName(motherMaidenName string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(motherMaidenName) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingMotherMaidenName.Code, Message: apiErr.ErrMissingMotherMaidenName.Message})
	}
	return errs
}

// ValidateEducation ...
func ValidateEducation(education int64) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if education == 0 {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidEducation.Code, Message: apiErr.ErrInvalidEducation.Message})
	}
	return errs
}

// ValidateWorkAddressLine ...
func ValidateWorkAddressLine(workAddressLine string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(workAddressLine) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingWorkAddressLine.Code, Message: apiErr.ErrMissingWorkAddressLine.Message})
	}
	return errs
}

// ValidateEmergencyContactName ...
func ValidateEmergencyContactName(emergencyContactName string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(emergencyContactName) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingEmergencyContactName.Code, Message: apiErr.ErrMissingEmergencyContactName.Message})
	}
	return errs
}

// ValidateEmergencyContactMobileNumber ...
func ValidateEmergencyContactMobileNumber(emergencyContactMobileNumber string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(emergencyContactMobileNumber) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingEmergencyContactMobileNumber.Code, Message: apiErr.ErrMissingEmergencyContactMobileNumber.Message})
	}
	return errs
}

// ValidateEmergencyContactRelationship ...
func ValidateEmergencyContactRelationship(emergencyContactRelationship string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(emergencyContactRelationship) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingEmergencyContactRelationship.Code, Message: apiErr.ErrMissingEmergencyContactRelationship.Message})
	}
	return errs
}
