package validation

import (
	"context"
	"reflect"
	"testing"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestValidateApplicantDBMY(t *testing.T) {
	type args struct {
		ctx        context.Context
		applicants []api.FlexiTermLoanApplicant
	}

	tests := []struct {
		name    string
		args    args
		ErrList []servus.ErrorDetail
	}{
		{
			name: "happy path - all validation should pass",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFEID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   api.IDType_NRIC,
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{},
		},
		{
			name: "should return error when safe id is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   api.IDType_NRIC,
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrMissingSafeID.Code,
					Message:   apiErr.ErrMissingSafeID.Message,
				},
			},
		},
		{
			name: "should return error when applicant id is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   api.IDType_NRIC,
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrMissingApplicantID.Code,
					Message:   apiErr.ErrMissingApplicantID.Message,
				},
			},
		},
		{
			name: "should return error when contact type is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "",
						ContactNumber:     "0100000000",
						FullName:          "FULL NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   api.IDType_NRIC,
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrMissingContactType.Code,
					Message:   apiErr.ErrMissingContactType.Message,
				},
			},
		},
		{
			name: "should return error when contact number is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "",
						FullName:          "FULL NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   api.IDType_NRIC,
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrMissingContactNumber.Code,
					Message:   apiErr.ErrMissingContactNumber.Message,
				},
			},
		},
		{
			name: "should return error when full name is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   api.IDType_NRIC,
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrMissingFullName.Code,
					Message:   apiErr.ErrMissingFullName.Message,
				},
			},
		},
		{
			name: "should return error when date of birth is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL_NAME",
						DateOfBirth:       "",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   api.IDType_NRIC,
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrMissingDateOfBirth.Code,
					Message:   apiErr.ErrMissingDateOfBirth.Message,
				},
			},
		},
		{
			name: "should return error when nationality is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL_NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   api.IDType_NRIC,
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrMissingNationality.Code,
					Message:   apiErr.ErrMissingNationality.Message,
				},
			},
		},
		{
			name: "should return error when funds purpose of use is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL_NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   api.IDType_NRIC,
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrInvalidFundsPurposeOfUse.Code,
					Message:   apiErr.ErrInvalidFundsPurposeOfUse.Message,
				},
			},
		},
		{
			name: "should return error when primary fund source is invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL_NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "",
						PrimaryID: &api.IDDetails{
							Type:   api.IDType_NRIC,
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrInvalidPrimaryFundSource.Code,
					Message:   apiErr.ErrInvalidPrimaryFundSource.Message,
				},
			},
		},
		{
			name: "should return error when primary id is missing or invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL_NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   "NOT NRIC",
							Number: "",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrInvalidIDType.Code,
					Message:   apiErr.ErrInvalidIDType.Message,
				},
				{
					ErrorCode: apiErr.ErrMissingIDNumber.Code,
					Message:   apiErr.ErrMissingIDNumber.Message,
				},
			},
		},
		{
			name: "should return error when gender is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL_NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   "NRIC",
							Number: "000000-00-0000",
						},
						Gender:        "",
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrMissingGender.Code,
					Message:   apiErr.ErrMissingGender.Message,
				},
			},
		},
		{
			name: "should return error when marital status is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL_NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   "NRIC",
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: "",
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrMissingMaritalStatus.Code,
					Message:   apiErr.ErrMissingMaritalStatus.Message,
				},
			},
		},
		{
			name: "should return error when employment detail is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL_NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   "NRIC",
							Number: "000000-00-0000",
						},
						Gender:           api.Gender_UNKNOWN,
						MaritalStatus:    api.MaritalStatus_SINGLE,
						EmploymentDetail: nil,
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrMissingEmploymentInfo.Code,
					Message:   apiErr.ErrMissingEmploymentInfo.Message,
				},
			},
		},
		{
			name: "should return error when employment detail is invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL_NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   "MYKAD",
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   "",
							IncomePerMonth:   "",
							WorkEmailAddress: "",
							IndustrySector:   "",
							OccupationCode:   "",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrMissingEmploymentType.Code,
					Message:   apiErr.ErrMissingEmploymentType.Message,
				},
				{
					ErrorCode: apiErr.ErrInvalidIncomePerMonth.Code,
					Message:   apiErr.ErrInvalidIncomePerMonth.Message,
				},
				{
					ErrorCode: apiErr.ErrMissingOccupationCode.Code,
					Message:   apiErr.ErrMissingOccupationCode.Message,
				},
			},
		},
		{
			name: "should return error address details is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL_NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   "NRIC",
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrMissingAddressInfo.Code,
					Message:   apiErr.ErrMissingAddressInfo.Message,
				},
			},
		},
		{
			name: "should return error address details is invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL_NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   "NRIC",
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "",
								AddressLine: "",
								Province:    "",
								City:        "",
								PostalCode:  "",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENT_ID",
							Timestamp: "TIMESTAMP",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrMissingAddressType.Code,
					Message:   apiErr.ErrMissingAddressType.Message,
				},
				{
					ErrorCode: apiErr.ErrMissingAddressLine.Code,
					Message:   apiErr.ErrMissingAddressLine.Message,
				},
				{
					ErrorCode: apiErr.ErrMissingProvince.Code,
					Message:   apiErr.ErrMissingProvince.Message,
				},
				{
					ErrorCode: apiErr.ErrMissingPostalCode.Code,
					Message:   apiErr.ErrMissingPostalCode.Message,
				},
			},
		},
		{
			name: "should return error when consent is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL_NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   "NRIC",
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id: "",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "0",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrMissingConsentValue.Code,
					Message:   apiErr.ErrMissingConsentValue.Message,
				},
			},
		},
		{
			name: "should return error when non fi commitment is missing",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL_NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   "NRIC",
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENTID",
							Timestamp: "Timestampt",
						},
						AdditionalData: []api.AdditionalData{},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrMissingNonFiCommitment.Code,
					Message:   apiErr.ErrMissingNonFiCommitment.Message,
				},
			},
		},
		{
			name: "should return error when non fi commitment is invalid",
			args: args{
				ctx: context.Background(),
				applicants: []api.FlexiTermLoanApplicant{
					{
						SafeID:            "SAFE_ID",
						ApplicantID:       "APPLICANTID",
						ContactType:       "HANDPHONE",
						ContactNumber:     "0100000000",
						FullName:          "FULL_NAME",
						DateOfBirth:       "01-01-2001",
						Nationality:       "MALAYSIAN",
						FundsPurposeOfUse: "saving",
						PrimaryFundSource: "salary man",
						PrimaryID: &api.IDDetails{
							Type:   "NRIC",
							Number: "000000-00-0000",
						},
						Gender:        api.Gender_UNKNOWN,
						MaritalStatus: api.MaritalStatus_SINGLE,
						EmploymentDetail: &api.EmploymentDetail{
							EmploymentType:   api.EmploymentType_SA,
							IncomePerMonth:   "10000",
							WorkEmailAddress: "<EMAIL>",
							IndustrySector:   "finance",
							OccupationCode:   "FIN",
						},
						AddressDetails: []api.AddressDetails{
							{
								AddressType: "REGISTERED",
								AddressLine: "NO 9 Jalan Sultan Ismail",
								Province:    "Selangor",
								City:        "Cheras",
								PostalCode:  "43000",
							},
						},
						Consent: &api.Consent{
							Id:        "CONSENTID",
							Timestamp: "Timestampt",
						},
						AdditionalData: []api.AdditionalData{
							{
								Key:   "NON_FI_COMMITMENT",
								Value: "",
							},
						},
					},
				},
			},
			ErrList: []servus.ErrorDetail{
				{
					ErrorCode: apiErr.ErrInvalidNonFiCommitment.Code,
					Message:   apiErr.ErrInvalidNonFiCommitment.Message,
				},
			},
		},
	}

	for _, tc := range tests {
		data := tc
		t.Run(data.name, func(t *testing.T) {
			errList := ValidateDBMYApplicants(data.args.applicants)

			// Check error length against test error length,
			if len(errList) != 0 && len(data.ErrList) != 0 {
				if isMatch := reflect.DeepEqual(errList, data.ErrList); !isMatch {
					t.Errorf("ValidateApplicants() = %v, want %v", errList, data.ErrList)
				}
				t.Log(errList)
			}
		})
	}
}
