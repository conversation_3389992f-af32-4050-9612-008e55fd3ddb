// Package validation ...
package validation

import (
	"strings"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/mapper"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// ValidateSGApplicants ...
func ValidateSGApplicants(applicants []api.FlexiTermLoanApplicant) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if len(applicants) == 0 {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingApplicants.Code, Message: apiErr.ErrMissingApplicants.Message})
	}
	for _, applicant := range applicants {
		//ValidateApplicantType ...
		errs = append(errs, ValidateApplicantType(applicant.ApplicantType)...)
		//ValidateIDType ...
		errs = append(errs, ValidateIDType(applicant.IDType)...)
		//ValidateGender ...
		errs = append(errs, ValidateGender(applicant.Gender)...)
		//ValidateIDNumber...
		errs = append(errs, ValidateIDNumber(applicant.IDNumber)...)
		//ValidateMaritalStatus ...
		errs = append(errs, ValidateMaritalStatus(applicant.MaritalStatus)...)
		//ValidateResidentialStatus ...
		errs = append(errs, ValidateResidentialStatus(applicant.ResidentialStatus)...)
		//ValidateContactType ...
		errs = append(errs, ValidateContactType(applicant.ContactType)...)
		//ValidateAddressType ...
		errs = append(errs, ValidateAddressType(applicant.AddressType)...)
		//ValidateEmploymentDetail ...
		if applicant.EmploymentDetail != nil {
			errs = append(errs, ValidateEmploymentDetail(applicant.EmploymentDetail)...)
		}
		//ValidateIncomeDocuments ...
		if applicant.Income != nil {
			errs = append(errs, ValidateIncomeDocuments(applicant.Income.IncomeDocuments)...)
		}
		//ValidateWhitelistFlag ...
		errs = append(errs, ValidateWhitelistFlag(applicant.WhitelistFlag)...)
		// ValidateRace ...
		errs = append(errs, ValidateRace(applicant.Race)...)
		// ValidateStreetName ...
		errs = append(errs, ValidateStreetName(applicant.StreetName)...)
		// ValidatePostalCode ...
		errs = append(errs, ValidatePostalCode(applicant.PostalCode)...)
		// ValidateState ...
		errs = append(errs, ValidateState(applicant.State)...)
	}
	return errs
}

// ValidateIncomeDocumentType ...
func ValidateIncomeDocumentType(incomeDocumentType api.IncomeDocumentType) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(incomeDocumentType)) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingIncomeDocumentType.Code, Message: apiErr.ErrMissingIncomeDocumentType.Message})
	}
	if _, ok := mapper.IncomeDocumentType[incomeDocumentType]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidIncomeDocumentType.Code, Message: apiErr.ErrInvalidIncomeDocumentType.Message})
	}
	return errs
}

// ValidateIncomeDocuments ...
func ValidateIncomeDocuments(incomeDocuments []api.IncomeDocument) []servus.ErrorDetail {
	if incomeDocuments == nil {
		return nil
	}
	var errs []servus.ErrorDetail
	for _, incomeDocument := range incomeDocuments {
		// ValidateIncomeDocumentType ...
		errs = append(errs, ValidateIncomeDocumentType(incomeDocument.IncomeDocumentType)...)
		//errs = append(errs, ValidateIncomeAmount(incomeDocument.IncomeComponents)...)
	}
	return errs
}

// ValidateResidentialStatus ...
func ValidateResidentialStatus(residentialStatus api.ResidentialStatus) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(residentialStatus)) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingResidentialStatus.Code, Message: apiErr.ErrMissingResidentialStatus.Message})
	}
	if _, ok := mapper.ResidentialStatus[residentialStatus]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidResidentialStatus.Code, Message: apiErr.ErrInvalidResidentialStatus.Message})
	}
	return errs
}

// ValidateEmploymentDetail ...
func ValidateEmploymentDetail(employmentDetail *api.EmploymentDetail) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(string(employmentDetail.EmploymentType)) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingResidentialStatus.Code, Message: apiErr.ErrMissingResidentialStatus.Message})
	}
	if _, ok := mapper.EmploymentType[employmentDetail.EmploymentType]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrInvalidEmploymentType.Code, Message: apiErr.ErrInvalidEmploymentType.Message})
	}
	return errs
}

// ValidateRace ...
func ValidateRace(race string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(race) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingRace.Code, Message: apiErr.ErrMissingRace.Message})
	}
	return errs
}

// ValidateStreetName ...
func ValidateStreetName(streetName string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(streetName) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingStreetName.Code, Message: apiErr.ErrMissingStreetName.Message})
	}
	return errs
}

// ValidatePostalCode ...
func ValidatePostalCode(postalCode string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(postalCode) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingPostalCode.Code, Message: apiErr.ErrMissingPostalCode.Message})
	}
	return errs
}

// ValidateState ...
func ValidateState(state string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if strings.TrimSpace(state) == "" {
		return append(errs, servus.ErrorDetail{ErrorCode: apiErr.ErrMissingState.Code, Message: apiErr.ErrMissingState.Message})
	}
	return errs
}
