package validation

import (
	"testing"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
)

func TestValidateEcosystemResponse(t *testing.T) {
	type args struct {
		ecosystemResponse []api.EcosystemResponse
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "should return error when EcosystemResponse validation fails",
			args:    args{ecosystemResponse: []api.EcosystemResponse{{Status: "SUCCESS", Partner: "Invalid"}}},
			wantErr: true,
		},
		{
			name:    "should not return error when EcosystemResponse validation is success",
			args:    args{ecosystemResponse: []api.EcosystemResponse{{Status: "SUCCESS", Partner: "GRAB"}}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			if err := ValidateEcosystemResponse(test.args.ecosystemResponse); (err != nil) != test.wantErr {
				t.<PERSON>("ValidateEcosystemResponse() = %v, want %v", err, test.wantErr)
			}
		})
	}
}

func TestValidatePartner(t *testing.T) {
	type args struct {
		partner string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "should return error when Partner is Missing",
			args:    args{partner: ""},
			wantErr: true,
		},
		{
			name:    "should return error when Partner is Invalid",
			args:    args{partner: "Invalid Partner"},
			wantErr: true,
		},
		{
			name:    "should not return error when Partner is valid",
			args:    args{partner: "GRAB"},
			wantErr: false,
		},
		{
			name:    "should not return error when Partner is valid",
			args:    args{partner: "MSTS"},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			if err := ValidatePartner(test.args.partner); (err != nil) != test.wantErr {
				t.Errorf("ValidatePartner() = %v, want %v", err, test.wantErr)
			}
		})
	}
}
