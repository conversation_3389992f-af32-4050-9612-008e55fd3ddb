package validation

import (
	"encoding/base64"
	"net/http"
	"strconv"
	"strings"

	"golang.org/x/exp/slices"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.com/gx-regional/dakota/lending/loan-app/constants"
	"gitlab.com/gx-regional/dakota/lending/loan-app/mapper"
	"gitlab.com/gx-regional/dakota/lending/loan-app/server/config"
	"gitlab.myteksi.net/dakota/lending/common/constant"
	"gitlab.myteksi.net/dakota/lending/common/countries"
	commonErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// ValidateInitDocumentUploadRequest ...
func ValidateInitDocumentUploadRequest(req *api.InitDocumentUploadRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	errs = append(errs, ValidateReferenceID(req.ReferenceID)...)
	errs = append(errs, ValidateOnboardingApplicationID(req.OnboardingApplicationID)...)
	errs = append(errs, ValidateProductSubCode(api.SubProductType(req.ProductVariantCode))...)
	errs = append(errs, ValidateFileType(req.FileType)...)
	errs = append(errs, ValidateSubFileType(req.FileType, req.SubFileType)...)
	errs = append(errs, ValidateProductVariantCode(req.ProductVariantCode)...)

	return errs
}

func ValidateProductVariantCode(productVariantCode string) []servus.ErrorDetail {
	// for backward compatibility
	if productVariantCode == "" {
		return nil
	}
	allowedProductVariantCodes := []string{constant.BizLineOfCreditProductVariantCode}
	if constants.DefaultCountryCode == countries.MY {
		allowedProductVariantCodes = append(allowedProductVariantCodes, constant.FlexiLineOfCreditProductVariantCode)
		allowedProductVariantCodes = append(allowedProductVariantCodes, constant.BizOnboardingProductVariantCode)
	}

	if !slices.Contains(allowedProductVariantCodes, productVariantCode) {
		return []servus.ErrorDetail{{ErrorCode: commonErr.ErrInvalidProductVariantCode.Code, Message: commonErr.ErrInvalidProductVariantCode.Message}}
	}
	return nil
}

// ValidateInitEpfLoginRequest ...
func ValidateInitEpfLoginRequest(req *api.InitEpfLoginRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	errs = append(errs, ValidateReferenceID(req.ReferenceID)...)
	errs = append(errs, ValidateOnboardingApplicationID(req.OnboardingApplicationID)...)
	errs = append(errs, ValidateProductSubCode(api.SubProductType(req.ProductVariantCode))...)

	return errs
}

// ValidateConfirmEpfLoginRequest ...
func ValidateConfirmEpfLoginRequest(req *api.ConfirmEpfLoginRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	errs = append(errs, ValidateReferenceID(req.ReferenceID)...)
	errs = append(errs, ValidateOnboardingApplicationID(req.OnboardingApplicationID)...)
	errs = append(errs, ValidateClientTxnID(req.ClientTxnID)...)
	errs = append(errs, ValidateConfirmStatus(req.Status)...)

	return errs
}

// ValidateConfirmDocumentUploadRequest ...
func ValidateConfirmDocumentUploadRequest(req *api.ConfirmDocumentUploadRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	errs = append(errs, ValidateReferenceID(req.ReferenceID)...)
	errs = append(errs, ValidateOnboardingApplicationID(req.OnboardingApplicationID)...)

	return errs
}

// ValidateDocumentUploadRequest ...
func ValidateDocumentUploadRequest(req *api.DocumentUploadRequest, fileTypeConfig config.FileTypeConfig) error {
	var errs []servus.ErrorDetail

	errs = append(errs, ValidateReferenceID(req.ReferenceID)...)
	errs = append(errs, ValidateInitTxnReferenceID(req.InitTxnReferenceID)...)
	errs = append(errs, ValidateOnboardingApplicationID(req.OnboardingApplicationID)...)
	errs = append(errs, ValidateFileID(req.FileID)...)
	errs = append(errs, ValidateFileBase64String(req.FileBase64String)...)

	if errs != nil {
		return commonErr.BuildErrorResponseWithErrorDetail(
			http.StatusBadRequest, commonErr.ErrInvalidDocumentUploadRequest.Code,
			commonErr.ErrInvalidDocumentUploadRequest.Message, errs)
	}

	if err := ValidateDocument(req, fileTypeConfig); err != nil {
		return err
	}

	return nil
}

// ValidateDocument ...
func ValidateDocument(req *api.DocumentUploadRequest, fileTypeConfig config.FileTypeConfig) error {
	fileExtension := string(getFileExtensionFromFileByte(req.FileBase64String))
	isFileExtensionValid := false

	for _, ext := range fileTypeConfig.AllowedExtensions {
		ext = strings.ToUpper(strings.TrimSpace(ext))
		if signature, ok := mapper.FileTypeSignature[ext]; ok {
			if ext == fileExtension && strings.Contains(req.FileBase64String, signature) {
				isFileExtensionValid = true
				break
			}
		}
	}

	var err servus.ServiceError

	if !isFileExtensionValid {
		err = commonErr.BuildErrorResponse(http.StatusBadRequest, commonErr.ErrUnsupportedFileType.Code, commonErr.ErrUnsupportedFileType.Message)
		return err
	}

	req.FileExtension = fileExtension

	fileByte, decodeErr := base64.StdEncoding.DecodeString(req.FileBase64String)
	if decodeErr != nil {
		err = commonErr.BuildErrorResponse(http.StatusBadRequest, commonErr.ErrInvalidFileByte.Code, commonErr.ErrInvalidFileByte.Message)
		return err
	}

	// todo: change the MaxFileSizeInBytes data type to int
	maxFileSizeInBytes, _ := strconv.Atoi(fileTypeConfig.MaxFileSizeInBytes)
	fileContentSize := len(fileByte)

	if fileContentSize == 0 || fileContentSize > maxFileSizeInBytes {
		err = commonErr.BuildErrorResponse(http.StatusBadRequest, commonErr.ErrInvalidFileSize.Code, commonErr.ErrInvalidFileSize.Message)
		return err
	}

	return nil
}

// ValidateFileBase64String ...
func ValidateFileBase64String(fileByte string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if err := validateEmptyString(fileByte, "fileByte", commonErr.ErrMissingFileByte); err != nil {
		return append(errs, *err)
	}

	return errs
}

// ValidateFileID ...
func ValidateFileID(fileID string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if err := validateEmptyString(fileID, "fileID", commonErr.ErrMissingFileID); err != nil {
		return append(errs, *err)
	}

	return errs
}

// ValidateInitTxnReferenceID ...
func ValidateInitTxnReferenceID(initTxnReferenceID string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if err := validateEmptyString(initTxnReferenceID, "initTxnReferenceID", commonErr.ErrMissingInitTxnReferenceID); err != nil {
		return append(errs, *err)
	}

	return errs
}

// ValidateClientTxnID ...
func ValidateClientTxnID(clientTxnID string) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if err := validateEmptyString(clientTxnID, "clientTxnID", commonErr.ErrMissingClientTxnID); err != nil {
		return append(errs, *err)
	}

	return errs
}

// ValidateConfirmStatus ...
func ValidateConfirmStatus(status *bool) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if status == nil {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: commonErr.ErrMissingEpfLoginStatus.Code,
			Message:   commonErr.ErrMissingEpfLoginStatus.Message,
			Path:      "status",
		})
	}
	return errs
}

// ValidateFileType ...
func ValidateFileType(fileType api.FileType) []servus.ErrorDetail {
	return validateEmptyAndInvalidField(fileType, "fileType", commonErr.ErrInvalidFileType, mapper.FileType)
}

// ValidateSubFileType ...
func ValidateSubFileType(fileType api.FileType, subFileType api.SubFileType) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if !constants.EnablePersonalBankStatementsUpload && subFileType == api.SubFileType_PERSONAL_BANK_STATEMENT {
		errs = append(errs, servus.ErrorDetail{ErrorCode: commonErr.ErrInvalidSubFileType.Code, Message: commonErr.ErrInvalidSubFileType.Message, Path: "subFileType"})
		return errs
	}
	if strings.TrimSpace(string(subFileType)) != "" {
		switch fileType {
		case api.FileType_STATEMENT:
			return validateEmptyAndInvalidField(subFileType, "subFileType", commonErr.ErrInvalidSubFileType, mapper.BankStatementSubFileType)
		default:
			errs = append(errs, servus.ErrorDetail{ErrorCode: commonErr.ErrInvalidSubFileType.Code, Message: commonErr.ErrInvalidSubFileType.Message})
		}
	}
	return errs
}

func validateEmptyAndInvalidField(field interface{}, path string, invalidErr commonErr.CustomError, mapper map[interface{}]bool) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if _, ok := mapper[field]; !ok {
		errs = append(errs, servus.ErrorDetail{ErrorCode: invalidErr.Code, Message: invalidErr.Message, Path: path})
	}
	return errs
}

func getFileExtensionFromFileByte(fileByte string) constants.FileExtension {
	isPDF := strings.Contains(fileByte, constants.PDFSignature)
	if isPDF {
		return constants.PDF
	}

	isPNG := strings.Contains(fileByte, constants.PNGSignature)
	if isPNG {
		return constants.PNG
	}

	isJPEG := strings.Contains(fileByte, constants.JPEGSignature)
	if isJPEG {
		return constants.JPEG
	}

	isWebp := strings.Contains(fileByte, constants.WEBPSignature)
	if isWebp {
		return constants.WEBP
	}

	return constants.UndefinedFileType
}
