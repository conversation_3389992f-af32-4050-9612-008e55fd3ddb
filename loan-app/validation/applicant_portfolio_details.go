package validation

import (
	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// ValidateApplicantPortfolioDetails ...
func ValidateApplicantPortfolioDetails(applicantPortfolioDetails *api.ApplicantPortfolioDetails) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	errs = append(errs, ValidateResponseStatus(applicantPortfolioDetails.Status)...)
	return errs
}
