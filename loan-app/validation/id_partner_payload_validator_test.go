package validation

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
	apiErr "gitlab.myteksi.net/dakota/lending/common/errors"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestValidateExtraDeviceInfo(t *testing.T) {
	tests := []struct {
		name         string
		deviceInfo   *api.DeviceInfo
		expectedErrs []servus.ErrorDetail
	}{
		{
			name: "All fields valid",
			deviceInfo: &api.DeviceInfo{
				AdjustId:       "validAdjustId",
				Imei:           "validImei",
				AppPackageName: "validAppPackageName",
			},
			expectedErrs: nil,
		},
		{
			name: "Missing AdjustId",
			deviceInfo: &api.DeviceInfo{
				AdjustId:       "",
				Imei:           "validImei",
				AppPackageName: "validAppPackageName",
			},
			expectedErrs: []servus.ErrorDetail{
				{ErrorCode: apiErr.ErrMissingAdjustID.Code, Message: apiErr.ErrMissingAdjustID.Message},
			},
		},
		{
			name: "Missing Imei",
			deviceInfo: &api.DeviceInfo{
				AdjustId:       "validAdjustId",
				Imei:           "",
				AppPackageName: "validAppPackageName",
			},
			expectedErrs: []servus.ErrorDetail{
				{ErrorCode: apiErr.ErrMissingIMEI.Code, Message: apiErr.ErrMissingIMEI.Message},
			},
		},
		{
			name: "Missing AppPackageName",
			deviceInfo: &api.DeviceInfo{
				AdjustId:       "validAdjustId",
				Imei:           "validImei",
				AppPackageName: "",
			},
			expectedErrs: []servus.ErrorDetail{
				{ErrorCode: apiErr.ErrMissingAppPackageName.Code, Message: apiErr.ErrMissingAppPackageName.Message},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errs := ValidateExtraDeviceInfo(tt.deviceInfo)
			assert.Equal(t, tt.expectedErrs, errs)
		})
	}
}

func TestValidatePartnerPayloadInfo(t *testing.T) {
	tests := []struct {
		name           string
		partnerPayload *api.PartnerPayload
		expectedErrs   []servus.ErrorDetail
	}{
		{
			name: "Valid PartnerPayload",
			partnerPayload: &api.PartnerPayload{
				ProdId:                "validProdId",
				BizChannelId:          "validBizChannelId",
				OrderId:               1,
				FirstLoanIdx:          1,
				RequestedLoanAmount:   1000,
				Commission:            10,
				RequestedTenure:       "12 months",
				InterestRate:          5.5,
				DisbursementDate:      "2023-01-01",
				FirstDueDate:          "2023-02-01",
				RequestedCurrencyCode: "USD",
				DrawDownAccount:       "validAccount",
				DrawDownCcyCode:       "USD",
				BankCode:              "validBankCode",
				AccountInfo: &api.AccountInfo{
					UserId:        1001,
					NumberOfChild: 2,
					CustRiskLevel: "custRiskLevel",
					WorkingMonths: "Jan",
					StayMonths:    "Jan",
					RegionCode:    "regionCode",
				},
				CreditInfo: &api.CreditInfo{
					PReviewResults:    1,
					RejectNodeId:      "validRejectNodeId",
					LtLevel2:          1,
					StLevel2:          1,
					LongTermUserType:  1.01,
					ShortTermUserType: 1.01,
					PProductCategory:  1,
				},
				ContractList: []api.ContractList{
					{
						ContractName: "validContract",
						ContractURL:  "validContractUrl",
					},
				},
			},
			expectedErrs: nil,
		},
		{
			name:           "Missing PartnerPayload",
			partnerPayload: nil,
			expectedErrs: []servus.ErrorDetail{
				{ErrorCode: apiErr.ErrMissingPartnerPayload.Code, Message: apiErr.ErrMissingPartnerPayload.Message},
			},
		},
		{
			name: "Missing ProdId",
			partnerPayload: &api.PartnerPayload{
				BizChannelId:          "validBizChannelId",
				OrderId:               1,
				FirstLoanIdx:          1,
				RequestedLoanAmount:   1000,
				Commission:            10,
				RequestedTenure:       "12 months",
				InterestRate:          5.5,
				DisbursementDate:      "2023-01-01",
				FirstDueDate:          "2023-02-01",
				RequestedCurrencyCode: "USD",
				DrawDownAccount:       "validAccount",
				DrawDownCcyCode:       "USD",
				BankCode:              "validBankCode",
				AccountInfo: &api.AccountInfo{
					UserId:        1001,
					NumberOfChild: 2,
					CustRiskLevel: "custRiskLevel",
					WorkingMonths: "Jan",
					StayMonths:    "Jan",
					RegionCode:    "regionCode",
				},
				CreditInfo: &api.CreditInfo{
					PReviewResults:    1,
					RejectNodeId:      "validRejectNodeId",
					LtLevel2:          1,
					StLevel2:          1,
					LongTermUserType:  1.01,
					ShortTermUserType: 1.01,
					PProductCategory:  1,
				},
				ContractList: []api.ContractList{
					{
						ContractName: "validContract",
						ContractURL:  "validContractUrl",
					},
				},
			},
			expectedErrs: []servus.ErrorDetail{
				{ErrorCode: apiErr.ErrMissingProdId.Code, Message: apiErr.ErrMissingProdId.Message},
			},
		},
		{
			name: "Missing BizChannelId",
			partnerPayload: &api.PartnerPayload{
				ProdId:                "validProdId",
				OrderId:               1,
				FirstLoanIdx:          1,
				RequestedLoanAmount:   1000,
				Commission:            10,
				RequestedTenure:       "12 months",
				InterestRate:          5.5,
				DisbursementDate:      "2023-01-01",
				FirstDueDate:          "2023-02-01",
				RequestedCurrencyCode: "USD",
				DrawDownAccount:       "validAccount",
				DrawDownCcyCode:       "USD",
				BankCode:              "validBankCode",
				AccountInfo: &api.AccountInfo{
					UserId:        1001,
					NumberOfChild: 2,
					CustRiskLevel: "custRiskLevel",
					WorkingMonths: "Jan",
					StayMonths:    "Jan",
					RegionCode:    "regionCode",
				},
				CreditInfo: &api.CreditInfo{
					PReviewResults:    1,
					RejectNodeId:      "validRejectNodeId",
					LtLevel2:          1,
					StLevel2:          1,
					LongTermUserType:  1.01,
					ShortTermUserType: 1.01,
					PProductCategory:  1,
				},
				ContractList: []api.ContractList{
					{
						ContractName: "validContract",
						ContractURL:  "validContractUrl",
					},
				},
			},
			expectedErrs: []servus.ErrorDetail{
				{ErrorCode: apiErr.ErrMissingBizChannelId.Code, Message: apiErr.ErrMissingBizChannelId.Message},
			},
		},
		{
			name: "Invalid OrderId",
			partnerPayload: &api.PartnerPayload{
				ProdId:                "validProdId",
				BizChannelId:          "validBizChannelId",
				FirstLoanIdx:          1,
				RequestedLoanAmount:   1000,
				Commission:            10,
				RequestedTenure:       "12 months",
				InterestRate:          5.5,
				DisbursementDate:      "2023-01-01",
				FirstDueDate:          "2023-02-01",
				RequestedCurrencyCode: "USD",
				DrawDownAccount:       "validAccount",
				DrawDownCcyCode:       "USD",
				BankCode:              "validBankCode",
				AccountInfo: &api.AccountInfo{
					UserId:        1001,
					NumberOfChild: 2,
					CustRiskLevel: "custRiskLevel",
					WorkingMonths: "Jan",
					StayMonths:    "Jan",
					RegionCode:    "regionCode",
				},
				CreditInfo: &api.CreditInfo{
					PReviewResults:    1,
					RejectNodeId:      "validRejectNodeId",
					LtLevel2:          1,
					StLevel2:          1,
					LongTermUserType:  1.01,
					ShortTermUserType: 1.01,
					PProductCategory:  1,
				},
				ContractList: []api.ContractList{
					{
						ContractName: "validContract",
						ContractURL:  "validContractUrl",
					},
				},
			},
			expectedErrs: []servus.ErrorDetail{
				{ErrorCode: apiErr.ErrInvalidOrderId.Code, Message: apiErr.ErrInvalidOrderId.Message},
			},
		},
		// Add more test cases for other fields as needed
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errs := ValidatePartnerPayloadInfo(tt.partnerPayload)
			assert.Equal(t, tt.expectedErrs, errs)
		})
	}
}
