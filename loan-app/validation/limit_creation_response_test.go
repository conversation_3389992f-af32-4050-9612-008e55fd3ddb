package validation

import (
	"testing"

	"gitlab.com/gx-regional/dakota/lending/loan-app/api"
)

func TestValidateLimitCreationResponse(t *testing.T) {
	type args struct {
		limitCreationResponse *api.LimitCreationResponse
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "should not return error when EcosystemResponse validation is success",
			args:    args{limitCreationResponse: &api.LimitCreationResponse{Status: "SUCCESS"}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			if err := ValidateLimitCreationResponse(test.args.limitCreationResponse); (err != nil) != test.wantErr {
				t.Errorf("ValidateLimitCreationResponse() = %v, want %v", err, test.wantErr)
			}
		})
	}
}
