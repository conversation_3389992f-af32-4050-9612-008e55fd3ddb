{"name": "loan-app Service", "serviceName": "loan-app", "env": "local", "host": "0.0.0.0", "port": 8009, "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "root:@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10, "connMaxLifetime": "1800s"}, "slave": {"dsn": "root:@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000, "commandGroup": "LoanApp", "tag": "DB_Master"}, "slaveCircuitBreaker": {"timeoutInMs": 1000, "commandGroup": "LoanApp", "tag": "DB_Slave"}}}, "statsd": {"host": "localhost", "port": 8125}, "trace": {"host": "localhost", "port": 8126}, "logger": {"syslogTag": "", "workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "cglsdebug", "development": true}, "productMasterConfig": {"serviceName": "product-master", "baseURL": "https://cb-debug.sgbank.dev/product-master", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "customerMasterConfig": {"serviceName": "customer-master", "baseURL": "https://debug.sgbank.dev/customer-master", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "accountServiceConfig": {"serviceName": "account-service", "baseURL": "https://cb-debug.sgbank.dev/account-service", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "perfiosAdapterConfig": {"baseURL": "https://cb-debug.sgbank.dev/perfios-adapter", "serviceName": "perfios-adapter", "circuitBreaker": {"timeout": 60000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false, "commandGroup": "LoanApp"}, "ekybServiceConfig": {"serviceName": "ekyb-service", "baseURL": "https://backend.dev.g-bank.app/ekyb-service", "circuitBreaker": {"timeout": 1000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false, "commandGroup": "LoanApp"}, "mlScoringConfig": {"baseURL": "http://flexicard-ascore-model.sgbank.st"}, "bizMlScoringConfig": {"baseURL": "https://msme-ascore-model.sgbank.st", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false, "commandGroup": "LoanApp"}, "creditBureauServiceConfig": {"serviceName": "credit-bureau", "baseURL": "https://cb-debug.sgbank.dev/credit-bureau", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "creditDecisionEngineConfig": {"serviceName": "cr-decision-eng", "baseURL": "https://cb-debug.sgbank.dev/cr-decision-eng", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "loanCoreConfig": {"serviceName": "loan-core", "baseURL": "https://cb-debug.sgbank.dev/loan-core", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "digicardCoreConfig": {"serviceName": "digicard-core", "baseURL": "https://debug.sgbank.dev/digicard-core", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "whitelistServiceConfig": {"serviceName": "whitelist-service", "baseURL": "https://debug.sgbank.dev/whitelist-service", "circuitBreaker": {"timeout": 15000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false, "commandGroup": "LoanApp"}, "applicationServiceConfig": {"serviceName": "application-service", "baseURL": "https://debug.sgbank.dev/application-service", "circuitBreaker": {"timeout": 15000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "experianAdapterConfig": {"serviceName": "experian-adapter", "baseURL": "https://debug.sgbank.dev/mock-service/experian-adapter", "circuitBreaker": {"timeout": 15000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "transactionStatementConfig": {"baseURL": "https://debug.sgbank.dev/transaction-statements", "serviceName": "transaction-statements", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "grabConfig": {"baseURL": "https://partner-api.stg-myteksi.com", "circuitConfig": {"grab": {"timeout": 15000, "max_concurrent_requests": 1000}}, "oAuthCredentials": {"client_id": "5cdfb4ebef3d47aba1f6687af90e921b", "client_secret": "FVn_o7RljV0rz7EH", "grant_type": "client_credentials", "scope": "lending.user_loan_meta"}, "hashConfig": {"saltHex": "0fbb95decfe4169a2ce6db458decdf9e", "iterations": 200000, "hashLength": 32}, "grabOAuthConfig": {"grabFetchWhiteListDetails": {"client_id": "5cdfb4ebef3d47aba1f6687af90e921b", "client_secret": "FVn_o7RljV0rz7EH", "grant_type": "client_credentials", "scope": "lending.user_loan_meta"}, "grabFetchUserProfileDetails": {"clientID": "dc75437d117141638c2fda3a7dbc5f31", "clientSecret": "CW02-TBclskj5t44", "grantType": "client_credentials", "scope": "lending.user_profile_details", "ttlForBearerTokenInRedisInSec": 518000}, "grabFetchLendingMexDetails": {"clientID": "dc75437d117141638c2fda3a7dbc5f31", "clientSecret": "CW02-TBclskj5t44", "grantType": "client_credentials", "scope": "bank.partner_api", "ttlForBearerTokenInRedisInSec": 518000}}, "hashingConfig": {"grabFetchUserProfileDetails": {"salt": "{{ grab_ecosystem_salt }}", "iterations": 205000, "derivedKeyLength": 40}}}, "datalakeConfig": {"hostAddress": "https://data-api.dev.idbank-int.net", "circuitConfig": {"datalake": {"timeout": 15000}}, "getApplicantLoanPortfolioPath": "/v1/loan_portfolios?nik_hashed=eq.%s"}, "appianConfig": {"hostAddress": "https://digibankdev.appiancloud.com", "clientID": "loan-app", "circuitConfig": {"appian": {"timeout": 15000}}, "registeredClientID": "{{APPIAN_CLIENT_ID}}", "registeredClientSecret": "{{APPIAN_CLIENT_SECRET}}", "grantType": "client_credentials", "ttlForBearerTokenInRedisInSec": 86400}, "hedwig": {"clientConfig": {"hostAddress": "https://debug.sgbank.dev/hedwig", "pushServerPath": "/hedwig/v1/push", "pushInboxServerPath": "/hedwig/v1/pushInbox", "emailServerPath": "/hedwig/v1/email", "circuitConfig": {"hedwig": {"timeout": 15000}}, "serviceName": "", "serviceKey": ""}, "pushNotificationTemplateMappings": {"bizLOCAccountSuccessPushNotificationTemplate": "3eaf0025-00f2-4b74-90ec-fc1ac85a4cde", "loanAccountCreationSuccessfulPushNotificationTemplate": "cf79f1ca-087a-4b01-931b-f045f0e710d5", "loanAccountHardRejectPushNotificationTemplate": "cb36b3c1-22df-403f-9883-04c17ced1de7", "loanAccountSoftRejectPushNotificationTemplate": "cc4c488f-16d5-44fd-b613-3ec1d8edb33e", "loanAccountFailedPushNotificationTemplate": "6e1d71a0-6c77-4a24-9261-863251aabf1b"}, "emailNotificationTemplateMappings": {"bizLOCAccountSuccessEmailTemplate": "0f043ea2-2670-4238-a3c1-4cc43fa3de97", "loanAccountCreationSuccessfulEmailTemplate": "8fe26814-23ec-4460-8154-799c6924c560", "loanAccountCreationBalanceTransferEmailTemplate": "8d27c520-bda9-4a41-87d2-fcfcfbcafbae"}, "smsNotificationTemplateMappings": {}}, "pigeonConfig": {"baseURL": "https://debug.sgbank.dev/pigeon", "smsNotificationTemplateMappings": {"loanAccountHardRejectSMSNotificationTemplate": "loan_application_hard_reject_sms"}, "emailNotificationTemplateMappings": {"loanAccountCreationSuccessfulEmailTemplate": "loan_account_creation_successful", "loanAccountCreationBalanceTransferEmailTemplate": "loan_account_creation_successful"}, "circuitConfig": {"pigeon": {"timeout": 15000}}}, "loanAppConfig": {"clientConfig": {"maxIdleConnsPerHost": 64, "idleConnTimeoutInSMillis": 10000, "timeoutInMillis": 60000, "requestLogLockTimeoutInMillis": 5000}}, "redisConfig": {"addr": "localhost:30001", "command_group": "LoanApp", "command_name": "redis_LoanApp", "error_percent_threshold": 20, "idleTimeoutInSec": 300, "max_concurrent_request": 1000, "password": "", "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "sleep_window": 5000, "timeout": 200, "tlsEnabled": false, "writeTimeoutInSec": 1}, "loanAppKafkaConfig": {"brokers": ["b-1.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "stream": "dev-loan-app-lifecycle-event", "clusterType": "critical", "enableTLL": true, "offsetType": "oldest", "clientID": "loan-app-local", "packageName": "pb", "dtoName": "LoanAppLifecycleEvent", "syncprod": true, "requiredAcks": -1}, "applicationStatusTransitionKafkaConfig": {"brokers": ["b-1.dev-backend-commo.ctk7qm.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dev-backend-commo.ctk7qm.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dev-backend-commo.ctk7qm.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-app-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-application-status-update-transition", "packageName": "pb", "dtoName": "ApplicationStatusTransition", "offsetType": "oldest", "enable": true, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "creditBureauEnquiryKafkaConfig": {"brokers": ["b-1.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-app-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-credit-bureau-event", "packageName": "pb", "dtoName": "CreditBureauEvent", "offsetType": "oldest", "enable": true, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "creditCardAccountCreationKafkaConfig": {"brokers": ["b-1.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-app-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-credit-card-account-creation-event", "packageName": "pb", "dtoName": "CreditCardAccountCreationEvent", "offsetType": "oldest", "enable": true, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "locAccountCreationEventKafkaConfig": {"brokers": ["b-1.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-app-local", "stream": "dev-loc-account-creation-event", "clusterType": "critical", "enableTLL": true, "packageName": "pb", "offsetType": "oldest", "dtoName": "LOCAccountCreationEvent", "enable": true, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "cronJobConfig": {"pendingDocExpiryInDays": "30"}, "featureFlags": {"appianFlag": true, "previousApplicationFlag": true, "whiteListingCheckFlag": true, "coolingPeriodCheckFlag": true, "applicantCurrentApplicationCheckFlag": true, "updateOfferDetailsNewImpl": true, "enableFlexiTermLoanApplicationWorkflow": false, "businessValidationFlag": false, "enableFlexiCardFlag": true, "enableBalanceTransfer": true, "addWhitelistFlagToApplicantFlag": false, "enableBizFlexiCredit": true, "enableBizBankStatementsUpload": true, "enableBIFForNotification": true, "enableRetryableStream": false, "enableGetECSRReportByID": true, "enablePrivateLimited": true, "enableActiveProfile": false, "enablePersonalBankStatementsUpload": true, "encryptionFeatureFlags": {"enableKMSClient": true, "flexiCardApplicationWorkflow": {"enableIDNumberEncryption": true, "enableSwitchToHashedIDNumber": false, "enableIDNumberHashShadow": false}, "bundledApplicationWorkflow": {"enableIDNumberEncryption": true, "enableSwitchToHashedIDNumber": false, "enableIDNumberHashShadow": false}, "appianApplication": {"enableIDNumberEncryption": true, "enableSwitchToHashedIDNumber": false, "enableIDNumberHashShadow": false}, "bizLoanApplication": {"enableIDNumberEncryption": false, "enableSwitchToHashedIDNumber": false, "enableIDNumberHashShadow": false}, "updateLoanOfferWorkflow": {"enableIDNumberEncryption": true, "enableSwitchToHashedIDNumber": false, "enableIDNumberHashShadow": false}, "getLoanApplicationDetailsByIdentifier": {"enableIDNumberEncryption": false, "enableSwitchToHashedIDNumber": false, "enableIDNumberHashShadow": false}}}, "localeConfig": {"defaultTimeZoneOffset": 28800, "defaultCountryCode": "SG", "defaultCurrencyCode": "SGD", "defaultTimeZone": "Asia/Singapore"}, "dynamicConstants": {"uploadDocumentApiTimeoutInSecs": 60}, "bizCampaignConfig": {"enable": false, "interestRate": 4.99}, "coolingPeriod": {"AB001": 55000, "AB002": 55000, "AB003": 55000, "AB004": 55000, "AV001": 0, "BF001": 55000, "BF002": 30, "BF003": 5, "BN001": 55000, "BN002": 55000, "BN003": 55000, "BO900": 30, "BO999": 0, "CE001": 55000, "CE002": 30, "CE003": 90, "CE004": 30, "CE005": 30, "CE006": 30, "CE007": 30, "CE008": 30, "CE009": 30, "CE010": 30, "CH001": 90, "CH002": 90, "CO900": 30, "CO999": 0, "CU001": 30, "CU002": 90, "CU010": 90, "CU101": 90, "CU102": 90, "DF001": 55000, "DF002": 30, "DF003": 5, "DF900": 30, "DF999": 0, "DV001": 30, "EC001": 90, "EC002": 90, "EC003": 90, "EC004": 90, "EE001": 90, "EE002": 90, "EE003": 90, "EE004": 90, "EE005": 90, "EH001": 90, "EH002": 90, "EH003": 90, "EN001": 55000, "EN002": 55000, "EO001": 0, "EO900": 30, "EO999": 0, "EU001": 90, "EU002": 90, "EU003": 90, "EU004": 30, "EV001": 90, "I001": 0, "I002": 0, "CE011": 30}, "statusReasonToPercentageMapping": {}, "workflowRetryConfig": {"updateLoanOfferDetails": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "createFlexiTermLoanApplication": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 7200, "maxAttempt": 50}}, "createFlexiCardApplication": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "bundledApplication": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "createBizFlexiCreditApplication": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 7200, "maxAttempt": 50}}, "mlDecisionScore": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "updateFinexus": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}}, "fileTypeConfig": {"sourceOfIncome": {"maxFileSizeInBytes": "3145728", "allowedExtensions": ["PDF"]}}, "createApplicationConfig": {"experianDelay": 10}, "mlopsConfig": {"iamHostAddress": "https://mlops-api.dev.idbank-int.net", "dmHostAddress": "https://mlops-api.dev.idbank-int.net", "circuitConfig": {"mlops-adapter": {"timeout": 15000}}, "registeredClientID": "{{MLOPS_CLIENT_ID}}", "registeredSecret": "{{MLOPS_SECRET_ID}}", "ttlForBearerTokenInRedisInSec": 270, "endpoints": {"authTokenPath": "/v1/token", "creditDecisionPath": "/v1/predictions/los"}}, "gdProxyServiceConfig": {"serviceName": "gd-proxy", "baseURL": "https://backend.dev.g-bank.app/gd-proxy", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false, "commandGroup": "LoanApp"}, "riskBrokerServiceConfig": {"serviceName": "risk-broker", "baseURL": "https://backend.dev.g-bank.app/risk-broker", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false, "commandGroup": "LoanApp"}, "incomeDerivationEventKafkaConfig": {"brokers": ["b-1.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-app-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-income-derivation-event", "packageName": "pb", "dtoName": "IncomeDerivationEvent", "offsetType": "oldest", "enable": false, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "sqsConfig": {"queueURL": "http://sqs.us-east-1.localhost.localstack.cloud:4566/************/loan-app-sqs", "awsRegion": "ap-southeast-1", "exponentialBackoffBaseIntervalSeconds": 1, "waitTimeSeconds": 5}, "lendingNameScreeningEventKafkaConfig": {"brokers": ["b-1.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-app-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-lending-name-screening", "packageName": "pb", "dtoName": "LendingNameScreening", "offsetType": "oldest", "enable": false, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "ecddVerdictEventKafkaConfig": {"brokers": ["b-1.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-app-local", "clusterType": "critical", "enableTLL": true, "stream": "TBA", "packageName": "pb", "dtoName": "TBA", "offsetType": "oldest", "enable": false, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "finexusUpdateKafkaConfig": {"brokers": ["b-1.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-app-local", "stream": "dev-finexus-update", "clusterType": "critical", "enableTLL": true, "packageName": "pb", "offsetType": "oldest", "dtoName": "LendingFinexusUpdate", "enable": false, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "cacheTimeoutConfig": {"listLoanDocumentTypesTTLInMinutes": 120}, "kmsEncryptionConfig": {"keyId": "{{ KMS_KEY_ID }}", "dataEncryptionKey": "{{DATA_ENCRYPTION_KEY}}", "hashKey": "{{<PERSON><PERSON><PERSON>_KEY}}"}, "backfillEncryptedAndHashedID": {"delayInSecs": 180, "startImmediately": true, "logTag": "backfillEncryptedAndHashedIDWorkerTag", "name": "backfillEncryptedAndHashedIDWorker", "lockKey": "backfillEncryptedAndHashedIDWorkerLock", "lockDurationInSecs": 1800, "enabled": true, "batchSize": 100, "batchProcessingIntervalInSecs": 1, "startID": 0, "endID": 175447, "checkpointKeyPostfix": "2", "checkpointExpiryHours": 168}, "lendingAccountCreationKafkaConfig": {"brokers": ["b-1.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-app-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-lending-account-creation-event", "packageName": "pb", "dtoName": "LendingAccountCreationEvent", "offsetType": "oldest", "enable": true, "maxRetryCount": 5, "delayInMilliSeconds": 200, "requiredAcks": -1}}