{"name": "loan-app Service", "serviceName": "loan-app", "host": "0.0.0.0", "port": 8001, "timeoutInMs": 60000, "env": "dev", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "root:@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "18000000s"}, "slave": {"dsn": "root:@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800000s"}, "masterCircuitBreaker": {"timeoutInMs": *********, "commandGroup": "LoanApp", "tag": "DB_Master"}, "slaveCircuitBreaker": {"timeoutInMs": *********, "commandGroup": "LoanApp", "tag": "DB_Slave"}}}, "statsd": {"host": "localhost", "port": 8125}, "trace": {"host": "localhost", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 3, "logFormat": "json", "development": true}, "productMasterConfig": {"serviceName": "product-master", "baseURL": "https://backend.dev.g-bank.app/product-master", "circuitBreaker": {"timeout": 5000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "customerMasterConfig": {"serviceName": "customer-master", "baseURL": "https://backend.dev.g-bank.app/customer-master", "circuitBreaker": {"timeout": 5000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "accountServiceConfig": {"serviceName": "account-service", "baseURL": "https://backend.dev.g-bank.app/cr-decision-eng", "circuitBreaker": {"timeout": 5000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "perfiosAdapterConfig": {"baseURL": "http://perfios-adapter.lending-platform.svc.cluster.local", "serviceName": "perfios-adapter", "circuitBreaker": {"timeout": 60000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "ekybServiceConfig": {"serviceName": "ekyb-service", "baseURL": "https://backend.dev.g-bank.app/ekyb-service", "circuitBreaker": {"timeout": 1000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "transactionStatementConfig": {"baseURL": "https://debug.sgbank.dev/transaction-statements", "serviceName": "transaction-statements", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "loanAppConfig": {"clientConfig": {"maxIdleConnsPerHost": 64, "idleConnTimeoutInSMillis": 10000, "timeoutInMillis": 60000, "requestLogLockTimeoutInMillis": 5000}}, "loanAppKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "stream": "dev-loan-app-lifecycle-event", "clusterType": "critical", "enableTLL": true, "initOffset": "oldest", "clientID": "loan-app-kp-local", "packageName": "pb", "dtoName": "LoanAppLifecycleEvent"}, "redisConfig": {"addr": "clustercfg.dbmy-dev-backend-ec-loan-app.o9f56r.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "test", "tlsEnabled": true}, "datalakeConfig": {"hostAddress": "https://data-api.dev.gxbank-int.net/", "circuitConfig": {"datalake": {"timeout": 5000}}, "getApplicantLoanPortfolioPath": "/v1/loan_portfolios?nik_hashed=eq.%s"}, "grabConfig": {"baseURL": "https://partner-api.stg-myteksi.com", "circuitConfig": {"grab": {"timeout": 15000, "max_concurrent_requests": 1000}}, "oAuthCredentials": {"client_id": "clientID", "client_secret": "clientSecret", "grant_type": "some_credentials", "scope": "lending.some.scope"}, "hashConfig": {"saltHex": "salt is salty", "iterations": 200000, "hashLength": 32}, "grabOAuthConfig": {"grabFetchWhiteListDetails": {"client_id": "clientID", "client_secret": "clientSecret", "grant_type": "some_credentials", "scope": "lending.some.scope"}, "grabFetchUserProfileDetails": {"clientID": "dummy_value", "clientSecret": "dummy_value", "grantType": "client_credentials", "scope": "lending.user_profile_details", "ttlForBearerTokenInRedisInSec": 800}, "grabFetchLendingMexDetails": {"clientID": "{{GRAB_CLIENT_ID}}", "clientSecret": "{{GRAB_CLIENT_SECRET}}", "grantType": "client_credentials", "scope": "bank.partner_api", "ttlForBearerTokenInRedisInSec": 518000}}}, "appianConfig": {"hostAddress": "https://dbmydev.gforce.g-bank.app", "clientID": "loan-app", "circuitConfig": {"appian": {"timeout": 20000}}, "registeredClientID": "{{APPIAN_CLIENT_ID}}", "registeredClientSecret": "{{APPIAN_CLIENT_SECRET}}", "grantType": "client_credentials", "ttlForBearerTokenInRedisInSec": 800}, "hedwig": {"clientConfig": {"hostAddress": "http://hedwig.backend-rtc-app-01.svc.cluster.local", "pushServerPath": "/hedwig/v1/push", "pushInboxServerPath": "/hedwig/v1/pushInbox", "circuitConfig": {"hedwig": {"timeoutInMs": 5000}}, "serviceName": "", "serviceKey": ""}, "pushNotificationTemplateMappings": {}, "emailNotificationTemplateMappings": {}, "smsNotificationTemplateMappings": {}}, "cronJobConfig": {"_comment": "NOTE: Change below configs when we move to PROD", "expiryInDays": "30", "pendingDocExpiryInDays": "30"}, "featureFlags": {"appianFlag": true, "previousApplicationFlag": false, "updateOfferDetailsNewImpl": true, "enableFlexiCreditEligibilityWithDepositDataFlag": true, "enableFlexiCreditEligibilityWithCustomerDataFlag": true, "enableFlexiCreditPreselectFlag": true, "applicantCurrentApplicationCheckFlag": true, "coolingPeriodCheckFlag": true, "enableFlexiCreditExpireNotificaiton": true, "enableBizFlexiCredit": true, "enableBizBankStatementsUpload": true, "enableFlexiTermLoanApplicationWorkflow": true, "whiteListingCheckFlag": false, "businessValidationFlag": false, "enableFlexiCardFlag": true, "enableBIFForNotification": true, "enableBypassIncomeDerivationIEM": true, "enableUtilisationTracker": true, "enableGetECSRReportByID": false, "enablePrivateLimited": false, "enableActiveProfile": false, "enablePersonalBankStatementsUpload": true, "enableBizFlexiCreditCdePostIncomeApplicantIncome": true, "encryptionFeatureFlags": {"enableKMSClient": false, "flexiCardApplicationWorkflow": {"enableIDNumberEncryption": false, "enableSwitchToHashedIDNumber": false, "enableIDNumberHashShadow": false}, "bundledApplicationWorkflow": {"enableIDNumberEncryption": false, "enableSwitchToHashedIDNumber": false, "enableIDNumberHashShadow": false}, "appianApplication": {"enableIDNumberEncryption": false, "enableSwitchToHashedIDNumber": false, "enableIDNumberHashShadow": false}, "bizLoanApplication": {"enableIDNumberEncryption": false, "enableSwitchToHashedIDNumber": false, "enableIDNumberHashShadow": false}, "updateLoanOfferWorkflow": {"enableIDNumberEncryption": false, "enableSwitchToHashedIDNumber": false, "enableIDNumberHashShadow": false}, "getLoanApplicationDetailsByIdentifier": {"enableIDNumberEncryption": false, "enableSwitchToHashedIDNumber": false, "enableIDNumberHashShadow": false}}}, "pigeonConfig": {"baseURL": "http://pigeon.pigeon.svc.cluster.local", "smsNotificationTemplateMappings": {"loanAccountHardRejectSMSNotificationTemplate": "loan_application_hard_reject_sms"}, "emailNotificationTemplateMappings": {"bizLOCAccountSuccessEmailTemplate": "biz_loc_account_creation_successful_email", "loanApplicationPendingAcceptanceTemplate": "application_pending_acceptance_email", "loanAccountSoftRejectionEmailTemplate": "application_soft_reject_email", "loanAccountIncomeDocumentRequiredTemplate": "application_income_document_required_email", "loanApplicationExpiredTemplate": "loan_application_expired_email"}, "pushNotificationTemplateMappings": {"bizLOCAccountSuccessPushNotificationTemplate": "biz_loc_account_creation_successful_push", "loanAccountCreationSuccessfulPushNotificationTemplate": "loc_account_creation_successful_push", "loanApplicationPendingAcceptanceTemplate": "application_pending_acceptance_push", "loanAccountHardRejectPushNotificationTemplate": "application_hard_reject_push", "loanAccountSoftRejectPushNotificationTemplate": "application_soft_reject_push", "loanAccountFailedPushNotificationTemplate": "application_failed_push", "loanAccountIncomeDocumentRequiredTemplate": "application_income_document_required_push", "loanApplicationExpiredTemplate": "loan_application_expired_push"}}, "mlScoringConfig": {"baseURL": "http://flexicard-ascore-model.sgbank.st", "withHealthCheck": false, "commandGroup": "LoanApp"}, "bizMlScoringConfig": {"baseURL": "https://msme-ascore-model.sgbank.st", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false, "commandGroup": "LoanApp"}, "creditBureauServiceConfig": {"serviceName": "credit-bureau", "baseURL": "https://backend.dev.g-bank.app/credit-bureau", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false, "commandGroup": "LoanApp"}, "creditDecisionEngineConfig": {"serviceName": "cr-decision-eng", "baseURL": "https://backend.dev.g-bank.app/cr-decision-eng", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false, "commandGroup": "LoanApp"}, "digicardCoreConfig": {"serviceName": "digicard-core", "baseURL": "https://debug.sgbank.dev/digicard-core", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false, "commandGroup": "LoanApp"}, "whitelistServiceConfig": {"serviceName": "whitelist-service", "baseURL": "http://whitelist-service.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 15000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false, "commandGroup": "LoanApp"}, "applicationServiceConfig": {"serviceName": "application-service", "baseURL": "https://backend.dev.g-bank.app/application-service", "circuitBreaker": {"timeout": 15000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "LoanApp"}, "experianAdapterConfig": {"serviceName": "experian-adapter", "baseURL": "http://mock-service.backend-dakota-app-01.svc.cluster.local/experian-adapter", "circuitBreaker": {"timeout": 15000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false, "commandGroup": "LoanApp"}, "dynamicConstants": {"defaultTimeZoneOffset": 28800, "defaultCountryCode": "MY", "defaultCurrencyCode": "MYR", "uploadDocumentApiTimeoutInSecs": 60, "mockGetDocumentTypesResponse": "{\n    \"documentSubmissionOptions\": [\n        {\n            \"docType\": \"epfStatement\",\n            \"applicationType\": \"NEW\",\n            \"salaryType\": \"SA\",\n            \"isEnable\": true,\n            \"priority\": 1,\n            \"requiredDocNumber\": 2,\n            \"requiredDocUnit\": \"year\",\n            \"fileConfig\": {\n                \"maxFileSizeInBytes\": 3145728,\n                \"maxUploadLimit\": 12,\n                \"allowedFileExtensions\": [\n                    \"PDF\"\n                ]\n            }\n        },\n        {\n            \"docType\": \"personalBankStatement\",\n            \"applicationType\": \"NEW\",\n            \"salaryType\": \"SA\",\n            \"isEnable\": true,\n            \"priority\": 2,\n            \"requiredDocNumber\": 2,\n            \"requiredDocUnit\": \"year\",\n            \"fileConfig\": {\n                \"maxFileSizeInBytes\": 3145728,\n                \"maxUploadLimit\": 12,\n                \"allowedFileExtensions\": [\n                    \"PDF\"\n                ]\n            }\n        },\n        {\n            \"docType\": \"businessBankStatement\",\n            \"applicationType\": \"REVIEW_REVIEW_LIMIT\",\n            \"salaryType\": \"SE\",\n            \"isEnable\": true,\n            \"priority\": 2,\n            \"requiredDocNumber\": 2,\n            \"requiredDocUnit\": \"year\",\n            \"fileConfig\": {\n                \"maxFileSizeInBytes\": 3145728,\n                \"maxUploadLimit\": 12,\n                \"allowedFileExtensions\": [\n                    \"PDF\"\n                ]\n            }\n        },\n        {\n            \"docType\": \"personalBankStatement\",\n            \"applicationType\": \"REVIEW_REVIEW_LIMIT\",\n            \"salaryType\": \"SE\",\n            \"isEnable\": true,\n            \"priority\": 1,\n            \"requiredDocNumber\": 2,\n            \"requiredDocUnit\": \"year\",\n            \"fileConfig\": {\n                \"maxFileSizeInBytes\": 3145728,\n                \"maxUploadLimit\": 12,\n                \"allowedFileExtensions\": [\n                    \"PDF\"\n                ]\n            }\n        }\n    ]\n}"}, "localeConfig": {"defaultCountryCode": "MY", "defaultCurrencyCode": "MYR", "defaultTimeZoneOffset": 28800, "defaultTimeZone": "Asia/Kuala_Lumpur"}, "creditBureauEnquiryKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-app-kp-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-credit-bureau-event", "packageName": "pb", "dtoName": "CreditBureauEvent", "offsetType": "oldest", "enable": true, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "creditCardAccountCreationKafkaConfig": {"brokers": [], "clientID": "loan-app-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-credit-card-account-creation-event", "packageName": "pb", "dtoName": "CreditCardAccountCreationEvent", "offsetType": "oldest", "enable": false, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "locAccountCreationEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-app-kp-local", "stream": "dev-loc-account-creation-event", "clusterType": "critical", "enableTLL": true, "packageName": "pb", "offsetType": "oldest", "dtoName": "LOCAccountCreationEvent", "enable": true, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "applicationStatusTransitionKafkaConfig": {"brokers": [], "clientID": "loan-app-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-application-status-update-transition", "packageName": "pb", "dtoName": "ApplicationStatusTransition", "offsetType": "oldest", "enable": false, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "statusReasonToPercentageMapping": {"LOAN_APPLICATION_CREATED": 20, "HYGIENE_CHECK_APPROVED": 40, "FRAUD_PRE_BUREAU_APPROVED": 60, "CDE_PRE_BUREAU_APPROVED": 70, "FRAUD_POST_BUREAU_APPROVED": 80, "CDE_POST_BUREAU_APPROVED": 80, "LOAN_APPLICATION_APPROVED": 100, "defaultTimeZoneOffset": 28800}, "workflowRetryConfig": {"updateLoanOfferDetails": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "createFlexiTermLoanApplication": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 7200, "maxAttempt": 50}}, "createFlexiCardApplication": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "createBizFlexiCreditApplication": {"auxiliary": {"intervalInSeconds": 7200, "maxAttempt": 50}, "transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "mlDecisionScore": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "bundledApplication": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "updateFinexus": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}}, "mlopsConfig": {"circuitConfig": {"mlops-adapter": {"timeout": 15000}}, "dmHostAddress": "https://mlops-api.dev.idbank-int.net", "endpoints": {"authTokenPath": "/v1/token", "creditDecisionPath": "/v1/predictions/los"}, "iamHostAddress": "https://mlops-api.dev.idbank-int.net", "registeredClientID": "{{MLOPS_CLIENT_ID}}", "registeredSecret": "{{MLOPS_SECRET_ID}}", "ttlForBearerTokenInRedisInSec": 270}, "createApplicationConfig": {"experianDelay": 10}, "fileTypeConfig": {"sourceOfIncome": {"maxFileSizeInBytes": "3145728", "allowedExtensions": ["PDF"]}}, "coolingPeriod": {"R01": 55000, "R02": 55000, "R03": 180, "R04": 180, "R05": 30, "R06": 55000, "R07": 55000, "R08": 30, "R09": 30, "R10": 30, "R11": 55000, "R12": 55000, "R13": 30, "R14": 30, "R15": 30, "R16": 30, "R17": 30, "R18": 180, "KO001": 30, "KO002": 90, "KO003": 55000, "KO004": 55000, "KO005": 55000, "KO006": 90, "KO007": 55000, "KO008": 180, "KO009": 30, "KO010": 30, "KO011": 30, "KO012": 30, "KO013": 30, "KO014": 55000, "KO015": 30, "KO016": 180, "KO017": 180, "KO018": 30, "KO019": 30, "PR001": 55000, "PR002": 55000, "PR003": 55000, "PR004": 55000, "R34": 180, "R99": 55000, "R101": 30, "R102": 55000, "FL001": 0, "KO020": 30, "KO021": 55000, "KO022": 30, "KO023": 30, "KO024": 90}, "gdProxyServiceConfig": {"serviceName": "gd-proxy", "baseURL": "https://backend.dev.g-bank.app/gd-proxy", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false, "commandGroup": "LoanApp"}, "riskBrokerServiceConfig": {"serviceName": "risk-broker", "baseURL": "https://backend.dev.g-bank.app/risk-broker", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false, "commandGroup": "LoanApp"}, "incomeDerivationEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-app-kp-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-income-derivation-event", "packageName": "pb", "dtoName": "IncomeDerivationEvent", "offsetType": "oldest", "enable": true, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "ecddVerdictEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-app-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-ecdd-verdict-event", "packageName": "pb", "dtoName": "EcddVerdictEvent", "offsetType": "oldest", "enable": true, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "lendingNameScreeningEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-app-kp-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-lending-name-screening", "packageName": "pb", "dtoName": "LendingNameScreening", "offsetType": "oldest", "enable": true, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "finexusUpdateKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-app-kp-local", "stream": "dev-finexus-update", "clusterType": "critical", "enableTLL": true, "packageName": "pb", "offsetType": "oldest", "dtoName": "LendingFinexusUpdate", "enable": true, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "eventTrackerConfig": {"eventTypeToConfigMap": {"IEM": {"programName": "IEM", "supportedTimeUnits": ["global", "day"], "utilizationTypeToEventConfigMap": {"INCR": {"timeUnit": ["global", "day"], "lockDurationInMilliSec": 5500, "maxLimit": 2000000}, "DECR": {"timeUnit": ["global", "day"], "lockDurationInMilliSec": 5500, "maxLimit": 2000000}}}}}, "customerExperienceConfig": {"serviceName": "customer-experience", "baseURL": "https://backend.dev.g-bank.app/customer-experience", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false}, "cacheTimeoutConfig": {"listLoanDocumentTypesTTLInMinutes": 120}, "kmsEncryptionConfig": {"keyId": "{{ KMS_KEY_ID }}", "dataEncryptionKey": "{{DATA_ENCRYPTION_KEY}}", "hashKey": "{{<PERSON><PERSON><PERSON>_KEY}}"}, "backfillEncryptedAndHashedID": {"delayInSecs": 180, "startImmediately": true, "logTag": "backfillEncryptedAndHashedIDWorkerTag", "name": "backfillEncryptedAndHashedIDWorker", "lockKey": "backfillEncryptedAndHashedIDWorkerLock", "lockDurationInSecs": 1800, "enabled": true, "batchSize": 100, "batchProcessingIntervalInSecs": 1, "startID": 0, "endID": 175447, "checkpointKeyPostfix": "2", "checkpointExpiryHours": 168}}